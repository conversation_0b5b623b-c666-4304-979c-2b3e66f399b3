import React, { FC, useRef, useState, useEffect, useCallback } from "react";
import styles from "./index.module.scss";
import { LeftOutlined, RightOutlined, HeartOutlined, HeartFilled, CheckOutlined, CaretRightFilled, MoreOutlined } from "@ant-design/icons";
import { Popover, message } from "antd";
import { modalShow } from "@/components/List";
import MatchCorrection from "@/pages/FATWall/FATWall_PC/All/MatchCorrection";
import { px2rem } from "@/utils/setRootFontSize";
import TVFocusable from "../../../pages/FATWall/FATWall_TV/TVFocus";
import { useRequest } from "ahooks";
import { fileDelete, move2trashbin, getFilePath } from "@/api/fatWall";

// 剧集数据类型定义
export interface Episode {
  id: string;
  title: string;
  thumbnail: string;
  episodeNumber: number;
  duration?: string;
  watched?: boolean;
  progress?: number; // 播放进度百分比 0-100
  favorite?: boolean; // 是否收藏
  file_id?: number; // 文件ID
  lib_id?: number; // 库ID
  file_media_id?: number
}

// 剧集列表组件
export interface EpisodeListProps {
  episodes: Episode[];
  currentEpisodeId?: string;
  onEpisodeSelect: (episode: Episode) => void;
  onToggleFavorite?: (episode: Episode) => void; // 切换收藏状态
  onToggleWatched?: (episode: Episode) => void; // 切换观看状态
  title?: string;
  isTv?: boolean;
  refreshList?: () => void; // 添加刷新列表的回调函数
  baseRow?: number; // TV端基础行坐标，用于动态计算TVFocusable的row值
}

const EpisodeList: FC<EpisodeListProps> = ({
  episodes,
  currentEpisodeId,
  onEpisodeSelect,
  onToggleFavorite,
  onToggleWatched,
  title = "剧集列表",
  isTv = false,
  refreshList,
  baseRow = 1, // 默认基础行坐标为1
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [hoveredEpisodeId, setHoveredEpisodeId] = useState<string | null>(null);
  const [morePopoverVisible, setMorePopoverVisible] = useState(false);
  const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);
  const [currentEpisode, setCurrentEpisode] = useState<Episode | null>(null);


  // 监听滚动事件以显示/隐藏导航箭头
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    };

    // 初始检查
    handleScroll();

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, [episodes]);

  // 水平滚动处理函数
  const handleScroll = (direction: 'left' | 'right') => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const scrollAmount = 300; // 每次滚动的像素数
    const currentScroll = scrollContainer.scrollLeft;
    const targetScroll = direction === 'left'
      ? currentScroll - scrollAmount
      : currentScroll + scrollAmount;

    scrollContainer.scrollTo({
      left: targetScroll,
      behavior: 'smooth'
    });
  };

  // 文件删除
  const { run: runFileDelete } = useRequest(
    fileDelete,
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          message.success('已从媒体库移除');
          if (refreshList) {
            refreshList();
          }
        } else {
          message.error('删除失败，请重试');
        }
      },
      onError: () => {
        message.error('删除失败，请重试');
      },
    }
  );

  // 获取文件路径
  const { run: runGetFilePath } = useRequest(
    getFilePath,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data && res.data.path) {
          // 获取到文件路径后，调用移动到回收站接口
          runMove2trashbin({ path: res.data.path });
        } else {
          message.error('获取文件路径失败');
        }
      },
      onError: () => {
        message.error('获取文件路径失败');
      }
    }
  );

  // 移动到回收站
  const { run: runMove2trashbin } = useRequest(
    move2trashbin,
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          message.success('删除成功');
          if (refreshList) {
            refreshList();
          }
        } else {
          message.error('删除失败，请重试');
        }
      },
      onError: () => {
        message.error('删除失败，请重试');
      },
    }
  );

  const handleEditMatch = (episode: Episode) => {
    setMorePopoverVisible(false);
    setCurrentEpisode(episode);
    setShowMatchCorrection(true);
  };

  const move2Trashbin = useCallback((m, episode: Episode) => {
    m.destroy();
    if (episode && episode.file_id) {
      runFileDelete({ file_id: episode.file_id });
    } else {
      message.error('无法获取文件信息');
    }
  }, [runFileDelete]);

  const delFile = useCallback((modal, episode: Episode) => {
    if (!episode.file_media_id) {
      message.error('无法获取媒体信息');
      return;
    }

    modalShow(`是否确定删除文件？`, <>删除的文件将移至"回收站"，保留30天</>, (m => {
      m.destroy();
      modal.destroy();
      
      // 先获取文件路径，然后再移动到回收站
      runGetFilePath({ 
        media_ids: [Number(episode.file_media_id)], 
        lib_id: episode.lib_id !== undefined ? episode.lib_id : 0 
      });
    }), () => null, false, { position: 'center', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } });
  }, [runGetFilePath]);

  // 删除
  const del = useCallback((episode: Episode) => {
    setMorePopoverVisible(false);

    const m = modalShow('确认删除吗？', (
      <>
        <div className={styles.modal_button} onClick={() => move2Trashbin(m, episode)}>仅从媒体库移除</div>
        <div className={styles.modal_button} style={{ color: 'var(--emergency-text-color)' }} onClick={() => delFile(m, episode)}>删除文件</div>
      </>
    ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'center' });
  }, [delFile, move2Trashbin]);

  // 处理收藏按钮点击
  const handleFavoriteClick = (e: React.MouseEvent, episode: Episode) => {
    e.stopPropagation(); // 阻止冒泡，避免触发卡片点击事件
    if (onToggleFavorite) {
      onToggleFavorite(episode);
    }
  };

  // 处理标记观看按钮点击
  const handleWatchedClick = (e: React.MouseEvent, episode: Episode) => {
    e.stopPropagation(); // 阻止冒泡，避免触发卡片点击事件
    if (onToggleWatched) {
      onToggleWatched(episode);
    }
  };

  // 处理更多菜单点击
  const handleMoreMenuClick = (e: React.MouseEvent, episode: Episode) => {
    e.stopPropagation(); // 阻止冒泡，避免触发卡片点击事件
    setCurrentEpisode(episode);
    setMorePopoverVisible(!morePopoverVisible);
  };

  // 处理焦点元素的滚动
  const handleFocusScroll = (item: any) => {
    if (scrollContainerRef.current && item.ref.current) {
      const container = scrollContainerRef.current;
      const element = item.ref.current;
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      
      // 检查元素是否在可视区域外（水平滚动）
      if (elementRect.right > containerRect.right || elementRect.left < containerRect.left) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
    }
  };

  return (
    <div className={styles.episodeListContainer}>
      {/* <h3 className={styles.episodeListTitle}>{title}</h3> */}

      <div className={styles.episodeListWrapper}>
        {showLeftArrow && !isTv && (
          <div className={styles.scrollArrow} onClick={() => handleScroll('left')}>
            <LeftOutlined />
          </div>
        )}

        <div ref={scrollContainerRef} className={isTv?styles.episodeList_tv:styles.episodeList}>
          {episodes.map((episode, index) => (
            isTv ? (
              <div key={episode.id} className={`${styles.episodeItem_tv} ${currentEpisodeId === episode.id ? styles.episodeItemActive : ''}`}>
                <TVFocusable
                  id={`tv-focus-episodeList-episode-${episode.id}`}
                  row={baseRow}
                  col={index}
                  onClick={() => onEpisodeSelect(episode)}
                  className={styles.episodeThumbnail_tv}
                  currentItemCallback={handleFocusScroll}
                >
                  <img src={episode.thumbnail} alt={`第${episode.episodeNumber}集`} />

                  {/* 播放进度条 */}
                  {episode.progress !== undefined && episode.progress > 0 && (
                    <div className={styles.progressBarContainer}>
                      <div
                        className={styles.progressBar}
                        style={{ width: `${episode.progress}%` }}
                      />
                    </div>
                  )}
                </TVFocusable>
                <div className={styles.episodeNumber_tv}>
                  <span>第{episode.episodeNumber}集</span>
                </div>
              </div>
            ) : (
              <div
                key={episode.id}
                className={`${styles.episodeItem} ${currentEpisodeId === episode.id ? styles.episodeItemActive : ''}`}
                onClick={() => onEpisodeSelect(episode)}
                onMouseEnter={() => setHoveredEpisodeId(episode.id)}
                onMouseLeave={() => setHoveredEpisodeId(null)}
              >
                <div className={styles.episodeThumbnail}>
                  <img src={episode.thumbnail} alt={`第${episode.episodeNumber}集`} />

                  {/* 遮罩层和播放按钮 */}
                  {hoveredEpisodeId === episode.id && (
                    <div className={styles.hoverOverlay}>
                      <div className={styles.playButton}>
                        <CaretRightFilled style={{ fontSize: "20px" }} />
                      </div>

                      <div className={styles.actionButtons}>
                        <span
                          className={styles.actionButton}
                          onClick={(e) => handleFavoriteClick(e, episode)}
                        >
                          {episode.favorite ? <HeartFilled style={{ color: "#FF4D4F" }} /> : <HeartOutlined />}
                        </span>
                        <span
                          className={styles.actionButton}
                          onClick={(e) => handleWatchedClick(e, episode)}
                        >
                          {episode.watched ? <CheckOutlined style={{ color: "#1890FF" }} /> : <CheckOutlined />}
                        </span>
                        <span
                          className={styles.actionButton}
                          onClick={(e) => handleMoreMenuClick(e, episode)}
                        >
                          <Popover
                            content={
                              <div className={styles.moreMenu}>
                                <div className={styles.moreMenuItem} onClick={() => handleEditMatch(episode)}>
                                  修正匹配信息
                                </div>
                                <div className={styles.moreMenuItem} style={{ color: 'red' }} onClick={() => del(episode)}>
                                  删除
                                </div>
                              </div>
                            }
                            arrow={false}
                            trigger="click"
                            open={morePopoverVisible && currentEpisode?.id === episode.id}
                            onOpenChange={(visible) => {
                              setMorePopoverVisible(visible);
                              if (visible) {
                                setCurrentEpisode(episode);
                              }
                            }}
                            placement="bottomRight"
                          >
                            <MoreOutlined  />
                          </Popover>
                        </span>
                      </div>
                    </div>
                  )}

                  {/* 播放进度条 */}
                  {episode.progress !== undefined && episode.progress > 0 && (
                    <div className={styles.progressBarContainer}>
                      <div
                        className={styles.progressBar}
                        style={{ width: `${episode.progress}%` }}
                      />
                    </div>
                  )}
                </div>
                <div className={styles.episodeNumber}>
                  <span>第{episode.episodeNumber}集</span>
                </div>
              </div>
            )
          ))}
        </div>

        {showRightArrow && !isTv && (
          <div className={styles.scrollArrow} onClick={() => handleScroll('right')}>
            <RightOutlined />
          </div>
        )}
      </div>
      
      <MatchCorrection
        visible={showMatchCorrection}
        onClose={() => {
          setShowMatchCorrection(false);
        }}
        selectList={currentEpisode ? [currentEpisode] : []}
        refresh={refreshList}
      />
    </div>
  );
};

export default EpisodeList; 