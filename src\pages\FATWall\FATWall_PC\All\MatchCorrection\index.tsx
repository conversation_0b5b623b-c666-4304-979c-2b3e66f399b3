import React, { useState, useEffect, useRef, ReactNode, useCallback } from "react";
import { Modal, Button, Input } from "antd";
import {
  SearchOutlined,
  CloseCircleFilled,
  EnterOutlined,
} from "@ant-design/icons";
import styles from "./index.module.scss";
import { useTheme } from "@/utils/themeDetector";
import close from "@/Resources/icon/close.png";
import closetDark from "@/Resources/icon/close_white.png";
import FilterFilmCard from "@/components/FATWall_APP/FilterFilmCard";
import { searchOnlineMedia, rematchMedia, mediaProps, fileRematch } from "@/api/fatWall";
import { useRequest } from "ahooks";
import { useParams } from "react-router-dom";
import { Toast } from '@/components/Toast/manager';
import { PreloadImage } from "@/components/Image";
import content_null_img from '@/Resources/icon/content_null.png';
import content_null_img_dark from '@/Resources/icon/content_null_dark.png';

// 通用搜索项接口
export interface SearchItem {
  id: string | number;
  [key: string]: any;
}

// 默认的电影搜索结果项结构
export interface MovieSearchItem extends SearchItem {
  title: string;
  year?: string;
  country?: string;
  genres?: string;
  posterUrl: string;
  director?: string;
}

export type SearchStatus = "idle" | "loading" | "success" | "error" | "empty";

// 通用搜索选择器属性接口
export interface MatchCorrectionProps<T extends SearchItem> {
  visible: boolean;
  onClose: () => void;
  onSearch?: (keyword: string) => void;
  onConfirm?: (selectedItem: T | null) => void;
  searchStatus?: SearchStatus;
  searchResults?: T[];
  selectedItem?: T | null;
  onSelectItem?: (item: T) => void;
  title?: string;
  placeholder?: string;
  confirmButtonText?: string;
  retryButtonText?: string;
  emptyMessage?: string;
  errorMessage?: string;
  renderItem?: (item: T, isSelected: boolean) => ReactNode;
  initialKeyword?: string;
  itemType?: "movie" | "default";
  onResetStatus?: () => void;
  isSearch?: boolean;
  selectList?: any[];
  refresh?: (newMediaId?: number) => void;
  searchKeyword?: string;
  searchCategory?: '全部' | '电影' | '电视剧';
  onSearchKeywordChange?: (keyword: string) => void;
  onCategoryChange?: (category: '全部' | '电影' | '电视剧') => void;
  onRetry?: () => void;
  onItemClick?: (item: any) => void;
}

export default function MatchCorrection<
  T extends SearchItem = MovieSearchItem
>({
  visible = false,
  onClose,
  onSearch,
  onConfirm,
  searchStatus: externalSearchStatus,
  searchResults: externalSearchResults,
  selectedItem: externalSelectedItem,
  onSelectItem,
  title = "搜索正确的影片名称",
  placeholder = "请输入影片名称",
  confirmButtonText = "确定修正",
  retryButtonText = "重试",
  emptyMessage = "没搜索到相关结果",
  errorMessage = "搜索失败,请重试",
  renderItem,
  initialKeyword = "",
  itemType = "movie",
  onResetStatus,
  isSearch = false,
  selectList,
  refresh,
  searchKeyword = "",
  searchCategory = "全部",
  onSearchKeywordChange,
  onCategoryChange,
  onRetry,
  onItemClick,
}: MatchCorrectionProps<T>) {
  const { id } = useParams<{ id: string }>();

  const { isDarkMode } = useTheme();
  const [keyword, setKeyword] = useState<string>(initialKeyword);
  const [internalSearchStatus, setInternalSearchStatus] =
    useState<SearchStatus>("idle");
  const [internalSearchResults, setInternalSearchResults] = useState<T[]>([]);
  const [internalSelectedItem, setInternalSelectedItem] = useState<T | null>(
    null
  );
  const inputRef = useRef<any>(null);
  // 添加用于跟踪当前高亮索引的状态
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  // 移除gridSize状态，改为使用常量和ref
  const itemsPerRow = 6; // 一行6个项目
  const lastSelectedIndexRef = useRef<number>(-1);

  // 添加一个用于存储项目引用的对象
  const itemsRef = useRef<Map<number, HTMLDivElement>>(new Map());
  // 搜索结果容器的引用
  const searchResultsContainerRef = useRef<HTMLDivElement>(null);

  // 确定是使用内部状态还是外部状态
  const searchStatus =
    externalSearchStatus !== undefined
      ? externalSearchStatus
      : internalSearchStatus;
  const searchResults =
    externalSearchResults !== undefined
      ? externalSearchResults
      : (internalSearchResults as T[]);
  const selectedItem =
    externalSelectedItem !== undefined
      ? externalSelectedItem
      : internalSelectedItem;


  // 假数据用于非搜索模式
  const mockData = [
    { label: '电视剧区分', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: true, isDrama: true },
    { label: '电影的跳转', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: true, isDrama: false },
    { label: '黑镜', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '人生切割术', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '棋士', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '黑镜', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '人生切割术', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '棋士', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '混沌少年时', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false }, { label: '人生切割术', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '棋士', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '混沌少年时', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false }, { label: '人生切割术', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '棋士', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
    { label: '混沌少年时', core: '9.4', time: '2001', cover: '', file: 'xxx', isLike: false },
  ]

  // 根据模式选择数据源
  const data = isSearch ? (searchResults.map((result: any) => ({
    label: result.title,
    core: result.score,
    time: result.year,
    cover: result.posterUrl,
    file: '',
    isLike: false,
    isDrama: result.type === '电视剧',
    media_id: result.media_id,
    id: result.id
  })) as any[]) : mockData;

  // 当组件显示时，聚焦输入框
  useEffect(() => {
    if (visible) {
      setKeyword(initialKeyword);
      if (!externalSelectedItem) {
        setInternalSelectedItem(null);
      }
      if (!externalSearchStatus) {
        setInternalSearchStatus("idle");
      }
      if (!externalSearchResults) {
        setInternalSearchResults([]);
      }
      // 重置选中索引
      setSelectedIndex(-1);
      lastSelectedIndexRef.current = -1;
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [
    visible,
    initialKeyword,
    externalSelectedItem,
    externalSearchStatus,
    externalSearchResults,
  ]);

  // 滚动到选中项目的函数
  const scrollToSelectedItem = useCallback((index: number) => {
    setTimeout(() => {
      const selectedElement = itemsRef.current.get(index);
      const containerElement = searchResultsContainerRef.current;

      if (selectedElement && containerElement) {
        // 获取容器和选中元素的位置信息
        const containerRect = containerElement.getBoundingClientRect();
        const selectedRect = selectedElement.getBoundingClientRect();

        // 计算元素相对于容器的位置
        const relativeTop = selectedRect.top - containerRect.top;
        const relativeBottom = selectedRect.bottom - containerRect.top;

        // 判断选中元素是否在容器可视区域之外
        const isAbove = relativeTop < 0;
        const isBelow = relativeBottom > containerRect.height;

        if (isAbove) {
          // 如果选中元素在可视区域上方，平滑滚动到元素位置
          const scrollDistance = relativeTop - 10; // 添加少量边距

          // 使用平滑滚动
          containerElement.scrollBy({
            top: scrollDistance,
            behavior: 'smooth'
          });
        } else if (isBelow) {
          // 如果选中元素在可视区域下方，平滑滚动到元素位置
          const scrollDistance = relativeBottom - containerRect.height + 10; // 添加少量边距

          // 使用平滑滚动
          containerElement.scrollBy({
            top: scrollDistance,
            behavior: 'smooth'
          });
        }
      }
    }, 50); // 短暂延迟确保DOM已更新
  }, []);

  // 创建一个稳定的键盘事件处理函数，避免重复创建
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!visible || !isSearch || searchStatus !== "success") return;

    switch (e.key) {
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex(prev => {
          // 使用函数式更新避免依赖selectedIndex
          const currentIndex = prev !== -1 ? prev : 0;
          // 计算当前项目在哪一行哪一列
          const currentRow = Math.floor(currentIndex / itemsPerRow);
          const currentCol = currentIndex % itemsPerRow;

          // 如果已经在第一行，则保持不变
          if (currentRow === 0) return currentIndex;

          // 否则，移动到上一行的相同列位置
          const targetIndex = (currentRow - 1) * itemsPerRow + currentCol;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(targetIndex), 0);
          return targetIndex;
        });
        break;
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex(prev => {
          // 使用函数式更新避免依赖selectedIndex
          const currentIndex = prev !== -1 ? prev : 0;
          // 计算当前项目在哪一行哪一列
          const currentRow = Math.floor(currentIndex / itemsPerRow);
          const currentCol = currentIndex % itemsPerRow;

          // 计算下一行同列的索引
          const targetIndex = (currentRow + 1) * itemsPerRow + currentCol;

          // 如果下一行同列位置超出了数据范围，则保持不变
          const newIndex = targetIndex < data.length ? targetIndex : currentIndex;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(newIndex), 0);
          return newIndex;
        });
        break;
      case "ArrowLeft":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          const newIndex = currentIndex > 0 ? currentIndex - 1 : currentIndex;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(newIndex), 0);
          return newIndex;
        });
        break;
      case "ArrowRight":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          const newIndex = currentIndex < data.length - 1 ? currentIndex + 1 : currentIndex;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(newIndex), 0);
          return newIndex;
        });
        break;
      case "Enter":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          if (currentIndex >= 0 && currentIndex < data.length) {
            // 选择当前高亮的项目
            const selectedItem = data[currentIndex];
            // 此处可以处理选中逻辑，例如打开详情页或执行其他操作
            console.log("选中项目:", selectedItem);

            // 如果有传入的选择回调函数，就调用它
            if (onSelectItem) {
              // 注意：需要确保data中的项目与T类型兼容
              onSelectItem(selectedItem as unknown as T);
            }
          }
          return currentIndex;
        });
        break;
      default:
        break;
    }
  }, [visible, isSearch, searchStatus, data, onSelectItem, scrollToSelectedItem]);

  // 添加键盘事件处理
  useEffect(() => {
    if (visible && isSearch && searchStatus === "success") {
      window.addEventListener("keydown", handleKeyDown);
      return () => {
        window.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [visible, isSearch, searchStatus, handleKeyDown]);

  // 确保初始化选中第一个项目，使用ref避免无限循环
  useEffect(() => {
    if (isSearch && searchStatus === "success" && lastSelectedIndexRef.current === -1 && data.length > 0) {
      lastSelectedIndexRef.current = 0;
      setSelectedIndex(0);
    }
  }, [isSearch, searchStatus, data]);

  // 当选中索引变化时，确保选中项目在可视区域内
  useEffect(() => {
    if (selectedIndex >= 0 && visible && isSearch) {
      scrollToSelectedItem(selectedIndex);
    }
  }, [selectedIndex, visible, isSearch, scrollToSelectedItem]);

  // 搜索请求
  const { run: runSearchOnlineMedia } = useRequest(
    async (keyword: string) => {
      if (!keyword.trim()) return [];
      const res = await searchOnlineMedia(keyword);
      let data: mediaProps[] = [];
      if (Array.isArray(res.data)) {
        data = res.data;
      } else if (res.data && Array.isArray((res.data as any).medias)) {
        data = (res.data as any).medias;
      }
      return data;
    },
    {
      manual: true,
      onSuccess: (data) => {
        if (!data || !data.length) {
          setInternalSearchStatus("empty");
          setInternalSearchResults([]);
        } else {
          setInternalSearchStatus("success");
          setInternalSearchResults(data as unknown as T[]);
        }
      },
      onError: () => {
        setInternalSearchStatus("error");
        setInternalSearchResults([]);
      },
    }
  );

  const { run: runRematchMedia } = useRequest(
    rematchMedia,
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          onClose();
          Toast.show('修正成功');
          // 刷新媒体列表，传递new_media_id
          if (refresh) {
            // 如果返回了new_media_id，传递给refresh函数
            const newMediaId = res.data?.new_media_id;
            refresh(newMediaId);
          }
        } else {
          Toast.show('修正失败，请重试');
        }
      },
      onError: (err) => {
        console.log('err: ', err);
      },
    }
  );

  // 文件修正
  const { run: runFileRematch } = useRequest(
    fileRematch,
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          onClose();
          Toast.show('修正成功');
          // 刷新媒体列表
          if (refresh) {
            refresh();
          }
        } else {
          Toast.show('修正失败，请重试');
        }
      },
      onError: (err) => {
        console.log('err: ', err);
      },
    }
  );

  // 处理搜索
  const handleSearch = () => {
    const currentKeyword = isSearch ? searchKeyword : keyword;
    if (!currentKeyword.trim()) return;

    if (!externalSearchStatus) {
      setInternalSearchStatus("loading");
    }

    // 调用搜索API
    if (onSearch) {
      onSearch(currentKeyword.trim());
    } else {
      runSearchOnlineMedia(currentKeyword.trim());
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (isSearch && onSearchKeywordChange) {
      onSearchKeywordChange(value);
    } else {
      setKeyword(value);
    }
  };

  // 清空输入
  const handleClear = () => {
    if (isSearch && onSearchKeywordChange) {
      onSearchKeywordChange("");
    } else {
      setKeyword("");
    }

    if (!externalSearchStatus) {
      setInternalSearchStatus("idle");
      setInternalSearchResults([]);
      setInternalSelectedItem(null);
    }
    if (onResetStatus) {
      onResetStatus();
    }
    inputRef.current?.focus();
  };

  // 处理选择项
  const handleSelectItem = (item: T) => {
    // 统一获取ID的方式，添加对index字段的支持，处理0值情况
    const getItemId = (item: any, fallbackIndex?: number) => {
      if (item.media_id !== undefined) return item.media_id;
      if (item.id !== undefined) return item.id;
      if (item.index !== undefined) return item.index;
      if (fallbackIndex !== undefined) return `item-${fallbackIndex}`;
      return undefined;
    };
    const itemId = getItemId(item);

    if (onSelectItem) {
      // 如果当前项已经被选中，则取消选中
      const selectedId = selectedItem ? getItemId(selectedItem) : null;
      if (selectedId !== null && itemId === selectedId) {
        onSelectItem(null as unknown as T);
      } else {
        onSelectItem(item);
      }
    } else if (!externalSelectedItem) {
      // 如果当前项已经被选中，则取消选中
      const internalSelectedId = internalSelectedItem ? getItemId(internalSelectedItem) : null;
      if (internalSelectedId !== null && itemId === internalSelectedId) {
        setInternalSelectedItem(null);
      } else {
        setInternalSelectedItem(item);
      }
    }
  };

  // 处理确认修正
  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm(selectedItem);
      onClose();
    } else if (selectedItem) {
      // 查找索引
      const index = (selectedItem as any).index !== undefined ? (selectedItem as any).index : 0;

      // 检查是否有文件ID，如果有则使用fileRematch，否则使用rematchMedia
      if (selectList && selectList.length > 0 && selectList[0].file_id) {
        runFileRematch({
          file_id: selectList[0].file_id,
          keyword: keyword,
          index,
        });
      } else {
        runRematchMedia({
          media_ids: selectList && selectList.length > 0 ? selectList.map(it => it.media_id) : [],
          lib_id: Number(id) || 0,
          keyword: keyword,
          index,
        });
      }
    } else {
      onClose();
    }
  };

  // 处理点击电影卡片
  const handleCardClick = (index: number) => {
    setSelectedIndex(index);
    // 滚动到选中项目
    scrollToSelectedItem(index);
  };

  // 默认的电影搜索结果项渲染
  const renderMovieItem = (item: T, isSelected: boolean) => {
    const movieItem = item as unknown as MovieSearchItem;
    return (
      <>
        <div className={styles.resultPoster}>
          <img src={Array.isArray((movieItem as any).poster) ? (movieItem as any).poster[0] : (movieItem.posterUrl || '')} alt={(movieItem as any).origin_name || (movieItem as any).trans_name || movieItem.title || ''} />
        </div>
        <div className={styles.resultInfo}>
          <div className={styles.resultTitle}>{(movieItem as any).origin_name || (movieItem as any).trans_name || movieItem.title || ''}</div>
          {movieItem.year && (
            <div className={styles.resultMeta}>
              时间: <span>{movieItem.year}</span>
            </div>
          )}
          {((movieItem as any).origin_place || movieItem.country) && (
            <div className={styles.resultMeta}>
              国家: <span>{(movieItem as any).origin_place || movieItem.country}</span>
            </div>
          )}
          {((movieItem as any).classes || movieItem.genres) && (
            <div className={styles.resultMeta}>
              类型: <span>{(movieItem as any).classes || movieItem.genres}</span>
            </div>
          )}
        </div>
        <div className={styles.resultSelect}>
          <div className={`${styles.customRadio} ${isSelected ? styles.selected : ''}`}>
            {isSelected && <div className={styles.innerDot}></div>}
          </div>
        </div>
      </>
    );
  };

  // 默认的普通搜索结果项渲染
  const renderDefaultItem = (item: T, isSelected: boolean) => {
    const anyItem = item as any;
    return (
      <>
        <div className={styles.resultInfo}>
          <div className={styles.resultTitle}>
            {anyItem.title || anyItem.name || anyItem.origin_name || anyItem.trans_name || `${anyItem.id || anyItem.media_id}`}
          </div>
        </div>
        <div className={styles.resultSelect}>
          <div className={`${styles.customRadio} ${isSelected ? styles.selected : ''}`}>
            {isSelected && <div className={styles.innerDot}></div>}
          </div>
        </div>
      </>
    );
  };

  // 渲染修正搜索结果
  const renderSearchResults = () => {
    switch (searchStatus) {
      // case "loading":
      //   return (
      //     <div className={styles.loadingContainer}>
      //       <Spin size="large" />
      //     </div>
      //   );
      case "error":
        return (
          <div className={styles.noResults}>
            <PreloadImage className={styles.noResultsIcon} src={isDarkMode ? content_null_img_dark : content_null_img} alt='content_null' />
            <div className={styles.noResultsText}>{errorMessage}</div>
            <div className={styles.retryButton} onClick={isSearch && onRetry ? onRetry : handleSearch}>
              {retryButtonText}
            </div>
          </div>
        );
      case "empty":
        return (
          <div className={styles.noResults}>
            <PreloadImage className={styles.noResultsIcon} src={isDarkMode ? content_null_img_dark : content_null_img} alt='content_null' />
            <div className={styles.noResultsText}>{emptyMessage}</div>
          </div>
        );
      case "success":
        if (isSearch) {
          return <div className={styles.resultItem}>
            {data.map((item, index) => (
              <div
                key={index}
                className={`${selectedIndex === index ? styles.selectedItem : ''}`}
                onClick={() => {
                  if (onItemClick) {
                    // 在搜索模式下点击卡片时，调用跳转函数
                    onItemClick(item);
                  } else {
                    handleCardClick(index);
                  }
                }}
                ref={el => {
                  if (el) {
                    itemsRef.current.set(index, el);
                  } else {
                    itemsRef.current.delete(index);
                  }
                }}
              >
                <FilterFilmCard
                  type='pc'
                  title={item.label}
                  subtitle={item.time}
                  score={item.core}
                  cover={item.cover}
                  isLike={item.isLike}
                />
              </div>
            ))}
          </div>
        }
        return searchResults.map((item, index) => {
          // 统一获取ID的方式，添加对index字段的支持，处理0值情况
          const getItemId = (item: any, fallbackIndex?: number) => {
            if (item.media_id !== undefined) return item.media_id;
            if (item.id !== undefined) return item.id;
            if (item.index !== undefined) return item.index;
            if (fallbackIndex !== undefined) return `item-${fallbackIndex}`;
            return undefined;
          };

          const itemKey = getItemId(item, index);
          const selectedKey = selectedItem ? getItemId(selectedItem) : null;
          const isSelected = selectedKey !== null && itemKey === selectedKey;

          return (
            <div
              key={itemKey}
              className={styles.resultItem}
              onClick={() => handleSelectItem(item)}
            >
              {renderItem
                ? renderItem(item, isSelected)
                : itemType === "movie"
                  ? renderMovieItem(item, isSelected)
                  : renderDefaultItem(item, isSelected)}
            </div>
          );
        });
      default:
        return null;
    }
  };

  const renderTitle = () => (
    <div className={styles.modalHeader}>
      <img
        alt=""
        src={isDarkMode ? closetDark : close}
        className={styles.backIcon}
        onClick={onClose}
      />
      <div className={styles.title}>{title}</div>
    </div>
  );

  const renderFooter = () => (
    <div className={styles.modalFooter}>
      <div>
        <CloseCircleFilled />
        移动光标
      </div>
      <div>
        <EnterOutlined />
        选择条目
      </div>
      <div>
        esc 隐藏窗口
      </div>
      <div>
        <CloseCircleFilled />
        切换类别
      </div>
    </div>
  );

  return (
    <Modal
      title={isSearch ? null : renderTitle()}
      open={visible}
      footer={isSearch ? renderFooter() : null}
      onCancel={onClose}
      width={546}
      className={isSearch ? styles.matchModalSearch : styles.matchModal}
      closeIcon={null}
      centered
      styles={{
        body: {
          height: "calc(636px - 62px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalWrapper}>
        <div className={isSearch ? styles.searchContainerTwo : styles.searchContainer}>
          <div className={styles.searchBox}>
            <Input
              ref={inputRef}
              className={styles.searchInput}
              placeholder={placeholder}
              value={isSearch ? searchKeyword : keyword}
              onChange={handleInputChange}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined className={styles.searchIcon} />}
              suffix={
                (isSearch ? searchKeyword : keyword) ? (
                  <CloseCircleFilled
                    className={styles.clearButton}
                    onClick={handleClear}
                  />
                ) : null
              }
              variant="borderless"
              autoFocus
            />
          </div>
          <div>
            {isSearch ? <span style={{ color: '#3482FF' }} onClick={onClose}>取消</span> : null}
          </div>
        </div>

        <div
          className={styles.searchResults}
          ref={searchResultsContainerRef}
          style={{
            maxHeight: "calc(100% - 70px)",
            overflowY: "auto"
          }}
        >
          {renderSearchResults()}
        </div>

        <div className={styles.footerActions}>
          {isSearch ? null : (
            <Button
              className={styles.confirmButton}
              onClick={handleConfirm}
              disabled={
                searchStatus === "empty" ||
                searchStatus === "error" ||
                !selectedItem
              }
              type="primary"
            >
              {confirmButtonText}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
