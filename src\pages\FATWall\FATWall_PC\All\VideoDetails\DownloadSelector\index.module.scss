// 下载选择弹窗样式
.downloadModal {
  :global(.ant-modal-content) {
   border-radius: 20px;
    padding: 0;
    width: 546px;
    // height: 600px;
    overflow: hidden;
    background-color: var(--desktop-modal-bg-color);
  }

  :global(.ant-modal-header) {
    padding: 20px 24px 16px;
    text-align: center;
    background-color: var(--desktop-modal-bg-color);
  }

  :global(.ant-modal-title) {
    color: var(--text-color);
    font-size: 18px;
    font-weight: 600;
  }

  :global(.ant-modal-close) {
    color: var(--text-color);
left: 15px;
    &:hover {
      background-color: var(--card-hover-color);
    }
  }

  :global(.ant-modal-body) {
    padding: 0;
    max-height: 500px;
    overflow: hidden;
  }
}

.content {
  padding: 20px 24px;
    display: flex;
  flex-direction: column;
  height: 400px;
  overflow-y: auto;

  // 滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--thinLine-background-color);
    border-radius: 3px;

    &:hover {
      background: var(--primary-color);
    }
  }
}

// 电视剧网格布局
.episodeGrid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.episodeItem {
  aspect-ratio: 1;
  background-color: var(--card-background-color);
  border: 1px solid #000;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background-color: var(--card-hover-color);
    border-color: var(--primary-color);
  }

  &.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    .episodeNumber {
      color: #ffffff;
    }
  }
}

.episodeNumber {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  user-select: none;
}

// 电影列表布局
.movieList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.movieItem {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: var(--card-background-color);
  border: 1px solid transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--card-hover-color);
  }
}

.moviePoster {
  width: 80px;
  height: 45px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.placeholderPoster {
  width: 100%;
  height: 100%;
  background-color: var(--card-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.movieInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-right: 12px;
}

.movieTitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.4;
}

.movieDuration {
  font-size: 14px;
  color: var(--list-value-text-color);
}

.movieCheckbox {
  display: flex;
  align-items: center;
  justify-content: center;

  :global(.ant-checkbox) {
    --adm-color-primary: var(--primary-color);
  }

  :global(.ant-checkbox-wrapper) {
    margin: 0;
  }

  :global(.ant-checkbox-inner) {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border-color: var(--thinLine-background-color);

    &:hover {
      border-color: var(--primary-color);
    }
  }

  :global(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }
}

// 底部操作区域
.footer {
  padding: 20px 24px;
  // border-top: 1px solid var(--thinLine-background-color);
  background-color: var(--desktop-modal-bg-color);
  display: flex;
  gap: 12px;
}

.downloadAllButton {
  flex: 1;
  height: 40px;
  background-color: var(--cancel-btn-background-color);
  color: var(--text-color);
  border: none;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--primary-color);
    color: #ffffff;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.downloadSelectedButton {
  flex: 1;
  height: 40px;
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    opacity: 0.9;
  }

  &:disabled,
  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: var(--primary-color);
    color: #fff;
  }
}
