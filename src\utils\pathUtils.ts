/**
 * 路径格式化工具函数
 */

/**
 * 将系统路径转换为用户友好的显示格式
 * 将 /home/<USER>/pool数字/data/ 前面的部分替换为 "存储空间"
 * 
 * @param path 原始路径
 * @returns 格式化后的路径
 * 
 * @example
 * formatPathForDisplay("/home/<USER>/pool0/data/百度网盘/04-Web项目旅游网/day26_综合案例2")
 * // 返回: "存储空间/百度网盘/04-Web项目旅游网/day26_综合案例2"
 */
export const formatPathForDisplay = (path: string): string => {
  if (!path || typeof path !== 'string') {
    return path || '';
  }

  // 匹配 /home/<USER>/pool数字/data/ 的模式
  const dataPathRegex = /^\/home\/[^/]+\/pool\d+\/data\//;
  
  if (dataPathRegex.test(path)) {
    // 将 data 前面的部分替换为 "存储空间"
    return path.replace(dataPathRegex, '存储空间/');
  }
  
  // 如果不匹配预期格式，返回原路径
  return path;
};

/**
 * 批量格式化路径数组
 * 
 * @param paths 路径数组
 * @returns 格式化后的路径数组
 */
export const formatPathsForDisplay = (paths: string[]): string[] => {
  if (!Array.isArray(paths)) {
    return [];
  }
  
  return paths.map(formatPathForDisplay);
};
