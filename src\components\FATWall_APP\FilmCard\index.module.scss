.card_list_container {
  width: 100%;
  height: 140px;
  overflow-x: auto;
  display: inline-flex;
  scrollbar-width: none;
  /* Firefox 兼容 */
  -ms-overflow-style: none;
  /* IE/Edge 兼容 */

  > div {
    margin-right: 12px;
  }

  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

.card_list_container_drama {
  width: 100%;
  height: 80px;
  overflow-x: auto;
  display: inline-flex;
  scrollbar-width: none;
  /* Firefox 兼容 */
  -ms-overflow-style: none;
  /* IE/Edge 兼容 */

  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // align-items: center;
  justify-content: center;
}

.content_container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  &:hover {
    cursor: pointer;
  }

  img {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    object-fit: cover;
  }

  .progress {
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0) 94%,
      rgba(0, 0, 0, 0.2)95%,
      rgba(0, 0, 0, 0.2) 100%
    );
    position: absolute;
    bottom: 0;
    border-radius: 12px;
    overflow: hidden;

    .content {
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0) 94%,
        rgba(89, 156, 250, 1)95%,
        rgba(89, 156, 250, 1) 100%
      );
      transition: all 0.3s;
    }
  }
}
.container_drama {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.content_container_drama {
  width: 135px;
  position: relative;
  height: 76px;
  border-radius: 5px;
  overflow: hidden;
  font-size: 0;
  line-height: 0;

  &::before,
  &::after {
    display: none !important;
  }

  * {
    font-size: 0;
    line-height: 0;
  }

  img {
    height: 76px;
    width: 135px;
  }

  .progress {
    width: 80px;
    height: 4px;
    background: rgba(0, 0, 0, 0.2);
    position: absolute;
    bottom: 0;
    border-radius: 2px;
    overflow: hidden;

    .content {
      height: 100%;
      background: rgba(89, 156, 250, 1);
      transition: all 0.3s;
    }
  }
}

.text_container {
  display: flex;
  flex-direction: column;

  .text_container_title {
    font-family: MiSans;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .text_container_title_drama {
    font-family: MiSans W;
    font-weight: 400;
    font-size: 14px !important;
    line-height: 100% !important;
    letter-spacing: 0px;
    color: #ffffff; //这里没有夜间模式 别改！
    margin-top: 4px;
  }

  .text_container_subtitle {
    font-family: MiSans;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    color: var(--subtitle-text-color);
  }
}

// Horizontal layout container
.horizontal_container {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 20px;

  .horizontal_text_container {
    display: flex;
    flex-direction: column;

    .text_container_title {
      font-family: MiSans;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      color: var(--text-color);
      max-width: 190px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .text_container_subtitle {
      font-family: MiSans W;
      font-weight: 400;
      font-size: 14px;
      line-height: 100%;
      letter-spacing: 0px;
      color: var(--list-value-text-color);
    }
  }
}
