import React, { Suspense, useCallback, useEffect } from "react";
import { Route, Switch } from "react-router-dom";
import routers, { IRouter } from "./router";
import setRootFontSize from "./utils/setRootFontSize";
import { useTheme } from "./utils/themeDetector";
import { getDeviceType, fetchAppInfo, fetchWebDavInfo, fetchAccountInfo, getSystemType } from "./utils/DeviceType";
import CustomLayout from "./layouts/Layout";
import { removeMicroPcListener } from "./utils/microAppUtils";
import { FocusProvider } from "./pages/FATWall/FATWall_TV/TVFocus";

const App: React.FC = () => {
  const { toggleTheme } = useTheme(); // 主题切换函数

  // 定义一个切换主题函数给hik调用
  const changeTheme = useCallback((theme: 'dark' | 'light') => {
    console.log('app调用切换主题:', theme);
    toggleTheme(theme);
  }, [toggleTheme]);

  useEffect(() => {
    // 设置根元素字体大小
    setRootFontSize();
    const initializeApp = async () => {
      try {
        await fetchAppInfo(); // 异步加载设备信息
        await fetchWebDavInfo(); // 加载webDav
        await fetchAccountInfo(); // 加载账号信息

        const os = getSystemType(); // 获取设备类型信息
        switch (os) {
          case 'windows':
            window.onetrack?.('init', ***********);
            break;
          case 'android':
            window.onetrack?.('init', ***********);
            break;
          case 'ios':
            window.onetrack?.('init', ***********);
            break;
          case 'macos':
            window.onetrack?.('init', ***********);
            break;
          default:
            console.warn(`未知的设备类型`);
        }
      } catch (error) {
        console.log("初始化应用时获取设备信息失败:", error);
      }
    };

    initializeApp();
    window.addEventListener("resize", setRootFontSize);
    return () => {
      window.removeEventListener("resize", setRootFontSize);
      //移除PC 监听
      removeMicroPcListener();
    };
  }, []);

  useEffect(() => {
    // 默认调用light
    console.log('初始化主题:light');
    toggleTheme('light');

    // 注册主题切换回调给hik
    window.hs_registerHandler?.('changeTheme', changeTheme);
  }, [changeTheme, toggleTheme])

  // 检查是否需要使用布局
  const shouldApplyLayout = (): boolean => {
    const deviceType = getDeviceType();
    const isPC = deviceType === 1; // 0表示移动端，1表示PC端，2表示TV端

    // 默认PC端使用布局，特定路由不使用
    if (isPC) return false;

    return true;
  };

  const renderRoutes = (routes: IRouter[], parentPath = "") => {

    return (
      <Switch>
        <FocusProvider>
          {routes.map((route) => {

            const fullPath = parentPath ? `${parentPath}${route.path}` : route.path;
            const useLayout = shouldApplyLayout(); // 判断是否使用布局
            const hasChildren = route.children && route.children.length > 0; // 检查是否有子路由

            return (
              <Route
                key={fullPath}
                path={fullPath}
                exact={route.exact}
                render={(props) => {
                  const Component = route.component;
                  const content = (
                    <Suspense fallback={null}>
                      <Component {...props}>
                        {hasChildren && renderRoutes(route.children!, fullPath)}
                      </Component>
                    </Suspense>
                  )

                  return !useLayout ? (
                    <CustomLayout>{content}</CustomLayout>
                  ) : content
                }}
              />
            );
          })}
        </FocusProvider>
      </Switch>
    );
  };

  return renderRoutes(routers);
};

export default App;
