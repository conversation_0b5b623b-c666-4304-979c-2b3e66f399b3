import { createContext, useContext } from 'react';

interface StorageContextType {
  threshold: number;
  setThreshold: React.Dispatch<React.SetStateAction<number>>;
  detectEvents: {
    motionDetect: boolean;
    humanDetect: boolean;
    fireDetect: boolean;
    petDetect: boolean;
    // noiseDetect: boolean;
  };
  setDetectEvents: React.Dispatch<
    React.SetStateAction<{
      motionDetect: boolean;
      humanDetect: boolean;
      fireDetect: boolean;
      petDetect: boolean;
      // noiseDetect: boolean;
    }>
  >;
}

export const StorageContext = createContext<StorageContextType>({
  threshold: 95,
  setThreshold: () => {},
  detectEvents: {
    motionDetect: true,
    humanDetect: true,
    fireDetect: true,
    petDetect: true,
    // noiseDetect: true,
  },
  setDetectEvents: () => {},
});

export const useStorage = () => useContext(StorageContext);