import React, { useState, useEffect, useRef, useCallback } from "react";
import { Modal, Input } from "antd";
import {
  SearchOutlined,
  CloseCircleFilled,
} from "@ant-design/icons";
import styles from "./index.module.scss";
import { useTheme } from "@/utils/themeDetector";
import FilterFilmCard from "@/components/FATWall_APP/FilterFilmCard";
import { PreloadImage } from "@/components/Image";
import content_null_img from '@/Resources/icon/content_null.png';
import content_null_img_dark from '@/Resources/icon/content_null_dark.png';

export type SearchStatus = "idle" | "loading" | "success" | "error" | "empty";

export interface MediaSearchModalProps {
  visible: boolean;
  onClose: () => void;
  onSearch?: (keyword: string) => void;
  searchStatus?: SearchStatus;
  searchResults?: any[];
  searchKeyword?: string;
  searchCategory?: '全部' | '电影' | '电视剧';
  onSearchKeywordChange?: (keyword: string) => void;
  onCategoryChange?: (category: '全部' | '电影' | '电视剧') => void;
  onRetry?: () => void;
  onItemClick?: (item: any) => void;
  onResetStatus?: () => void;
  placeholder?: string;
  retryButtonText?: string;
  emptyMessage?: string;
  errorMessage?: string;
}

export default function MediaSearchModal({
  visible = false,
  onClose,
  onSearch,
  searchStatus = "idle",
  searchResults = [],
  searchKeyword = "",
  searchCategory = "全部",
  onSearchKeywordChange,
  onCategoryChange,
  onRetry,
  onItemClick,
  onResetStatus,
  placeholder = "请输入影片名称",
  retryButtonText = "重试",
  emptyMessage = "没搜索到相关结果",
  errorMessage = "搜索失败,请重试",
}: MediaSearchModalProps) {
  const { isDarkMode } = useTheme();
  const inputRef = useRef<any>(null);

  // 添加用于跟踪当前高亮索引的状态
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  // 一行6个项目
  const itemsPerRow = 6;
  const lastSelectedIndexRef = useRef<number>(-1);

  // 添加一个用于存储项目引用的对象
  const itemsRef = useRef<Map<number, HTMLDivElement>>(new Map());
  // 搜索结果容器的引用
  const searchResultsContainerRef = useRef<HTMLDivElement>(null);

  // 将搜索结果转换为卡片数据格式
  const data = searchResults.map((result: any) => ({
    label: result.title,
    core: result.score,
    time: result.year,
    cover: result.posterUrl,
    file: '',
    isLike: false,
    isDrama: result.type === '电视剧',
    media_id: result.media_id,
    id: result.id
  }));

  // 当组件显示时，聚焦输入框
  useEffect(() => {
    if (visible) {
      // 重置选中索引
      setSelectedIndex(-1);
      lastSelectedIndexRef.current = -1;
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [visible]);

  // 滚动到选中项目的函数
  const scrollToSelectedItem = useCallback((index: number) => {
    setTimeout(() => {
      const selectedElement = itemsRef.current.get(index);
      const containerElement = searchResultsContainerRef.current;

      if (selectedElement && containerElement) {
        // 获取容器和选中元素的位置信息
        const containerRect = containerElement.getBoundingClientRect();
        const selectedRect = selectedElement.getBoundingClientRect();

        // 计算元素相对于容器的位置
        const relativeTop = selectedRect.top - containerRect.top;
        const relativeBottom = selectedRect.bottom - containerRect.top;

        // 判断选中元素是否在容器可视区域之外
        const isAbove = relativeTop < 0;
        const isBelow = relativeBottom > containerRect.height;

        if (isAbove) {
          // 如果选中元素在可视区域上方，平滑滚动到元素位置
          const scrollDistance = relativeTop - 10; // 添加少量边距

          // 使用平滑滚动
          containerElement.scrollBy({
            top: scrollDistance,
            behavior: 'smooth'
          });
        } else if (isBelow) {
          // 如果选中元素在可视区域下方，平滑滚动到元素位置
          const scrollDistance = relativeBottom - containerRect.height + 10; // 添加少量边距

          // 使用平滑滚动
          containerElement.scrollBy({
            top: scrollDistance,
            behavior: 'smooth'
          });
        }
      }
    }, 50); // 短暂延迟确保DOM已更新
  }, []);

  // 键盘事件处理函数
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!visible || searchStatus !== "success") return;

    switch (e.key) {
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          // 计算当前项目在哪一行哪一列
          const currentRow = Math.floor(currentIndex / itemsPerRow);
          const currentCol = currentIndex % itemsPerRow;

          // 如果已经在第一行，则保持不变
          if (currentRow === 0) return currentIndex;

          // 否则，移动到上一行的相同列位置
          const targetIndex = (currentRow - 1) * itemsPerRow + currentCol;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(targetIndex), 0);
          return targetIndex;
        });
        break;
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          // 计算当前项目在哪一行哪一列
          const currentRow = Math.floor(currentIndex / itemsPerRow);
          const currentCol = currentIndex % itemsPerRow;

          // 计算下一行同列的索引
          const targetIndex = (currentRow + 1) * itemsPerRow + currentCol;

          // 如果下一行同列位置超出了数据范围，则保持不变
          const newIndex = targetIndex < data.length ? targetIndex : currentIndex;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(newIndex), 0);
          return newIndex;
        });
        break;
      case "ArrowLeft":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          const newIndex = currentIndex > 0 ? currentIndex - 1 : currentIndex;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(newIndex), 0);
          return newIndex;
        });
        break;
      case "ArrowRight":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          const newIndex = currentIndex < data.length - 1 ? currentIndex + 1 : currentIndex;
          // 滚动到新的选中项目
          setTimeout(() => scrollToSelectedItem(newIndex), 0);
          return newIndex;
        });
        break;
      case "Enter":
        e.preventDefault();
        setSelectedIndex(prev => {
          const currentIndex = prev !== -1 ? prev : 0;
          if (currentIndex >= 0 && currentIndex < data.length) {
            // 选择当前高亮的项目
            const selectedItem = data[currentIndex];
            console.log("选中项目:", selectedItem);

            // 如果有传入的选择回调函数，就调用它
            if (onItemClick) {
              onItemClick(selectedItem);
            }
          }
          return currentIndex;
        });
        break;
      default:
        break;
    }
  }, [visible, searchStatus, data, onItemClick, scrollToSelectedItem, itemsPerRow]);

  // 添加键盘事件处理
  useEffect(() => {
    if (visible && searchStatus === "success") {
      window.addEventListener("keydown", handleKeyDown);
      return () => {
        window.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [visible, searchStatus, handleKeyDown]);

  // 确保初始化选中第一个项目，使用ref避免无限循环
  useEffect(() => {
    if (searchStatus === "success" && lastSelectedIndexRef.current === -1 && data.length > 0) {
      lastSelectedIndexRef.current = 0;
      setSelectedIndex(0);
    }
  }, [searchStatus, data]);

  // 当选中索引变化时，确保选中项目在可视区域内
  useEffect(() => {
    if (selectedIndex >= 0 && visible) {
      scrollToSelectedItem(selectedIndex);
    }
  }, [selectedIndex, visible, scrollToSelectedItem]);

  // 处理搜索
  const handleSearch = () => {
    if (!searchKeyword.trim()) return;

    // 调用搜索API
    if (onSearch) {
      onSearch(searchKeyword.trim());
    }

    // 搜索后让输入框失去焦点，避免与列表键盘事件冲突
    if (inputRef.current) {
      inputRef.current.blur();
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSearchKeywordChange) {
      onSearchKeywordChange(e.target.value);
    }
  };

  // 清空输入
  const handleClear = () => {
    if (onSearchKeywordChange) {
      onSearchKeywordChange("");
    }

    if (onResetStatus) {
      onResetStatus();
    }
    inputRef.current?.focus();
  };

  // 处理点击电影卡片
  const handleCardClick = (index: number) => {
    setSelectedIndex(index);
    // 滚动到选中项目
    scrollToSelectedItem(index);
  };

  // 渲染搜索结果
  const renderSearchResults = () => {
    switch (searchStatus) {
      case "error":
        return (
          <div className={styles.noResults}>
            <PreloadImage className={styles.noResultsIcon} src={isDarkMode ? content_null_img_dark : content_null_img} alt='content_null' />
            <div className={styles.noResultsText}>{errorMessage}</div>
            <div className={styles.retryButton} onClick={onRetry || handleSearch}>
              {retryButtonText}
            </div>
          </div>
        );
      case "empty":
        return (
          <div className={styles.noResults}>
            <PreloadImage className={styles.noResultsIcon} src={isDarkMode ? content_null_img_dark : content_null_img} alt='content_null' />
            <div className={styles.noResultsText}>{emptyMessage}</div>
          </div>
        );
      case "success":
        return (
          <div className={styles.resultItem}>
            {data.map((item, index) => (
              <div
                key={index}
                className={`${selectedIndex === index ? styles.selectedItem : ''}`}
                onClick={() => {
                  if (onItemClick) {
                    // 在搜索模式下点击卡片时，调用跳转函数
                    onItemClick(item);
                  } else {
                    handleCardClick(index);
                  }
                }}
                ref={el => {
                  if (el) {
                    itemsRef.current.set(index, el);
                  } else {
                    itemsRef.current.delete(index);
                  }
                }}
              >
                <FilterFilmCard
                  type='pc'
                  title={item.label}
                  subtitle={item.time}
                  score={item.core}
                  cover={item.cover}
                  isLike={item.isLike}
                />
              </div>
            ))}
          </div>
        );
      default:
        return null;
    }
  };

  const renderFooter = () => (
    <div className={styles.modalFooter}>
      <span>⮁ 移动光标 ｜ ↵ 选择条目 ｜ esc 隐藏窗口 ｜ ⌘ + ⌥ + ⮀ 切换类别</span>
    </div>
  );

  return (
    <Modal
      title={null}
      open={visible}
      footer={renderFooter()}
      onCancel={onClose}
      width={862}
      className={styles.mediaSearchModal}
      closeIcon={null}
      centered
      styles={{
        body: {
          height: "calc(636px - 62px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalWrapper}>
        <div className={styles.searchContainer}>
          <div className={styles.searchBox}>
            <Input
              ref={inputRef}
              className={styles.searchInput}
              placeholder={placeholder}
              value={searchKeyword}
              onChange={handleInputChange}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined className={styles.searchIcon} />}
              suffix={
                searchKeyword ? (
                  <CloseCircleFilled
                    className={styles.clearButton}
                    onClick={handleClear}
                  />
                ) : null
              }
              variant="borderless"
              autoFocus
            />
          </div>
          <div>
            <span style={{ color: '#3482FF' }} onClick={onClose}>取消</span>
          </div>
        </div>

        <div
          className={styles.searchResults}
          ref={searchResultsContainerRef}
          style={{
            maxHeight: "calc(100% - 70px)",
            overflowY: "auto"
          }}
        >
          {renderSearchResults()}
        </div>
      </div>
    </Modal>
  );
} 