import { UserProvider } from "@/utils/UserContext";
import { Spin } from "antd";
import { FC, useEffect } from "react";
import { Route, Switch, useHistory, useRouteMatch } from "react-router-dom";
import styles from "./index.module.scss";
// import { useSideBarChange } from "@/layouts/Layout";

// import { useTheme } from "@/utils/themeDetector";
// import useBDSideBar from "@/layouts/sideBarHooks/bd";


const DefaultLayout = () => {
  const history = useHistory();

  useEffect(() => {
    history.push('/hubGateway_pc/autojk');
  }, [history])

  return (
    <div className={styles.loadingContainer}>
      <Spin size="large" />
    </div>
  )
}

const NasDiskDesktopContent: FC = (props) => {
  const { path } = useRouteMatch();


  return (
    <div className={styles.root_container}>
      <Switch>

        {/* 默认内容 */}
        <Route exact={true} path={path}>
          <DefaultLayout />
        </Route>
      </Switch>
    </div>
  )
}

const NasDiskDesktop: FC = (props) => {
  return (
    <UserProvider>
      <NasDiskDesktopContent {...props} />
    </UserProvider>
  )
}

export default NasDiskDesktop;
