import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from '../AllRecentlyPlay/index.module.scss';
import { IFilmCard } from '../../../../components/FATWall_APP/FilmCard';
import { MovieCardTV } from '../optPages/RecentlyPlay';
import { filmAndTvProps, getRecentlyAdd } from '@/api/fatWall';
import { useLibraryListTV } from '..';
import { formatTimeAgo } from '../../FATWall_APP/Recently/RecentlyPlay';
import { useInViewport } from 'ahooks';
import ErrorComponentTV from '../Error';
import { useHistory } from 'react-router-dom';
import placeholder‌_poster_big from '@/Resources/icon/placeholder‌_row_big.png';
import { defaultPageParamTV } from '../optPages/All';

const AllRecentlyAdd = () => {
  const [recentlyAddFilm, setRecentlyAddFilm] = useState<filmAndTvProps>({ medias: [], count: 0 });
  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮
  const history = useHistory();

  // 加载更多的必要参数
  const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParamTV); // 分页参数
  const prevParams = useRef({ ...pageOpt });
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const [loading, setLoading] = useState<boolean>(true);

  // 最近播放和添加的电影列表数据
  const AddedRun = useCallback(async (callback: (v: filmAndTvProps) => void, pageOpt) => {
    setLoading(true);
    const data = await getRecentlyAdd({ ...pageOpt, tv: 1 }, { showLoading: false }).catch(e => {
      console.log('获取最近播放失败：', e);
      setIsError(true);
      setLoading(false);
    })

    if (data && data.code === 0 && data.data) {
      // 最近播放的电影列表数据加载成功，更新状态
      callback({ medias: data.data.medias, count: 0 });
      setIsError(false);
      setLoading(false);

      // 判断是否还有更多数据可以加载
      if (data.data.count < pageOpt.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    } else {
      setLoading(false);
      setIsError(true);
    }
  }, []);

  useEffect(() => {
    AddedRun(v => setRecentlyAddFilm(v), defaultPageParamTV);
  }, [AddedRun])

  const recentlyAddFilmList: IFilmCard[] = useMemo(() => {
    return recentlyAddFilm.medias.map((media, index) => {
      const time = formatTimeAgo(media.create_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : placeholder‌_poster_big : placeholder‌_poster_big; // 数组0索引为封面图，1索引为海报
      return {
        ...media, poster: poster, media_id: media.media_id, progress: media.last_seen_percent, title: media.trans_name, time: `${time}`, type: 'add', year: `${media.year}`
      }
    })
  }, [recentlyAddFilm])

  const recentlyAddListByTime = useMemo(() => {
    let obj: { [key: string]: IFilmCard[] } = {
      sevenDays: [],
      oneMonths: [],
      threeMonths: []
    };

    recentlyAddFilmList.forEach((it: any) => {
      const now = Math.floor(Date.now() / 1000);
      const seconds = now - it.create_time;
      const minutes = seconds / 60;
      const hours = minutes / 60;
      const time = hours / 24; // 天数

      if (time <= 7) {
        obj['sevenDays'].push(it);
      } else if (time > 7 && time <= 31) {
        obj['oneMonths'].push(it);
      } else {
        obj['threeMonths'].push(it);
      }
    })
    return obj;
  }, [recentlyAddFilmList])

  const timeLabel: { [key: string]: string } = {
    sevenDays: '近7天',
    oneMonths: '近1个月',
    threeMonths: '近3个月'
  }

  // 跳转到VideoDetails页面
  const toVideoDetails = useCallback((item: any) => {
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_tv/videoDetails?${params.toString()}`);
  }, [history]);

  // 重置
  const clearAndRefresh = useCallback(() => {
    setHasMore(false);
    setRecentlyAddFilm({ medias: [], count: 0 });

    // 重置分页参数，重新加载数据
    setPageOpt(defaultPageParamTV);
    if (prevParams.current) {
      const { limit, offset } = prevParams.current;
      if (limit === defaultPageParamTV.limit && offset === defaultPageParamTV.offset) {
        AddedRun((v) => setRecentlyAddFilm(v), defaultPageParamTV);
      }
    }
    prevParams.current = defaultPageParamTV;
  }, [AddedRun])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useEffect(() => {
    if (inViewport) {
      setPageOpt((prev) => {
        prevParams.current = { ...prev, offset: prev.offset + prev.limit };
        return { ...prev, offset: prev.offset + prev.limit }
      })
    }
  }, [inViewport])

  // 处理分页参数变化时的数据加载
  useEffect(() => {
    if (pageOpt.offset > 0) {
      AddedRun((v) => setRecentlyAddFilm(prev => ({
        ...prev,
        medias: [...prev.medias, ...v.medias],
        count: prev.count + v.count
      })), pageOpt);
    }
  }, [pageOpt, AddedRun])

  const libs = useLibraryListTV();

  return (
    <ErrorComponentTV loading={loading} isError={isError} hasContent={recentlyAddFilmList.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
      text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无内容'} style={{ backgroundColor: 'var(--text-color' }}
      subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
      <div className={styles.container}>
        <div className={styles.header}>
          <span>最近添加</span>
        </div>
        {
          Object.keys(recentlyAddListByTime).map((key, ind) => (
            recentlyAddListByTime[key].length !== 0 &&
            <div key={key} className={styles.file_container}>
              <span className={styles.time_title}>{timeLabel[key]}</span>
              <div className={styles.file_content}>
                {
                  recentlyAddListByTime[key].map((item, index) => {
                    let length = 0;
                    const arr = Object.entries(recentlyAddListByTime);
                    for (let i = 0; i < ind; i++) {
                      const dataArr = arr[i][1];
                      length += Math.ceil(dataArr.length / 4);
                    }
                    return <MovieCardTV type='add' key={`${item.title}_${index}`} focusableId={`tv-id-allRecentlyAdd-${item.title}-${index}`} focusableRow={Math.floor(index / 4) + length} focusableCol={index % 4} callback={() => toVideoDetails(recentlyAddFilm.medias[recentlyAddListByTime[key].indexOf(item)])} cover={item.poster} playTime={item.progress!.toString()} movieTitle={item.title} time={item.time} />
                  })
                }
              </div>
            </div>
          ))
        }

        {
          hasMore && <div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>
        }
      </div >
    </ErrorComponentTV>
  )
}

export default AllRecentlyAdd;