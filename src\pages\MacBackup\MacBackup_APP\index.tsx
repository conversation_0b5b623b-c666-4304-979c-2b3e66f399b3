import { FC, useCallback, useMemo, useRef, useState } from 'react';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { PreloadImage } from '@/components/Image';
import finish from '@/Resources/icon/finish.png';
import finish_dark from '@/Resources/icon/finish-dark.png';
import { useTheme } from '@/utils/themeDetector';

import mac_show_img from '@/Resources/macBackup/mac_show_pic.png';
import nas_show_img from '@/Resources/macBackup/nas_show_pic.png';
import next from "@/Resources/icon/next.png";
import next_dark from "@/Resources/icon/next_dark.png";

import List, { IListData, modalShow } from '@/components/List';
import { Form, Switch, Input, Button } from 'antd';
import { Toast } from '@/components/Toast/manager';
import FloatPanel from '@/components/FloatPanel';
import FileSelectorUtil, { IFileDirBaseProps } from '@/components/FileSelector';
import { useRequest, useUpdateEffect } from 'ahooks';
import { FileItem, getPoolInfo, listDirectory, StoragePool } from '@/api/fatWall';
import { WebDavInfo } from '@/utils/DeviceType';
import NumberInput from './numberInput';
import { px2rem } from '@/utils/setRootFontSize';

export interface IMacBackupConfig {
  isBackup: boolean;
  path: string;
  sizeLimit: number;
  unit: string;
}

interface listCardProps {
  text: string;
  subtext?: string;
  onCallback?: () => void;
}

export const ListCard = (props: listCardProps) => {
  const { text, subtext, onCallback } = props;
  const { isDarkMode } = useTheme();

  return (
    <div className={styles.list_card_container} onClick={onCallback} style={{ cursor: onCallback ? 'pointer' : 'auto' }}>
      <div className={styles.list_card_left}>
        <div className={styles.list_card_left_text}>{text}</div>
        {subtext && <div className={styles.list_card_left_subtext}>{subtext}</div>}
      </div>
      <div className={styles.list_card_right}>
        <PreloadImage src={isDarkMode ? next_dark : next} alt='next' />
      </div>
    </div>
  )
}

const MacBackup_App_Index_Page: FC = () => {

  const { isDarkMode } = useTheme();
  const [form] = Form.useForm();

  // mac备份配置
  const [macBackupConfig, setMacBackupConfig] = useState<IMacBackupConfig>({
    isBackup: false,
    path: '',
    sizeLimit: 0,
    unit: 'GB'
  });

  const [agreementIsOpen, setAgreementIsOpen] = useState<boolean>(false); // 确认是否已开启协议

  // 存储路径是否展开面板的开关状态
  const [storagePathOpenPlane, setStoragePathOpenPlane] = useState<boolean>(false);

  // 原始存储池信息
  const [poolData, setPoolData] = useState<StoragePool[]>([]);

  // 当前文件夹信息
  const [currentFileDirList, setCurrentFileDirList] = useState<FileItem[]>([]);

  // 当前所在的路径配置
  const [currentFileDirConfig, setCurrentFileDirConfig] = useState<{ dir_name: string; data_dir: string }>({ dir_name: '', data_dir: '' });

  // 查询文件夹的分页配置
  const page_config_ref = useRef<{ size: number, token: string }>({ size: 20, token: "" });

  // 当前选择的文件夹路径
  const [selectedValue, setSelectedValue] = useState<string>('');

  // webDav配置
  const [currentWebDav, setCurrentWebDav] = useState<WebDavInfo>(
    {
      alias_root: "/home/<USER>",
      password: "password",
      port: 5000,
      uri: "/",
      username: "user0"
    }
  );

  // 是否选择了用户信息
  const [isSelectedUser, setIsSelectedUser] = useState<boolean>(false);

  // 共享用户list
  const [sharedUserList, setSharedUserList] = useState<any[]>([
    { needPwd: true, userName: '共享账号1' },
    { needPwd: false, userName: '共享账号2' },
  ]);

  // 当前选择的用户信息
  const [selectedUser, setSelectedUser] = useState<any | undefined>();

  // 用户验证表单
  const [userForm] = Form.useForm();


  // 文件夹列表信息配置
  const file_dir_list: IFileDirBaseProps[] = useMemo(() => {
    if (currentFileDirList.length === 0 && currentFileDirConfig.dir_name === '' && currentFileDirConfig.data_dir === '') {
      return poolData.map(item => {
        return {
          id: `${currentWebDav.alias_root}${item.data_dir}`,
          dir_name: item.name,
          data_dir: item.data_dir,
          isDirectory: true
        }
      })
    }

    return currentFileDirList.map(item => {
      return {
        id: `${item.parent}/${item.name}`,
        dir_name: item.name,
        data_dir: `${item.parent}${item.name}/`,
        isDirectory: item.xattr?.directory || false
      }
    })
  }, [currentFileDirConfig.data_dir, currentFileDirConfig.dir_name, currentFileDirList, currentWebDav.alias_root, poolData])

  // 当选择的是顶部时使用存储池的数据
  const initData = useCallback(() => {
    setCurrentFileDirList([]);
    setCurrentFileDirConfig({ dir_name: '', data_dir: '' });
  }, [])

  // 获取存储池信息
  const { runAsync: fetchPoolInfo } = useRequest(getPoolInfo, { manual: true });

  // 获取子文件夹信息
  const { runAsync: fetchDirectoryList } = useRequest(listDirectory, { manual: true });

  const getDirectoryList = useCallback(async (path, dir_name, callback: (d: FileItem[]) => void) => {
    console.log(`开始获取文件夹列表: path:${path},dir_name: ${dir_name}`);

    const res = await fetchDirectoryList({
      page: page_config_ref.current, path: { parent: path, recursion: false }
    }).catch((e) => console.log('获取文件夹列表失败: ', e));

    if (res && res.code === 0) {
      callback(res.data.files);
      setCurrentFileDirConfig({ dir_name: dir_name || '', data_dir: path || '' });

      page_config_ref.current = { size: page_config_ref.current.size, token: res.data.page.token };
    } else {
      console.log('获取文件夹列表失败: ', JSON.stringify(res));
    }

    return res;

  }, [fetchDirectoryList])

  const getPoolInfoData = useCallback(async () => {
    const res = await fetchPoolInfo({}).catch((e) => console.log('获取存储池信息失败: ', e));
    if (res && res.code === 0) {
      if (res.data.internal_pool.length > 0) {
        setPoolData(res.data.internal_pool);
        setCurrentFileDirConfig({ dir_name: '', data_dir: '' });
        if (res.data.webDAV) setCurrentWebDav(res.data.webDAV);
      }
    } else {
      console.log('获取存储池信息失败: ', JSON.stringify(res));
    }
  }, [fetchPoolInfo])

  useUpdateEffect(() => {
    if (storagePathOpenPlane) {
      getPoolInfoData();
    }
  }, [storagePathOpenPlane])

  const finishCallback = useCallback(() => {
    if (macBackupConfig.isBackup && macBackupConfig.path === '') {
      Toast.show('请设置存储位置');
      return;
    }

    const configNotChange = macBackupConfig.path === '' && macBackupConfig.sizeLimit === 0 && macBackupConfig.isBackup === false;

    if (configNotChange || macBackupConfig.path !== '') {
      console.log(`保存成功,配置信息: ${JSON.stringify(macBackupConfig)}`);
    }
  }, [macBackupConfig])

  // 保存配置右侧icon
  const right = useMemo(() => {
    const configNotChange = macBackupConfig.path === '' && macBackupConfig.sizeLimit === 0 && macBackupConfig.isBackup === false;

    const disabled =
      !configNotChange &&
      ((macBackupConfig.isBackup && macBackupConfig.path === '') ||
        (macBackupConfig.isBackup === false && macBackupConfig.sizeLimit !== 0 && macBackupConfig.path === ''));

    const opacity = configNotChange ? 1
      : macBackupConfig.isBackup && macBackupConfig.path !== '' ? 1
        : macBackupConfig.isBackup && macBackupConfig.path === '' ? 0.2
          : macBackupConfig.isBackup === false && macBackupConfig.sizeLimit !== 0 && macBackupConfig.path === '' ? 0.2 : 1;
    return (
      <div style={{ opacity: opacity, pointerEvents: disabled ? 'none' : 'auto', display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
        <PreloadImage style={{ height: px2rem('40px'), display: macBackupConfig.isBackup ? 'flex' : 'none' }} onClick={finishCallback} src={!isDarkMode ? finish : finish_dark} alt='submit' />
      </div>
    )
  }, [finishCallback, isDarkMode, macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit])

  // 浮动面板选中后回调
  const planeFinish = useCallback(() => {
    console.log('浮动面板选中', selectedValue, '回调并关闭浮动面板');
    setMacBackupConfig(p => ({ ...p, path: selectedValue }));
    setStoragePathOpenPlane(false);
    setCurrentFileDirList([]);
    setCurrentFileDirConfig({ data_dir: '', dir_name: '' })
  }, [selectedValue])

  // 弹窗navBar的右侧icon
  const floatNavRight = useMemo(() => {
    if (selectedUser && selectedUser.needPwd) return <></>
    const opacity = selectedValue === '' ? 0.2 : 1; // 判断是否选中路径，未选中的不显示提交按钮
    return (
      <div style={{ opacity: opacity, pointerEvents: selectedValue === '' ? 'none' : 'auto' }}>
        <PreloadImage style={{ height: px2rem('40px') }} onClick={planeFinish} src={!isDarkMode ? finish : finish_dark} alt='submit' />
      </div>
    )
  }, [isDarkMode, planeFinish, selectedUser, selectedValue])

  const isBackupOnChange = useCallback((checked: boolean) => {
    if (!checked) {
      // 取消
      setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
      setIsSelectedUser(false);
      return;
    }

    if (!agreementIsOpen) {
      modalShow('开启SMB网络共享服务', <span>若要Time Machine功能正确发现局域网内的智能存储设备，需要开启SMB网络共享服务。确定开启？</span>, (m) => {
        // 确定开启 todo
        console.log('确认开启该服务协议')
        setAgreementIsOpen(true);
        m.destroy();
        setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
        console.log('成功开启，备份助手功能开启')
      }, () => null, false, { position: 'bottom' });
    } else {
      setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
    }
  }, [agreementIsOpen])

  useUpdateEffect(() => {
    form.setFieldsValue({ size: macBackupConfig.sizeLimit, unit: macBackupConfig.unit });
  }, [macBackupConfig.sizeLimit, macBackupConfig.unit]);

  // 打开容量限制弹窗
  const openSizeLimitModal = useCallback(() => {

    modalShow('容量限制', <NumberInput form={form} />, (m) => {
      form.validateFields().then(value => {
        setMacBackupConfig(prev => ({ ...prev, sizeLimit: value.size, unit: value.unit }));
      })
      m.destroy();
    }, () => null, false, { position: 'center' });
  }, [form])

  // 列表数据配置
  const optList: (IListData | null)[] = useMemo(() => {
    // 处理当前路径的label显示
    const path = macBackupConfig.path;
    let pathLabel = path;
    if (path !== '') {
      const pathStrings = path.split('/').filter(s => s !== '');
      const alias_root = `/${pathStrings[0]}/${pathStrings[1]}`;
      const data_dir = `/${pathStrings[2]}/${pathStrings[3]}`;
      const poolItem = poolData.find(it => it.data_dir === data_dir);
      pathStrings.splice(0, 4);

      if (poolItem && alias_root === currentWebDav.alias_root) {
        pathLabel = `${poolItem.name}/${pathStrings.join('/')}`;
      }
    }

    return [
      {
        key: 'open_mac_backup', type: 'switch', label: '开启Mac备份助手功能', render: () => {
          return <Switch value={macBackupConfig.isBackup} onChange={isBackupOnChange} />
        }
      },
      macBackupConfig.isBackup ? {
        key: 'back_path', type: 'text', 'label': '存储位置', render: () => {
          return <span className={`${styles.list_right_container} ${macBackupConfig.isBackup === false ? styles.list_right_container_disabled : ''}`} onClick={() => setStoragePathOpenPlane(true)}>
            <span className={styles.list_right_container_span}>{`${macBackupConfig.path === '' ? '未设置' : pathLabel}`}</span>
            <PreloadImage className={styles.list_right_container_img} src={isDarkMode ? next_dark : next} alt="next" />
          </span>
        }
      } : null,
      macBackupConfig.isBackup ? {
        key: 'back_size_limit', type: 'input', 'label': '容量限制', render: () => {
          return <span className={`${styles.list_right_container} ${macBackupConfig.isBackup === false ? styles.list_right_container_disabled : ''}`} onClick={openSizeLimitModal}>
            <span className={styles.list_right_container_span}>{`${macBackupConfig.sizeLimit === 0 ? '未设置' : `${macBackupConfig.sizeLimit}${macBackupConfig.unit}`}`}</span>
            <PreloadImage className={styles.list_right_container_img} src={isDarkMode ? next_dark : next} alt="next" />
          </span>
        }
      } : null
    ]
  }, [currentWebDav.alias_root, isBackupOnChange, isDarkMode, macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit, macBackupConfig.unit, openSizeLimitModal, poolData])

  const back2Index = useCallback(() => {
    const configNotChange = macBackupConfig.path === '' && macBackupConfig.sizeLimit === 0 && macBackupConfig.isBackup === false;

    const back = () => {
      console.log('退出设置');
    }

    if (!configNotChange) {
      modalShow('Mac备份助手未完成设置', <span>本页面有尚未保存的设置。确定退出？退出后将清空本页面内未保存的全部设置。</span>, (m) => {
        m.destroy();
        console.log('继续设置')
      }, () => {
        back();
      }, false, { position: 'bottom', cancelBtnText: '关闭退出', okBtnText: '继续设置', okBtnStyle: { background: 'var(--primary-color)', color: '#fff' } });
      return;
    }

    back();
  }, [macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit])

  // 面板关闭
  const floatClose = useCallback(() => {
    userForm.resetFields(); // 重置表单
    // 判断当前是否在登录用户中，如果是则返回上一级
    if (selectedUser && selectedUser.needPwd) {
      setIsSelectedUser(false); // 取消选择用户
      setSelectedUser(undefined); // 取消选择的用户信息
      return;
    }

    // 否则登录后不清除已选中用户
    setStoragePathOpenPlane(false); // 关闭路径选择面板
    setCurrentFileDirList([]); // 重置当前目录列表信息
    setCurrentFileDirConfig({ data_dir: '', dir_name: '' }); // 重置当前目录信息
  }, [selectedUser, userForm])

  // 登录共享用户
  const loginSharedUser = useCallback(() => {
    //todo

    userForm.validateFields().then(v => {
      console.log(v);
      userForm.resetFields(); // 重置表单
      setSelectedUser(undefined) // 登录成功后清空选择中的用户信息，保留已选中用户
    }).catch(e => console.log(e));
  }, [userForm])

  // floatPanel的content
  const floatPanelContent = useMemo(() => {
    // 选择共享用户
    if (!isSelectedUser) {
      return (sharedUserList.map((it, i) => (
        <ListCard key={`${it.userName}_${i}`} text={it.userName} subtext={it.needPwd ? '账号密码访问' : '无需密码访问'} onCallback={() => { setIsSelectedUser(true); setSelectedUser(it) }} />
      )))
    }

    if (selectedUser && selectedUser.needPwd) {
      return (
        <div className={styles.user_info_form_container}>
          <Form form={userForm}>
            <Form.Item name={'username'} label={'用户名'} rules={[{ required: true, message: '请输入用户名' }]}>
              <Input placeholder='请输入用户名' variant='underlined' autoComplete='off' />
            </Form.Item>
            <Form.Item name={'password'} label={'密码'} rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password placeholder='请输入密码' variant='underlined' autoComplete='off' />
            </Form.Item>
          </Form>
          <Button type='primary' onClick={loginSharedUser}>确定</Button>
        </div>
      )
    }

    return (
      <FileSelectorUtil onChange={setSelectedValue} setCurrentFileDirList={setCurrentFileDirList} data={{
        currentFileDirConfig: currentFileDirConfig,
        current_file_dir_list: file_dir_list,
        web_alias_root: currentWebDav.alias_root
      }} dirOnClick={getDirectoryList} initData={initData} requestPageOpt={page_config_ref} />
    )
  }, [currentFileDirConfig, currentWebDav.alias_root, file_dir_list, getDirectoryList, initData, isSelectedUser, loginSharedUser, selectedUser, sharedUserList, userForm])

  const floatPanelTitle = useMemo(() => {
    if (!isSelectedUser) return '选择共享账号';
    if (selectedUser && selectedUser.needPwd) return '登录共享账号';
    return '设置存储位置';
  }, [isSelectedUser, selectedUser])

  return (
    <div className={styles.root_container}>
      <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} right={right} onBack={back2Index} />
      <div className={styles.content}>
        <div className={styles.header}>
          <span className={styles.header_span}>Mac备份助手</span>
        </div>
        <div className={styles.body}>
          <div className={styles.show_backup_machine_container}>
            <PreloadImage className={styles.backup_machine_img} src={mac_show_img} alt='mac' />
            <PreloadImage className={styles.backup_machine_img} src={nas_show_img} alt='nas' />
          </div>
          <div className={styles.body_span_container}>
            <span className={styles.body_span}>开启MAC备份助手功能后，可通过MAC电脑-Time Machine功能，发现本机指定的磁盘路径，并将其设置为外部备份磁盘</span>
          </div>
        </div>
        <div className={styles.footer}>
          <List dataSource={optList.filter(it => it !== null)} />
        </div>
      </div>

      <FloatPanel showFloatPanel={storagePathOpenPlane} setShowFloatPanel={setStoragePathOpenPlane} className={styles.storage_plane} anchors={[0, 800]}>
        <div className={styles.float_panel_container}>
          <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} title={floatPanelTitle} right={floatNavRight} onBack={floatClose} />
          {floatPanelContent}
        </div>
      </FloatPanel>
    </div>
  )
}

export default MacBackup_App_Index_Page;