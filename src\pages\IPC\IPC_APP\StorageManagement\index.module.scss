.container {
  // background-color: #ffffff;
  // padding: 0 10px;
  min-height: calc(100vh - 35px);
  background-color: var(--background-color);

  .header {
    padding: 10px 20px 0;
    .backIcon {
      width: 40px;
      height: 40px;
    }
  }
  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 20px;
  }

  .cameraList {
    padding: 0 24px;
    --border-inner: none;
    --border-top: none;
    --border-bottom: none;
    --adm-color-background: transparent;

    :global {
      .adm-list-item {
        padding: 12px 0;
      }

      .adm-list-item-content-prefix {
        margin-right: 12px;
        border-radius: 16px;
      }

      .adm-list-item-content-main,
      .adm-list-item-title {
        color: var(--text-color);
        font-size: 17px;
        font-weight: 500;
      }

      .adm-list-item-content-arrow {
        color: #ccc;
      }
      a.adm-list-item:active:not(.adm-list-item-disabled) {
        background-color: var(--background-color);
      }
    }
  }

  .cameraImg {
    width: 48px !important;
    height: 48px !important;
    border-radius: 8px;
    object-fit: contain;
    flex-shrink: 0;
    min-width: 48px; // 确保最小宽度
    min-height: 48px; // 确保最小高度
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    // background-color: #f5f5f5;
  }
}
