import { Image } from "antd-mobile";
import { Switch } from "antd-mobile";
import { useHistory, useParams, useLocation } from "react-router-dom";
import { useStorage } from "../Context/storageContext";
import styles from "./index.module.scss";
import { useState, useEffect } from "react";
import { Input, Modal, Toast } from "antd-mobile";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import enterRight from "@/Resources/icon/enter_right.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import enterRightDark from "@/Resources/icon/enter_right_dark.png";
import { modalShow } from "@/components/List";
import {
  CameraInfo,
  cleanRecord,
  removeRecordCamera,
  renameDevice,
  setupRecordCamera,
  listRecordCamera,
} from "@/api/ipc";
import { useRequest, clearCache } from "ahooks";

import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { PreloadImage } from "@/components/Image";
interface LocationState {
  camera?: CameraInfo;
  isDataComplete?: boolean;
  ensureCompleteData?: () => void;
}

// 摄像机信息类型定义
const DeviceDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { threshold } = useStorage();
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { isDarkMode } = useTheme();

  // 获取传递的摄像机数据
  const [camera, setCamera] = useState<CameraInfo | null>(null);

  // 处理摄像机图标路径的函数
  const processIconPath = (icon: string): string => {
    if (!icon) return '';

    const origin = window.location.origin;
    const pathname = window.location.pathname;
    const needPath = pathname.split('/').slice(1, 4).join('/'); // APP端路径处理

    return `${origin}/${needPath}/${icon}`;
  };

  // 优先使用外部传递的数据或缓存数据，只有在都没有时才调用接口
  const cacheKey = `device_detail_camera_${id}`;

  useRequest(() => listRecordCamera({ did: [id] }, { showLoading: false }), {
    // 只有在没有外部数据且没有缓存数据时才执行接口调用
    ready: !location.state?.camera && !sessionStorage.getItem(cacheKey),
    onSuccess: (res) => {
      const cam = res.data?.camera?.[0];
      if (cam) {
        setCamera(cam);
        // 缓存接口获取的数据
        sessionStorage.setItem(cacheKey, JSON.stringify(cam));
      }
    },
    onError: (error) => {
      Toast.show({
        content: "获取摄像机数据失败",
        position: "bottom",
      });
    },
    refreshDeps: [id],
  });

  // 数据初始化：优先使用外部数据，其次使用缓存数据
  useEffect(() => {
    if (location.state?.camera) {
      // 外部传递的数据优先级最高
      setCamera(location.state.camera);
      // 更新缓存
      sessionStorage.setItem(cacheKey, JSON.stringify(location.state.camera));

      // 检查数据完整性，如果数据不完整则静默获取完整数据
      const isDataComplete = location.state.isDataComplete;
      const ensureCompleteData = location.state.ensureCompleteData;
      if (isDataComplete === false && ensureCompleteData) {
        ensureCompleteData();
      }
    } else {
      // 尝试从缓存中获取数据
      const cachedCamera = sessionStorage.getItem(cacheKey);
      if (cachedCamera) {
        try {
          setCamera(JSON.parse(cachedCamera));
        } catch (error) {
          console.error('解析缓存数据失败:', error);
          // 清除无效缓存
          sessionStorage.removeItem(cacheKey);
        }
      }
    }
  }, [location.state?.camera, location.state?.isDataComplete, location.state?.ensureCompleteData, cacheKey]);

  // 监听父组件的数据更新事件
  useEffect(() => {
    const handleCameraDataUpdated = (event: CustomEvent) => {
      const { cameras, isComplete } = event.detail;
      if (isComplete && cameras) {
        // 找到当前设备的最新数据
        const updatedCamera = cameras.find((cam: CameraInfo) => cam.did === id);
        if (updatedCamera) {
          setCamera(updatedCamera);
          // 更新缓存
          sessionStorage.setItem(cacheKey, JSON.stringify(updatedCamera));
        }
      }
    };

    window.addEventListener('cameraDataUpdated', handleCameraDataUpdated as EventListener);

    return () => {
      window.removeEventListener('cameraDataUpdated', handleCameraDataUpdated as EventListener);
      // 在页面卸载时清理缓存
      // sessionStorage.removeItem(cacheKey);
    };
  }, [id, cacheKey]);

  const [isRecording, setIsRecording] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [remark, setRemark] = useState("");
  const [originalRemark, setOriginalRemark] = useState(""); // 保存原始备注值
  const [, setIsInputFocused] = useState(false);

  // 初始化备注
  useEffect(() => {
    if (camera) {
      const cameraName = camera.name || "";
      setRemark(cameraName);
      setOriginalRemark(cameraName); // 同时保存原始值
      setIsRecording(camera.record_enabled || false);

      // 页面曝光埋点
      window.onetrack?.('track', 'ipc_deviceDetail_expose');

      // 存储空间统计埋点
      if (camera.space_limit && camera.used_space) {
        window.onetrack?.('track', 'ipc_deviceDetail_storageSpace_count', {
          space_limit: camera.space_limit,
          used_space: camera.used_space
        });
      }
    }
  }, [camera]);

  // 计算存储使用百分比和颜色
  const storageUsed = camera ? camera.used_space / 1024 : 0; // 转换为GB
  const storageTotal = camera ? camera.space_limit / 1024 : 0; // 转换为GB

  const { loading: renamingLoading, run: runRenameDevice } = useRequest(
    renameDevice,
    {
      manual: true,
      debounceWait: 300,
      onSuccess: (response) => {
        if(response && (response.code !== 0)){
          Toast.show({
            content: response?.result,
            position: "bottom",
          });
          return;
        }
        // 关闭弹窗
        setIsModalVisible(false);

        // 更新本地摄像机数据和原始备注值
        if (camera) {
          const updatedCamera = {
            ...camera,
            name: remark,
          };
          setCamera(updatedCamera);
          setOriginalRemark(remark); // 更新原始值为新的备注
          // 同步更新缓存
          sessionStorage.setItem(cacheKey, JSON.stringify(updatedCamera));
        }

        // 显示成功提示
        Toast.show({
          content: "备注修改成功",
          position: "bottom",
        });
      },
      onError: (error) => {
        console.error("设备备注修改失败:", error);

        // 关闭弹窗
        setIsModalVisible(false);

        // 显示失败提示
        Toast.show({
          content: "备注修改失败，请重试",
          position: "bottom",
        });
      },
    }
  );

  // 添加录制开关
  const { run: runSetupRecordCamera } = useRequest(
    setupRecordCamera,
    {
      manual: true,
      debounceWait: 300,
      onSuccess: () => {
        Toast.show({
          content: "录制状态修改成功",
          position: "bottom",
        });

        // 更新本地摄像机数据
        if (camera) {
          const updatedCamera = {
            ...camera,
            record_enabled: isRecording,
          };
          setCamera(updatedCamera);
          // 同步更新缓存
          sessionStorage.setItem(cacheKey, JSON.stringify(updatedCamera));
        }
      },
      onError: (error) => {
        Toast.show({
          content: "录制状态修改失败，请重试",
          position: "bottom",
        });
        setIsRecording(!isRecording);
      },
    }
  );

  const { run: runCleanRecord } = useRequest(cleanRecord, {
    manual: true,
    debounceWait: 300,
    onSuccess: (res) => {
      if(res && (res.code !== 0)){
        Toast.show({
          content: res?.result,
          position: "bottom",
        });
        return;
      }
      Toast.show({ content: "清空成功", duration: 2000 });
      setCamera((prev) => {
        const updatedCamera = prev
          ? {
            ...prev,
            used_space: 0,
            record_period: 0,
          }
          : null;

        // 同步更新缓存
        if (updatedCamera) {
          sessionStorage.setItem(cacheKey, JSON.stringify(updatedCamera));
        }

        return updatedCamera;
      });
      // 清空存档成功后，清除事件回看数据缓存
      clearCache('event-replay-data');
    },
    onError: (error) => {
      Toast.show({ content: `清空失败：${error.message}` });
    },
  });

  const { run: runRemoveRecord } = useRequest(
    removeRecordCamera,
    {
      manual: true,
      debounceWait: 300,
      onSuccess: (res) => {
        if(res && (res.code !== 0)){
          Toast.show({
            content: res?.result,
            position: "bottom",
          });
          return;
        }
        Toast.show({ content: "解绑成功", duration: 2000 });
         history.push({
          pathname: "/cameraManagement_app",
          state: { shouldRefresh: true }
    });
      },
      onError: (error) => {
        Toast.show({ content: `解绑失败：${error.message}` });
      },
    }
  );

  const handleClearArchive = () => {
    if (!camera) return;
    modalShow(
      "清空存档",
      <div className={styles.modalConfirmText}>
        清空后无法恢复,是否确认清空？
      </div>,
      (m) => {
        m.destroy();
        runCleanRecord({ camera: [camera.did] });
      },
      () => { }, // onCancel
      false, // onlyShow
      {
        okBtnText: "清空",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom",
      }
    );
  };

  const handleUnbindDevice = () => {
    if (!camera) return;
    modalShow(
      "是否确认解绑设备",
      <div />,
      (m) => {
        m.destroy();
        runRemoveRecord({ camera: [camera.did] });
        Toast.show({
          content: "解绑成功",
          duration: 2000,
        });
      },
      () => { }, // onCancel
      false, // onlyShow
      {
        okBtnText: "解绑",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom",
      }
    );
  };

  const getStorageColor = () => {
    const usagePercentage = (storageUsed / storageTotal) * 100;
    // 暗黑模式下的颜色
    if (isDarkMode) {
      if (threshold >= 100) return "var(--list-value-text-color)";
      return usagePercentage > threshold
        ? "var(--emergency-text-color)"
        : "var(--list-value-text-color)";
    }

    // 明亮模式下的颜色
    if (threshold >= 100) return "var(--list-value-text-color)";
    return usagePercentage > threshold
      ? "var(--emergency-text-color)"
      : "var(--list-value-text-color)";
  };

  // 打开备注编辑弹窗
  const handleOpenRemarkModal = () => {
    // 打开弹窗时重新设置为当前的备注值
    setRemark(originalRemark);
    setIsModalVisible(true);
  };

  // 取消编辑备注
  const handleCancelRemark = () => {
    // 恢复到原始值
    setRemark(originalRemark);
    setIsModalVisible(false);
  };

  const handleSaveRemark = () => {
    if (!camera) {
      Toast.show({
        content: "设备信息不存在",
        position: "bottom",
      });
      setIsModalVisible(false);
      return;
    }

    // 如果备注为空，提示用户
    // if (!remark.trim()) {
    //   Toast.show({
    //     content: '备注不能为空',
    //     position: 'bottom',
    //   });
    //   return;
    // }

    // 如果备注未修改，直接关闭弹窗
    if (remark === originalRemark) {
      setIsModalVisible(false);
      return;
    }

    // 添加重命名埋点
    window.onetrack?.('track', 'ipc_deviceDetail_rename_submit', { remarkName: remark });

    // 调用重命名接口
    runRenameDevice({
      name: remark,
      did: camera.did,
    });
  };

  const getRecordPeriodText = () => {
    if (!camera || !camera.record_period) return "0天0小时0分钟";

    const totalMinutes = camera.record_period;
    const days = Math.floor(totalMinutes / 1440);
    const hours = Math.floor((totalMinutes % 1440) / 60);
    const minutes = totalMinutes % 60;

    return `${days}天${hours}小时${minutes}分钟`;
  };

  return (
    <div className={styles.container}>
      <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
      <div className={styles.title}>设备详情</div>
      {
         
           <div>
         <div className={styles.deviceInfo}>
        <PreloadImage src={processIconPath(camera?.icon || "")} className={styles.cameraImg}
            style={{
              width: 48,
              height: 48,
              objectFit: 'contain'
            }}/>
        <div className={styles.deviceName}>
          <div>{camera?.name || ""}</div>
          {/* <div className={styles.networkInfo}>
            <span>
              <Image src={higDownload} className={styles.highest} />
              -- KB/s
            </span>
            <span>
              <Image src={minDownload} className={styles.minimum} />0 KB/s
            </span>
          </div> */}
        </div>
      </div>
      <div className={styles.thinLine} />
      <div className={styles.toggleSection}>
        <span>开启录制</span>
        <Switch
          className={styles.toggleSwitch}
          checked={isRecording}
          onChange={(value) => {
            setIsRecording(value);

            // 添加埋点
            if (value) {
              window.onetrack?.('track', 'ipc_deviceDetail_isRecord_enable');
            } else {
              window.onetrack?.('track', 'ipc_deviceDetail_isRecord_disable');
            }

            if (camera) {
              runSetupRecordCamera({
                camera: [camera.did],
                config: {
                  record_enabled: value,
                },
              });
            }
          }}
        />
      </div>
      <div className={styles.thinLine} />

      <div className={styles.infoList}>
        <div className={styles.information}>设备信息</div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>备注</span>
          <span
            className={styles.infoValue}
            onClick={handleOpenRemarkModal}
          >
            {remark}
            <Image
              src={isDarkMode ? enterRightDark : enterRight}
              className={styles.arrow}
            />
          </span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>IP地址</span>
          <span className={styles.infoValue}>{camera?.ip}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>MAC地址</span>
          <span className={styles.infoValue}>{camera?.mac}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>已录制时长</span>
          <span className={styles.infoValue}>{getRecordPeriodText()}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.infoLabel}>占用存储空间</span>
          <span
            className={styles.infoValue}
            style={{ color: getStorageColor() }}
          >
            <span>{storageUsed.toFixed(2)}GB</span>/{storageTotal.toFixed(2)}GB
          </span>
        </div>
      </div>
      <div className={styles.thinLine} />

      <div className={styles.manage}>设备管理</div>

      <div
        className={styles.manageSection}
        onClick={() =>
          history.push({
            pathname: "/cameraManagement_app/storageManagement/recordConfig",
            state: { camera }
          })
        }
      >
        <span>录制配置</span>
        <Image src={isDarkMode ? enterRightDark : enterRight} style={{width:18,height:18}}/>
      </div>
      <div className={styles.buttonGroup}>
        <button className={styles.clearButton} onClick={handleClearArchive}>
          清空存档
        </button>
        <button className={styles.unbindButton} onClick={handleUnbindDevice}>
          解绑设备
        </button>
      </div>

      <Modal
        className={styles.modalBox}
        visible={isModalVisible}
        content={
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>备注</div>
            <Input
              className={styles.remarkInput}
              value={remark}
              onChange={setRemark}
              placeholder="请输入备注"
              onFocus={() => setIsInputFocused(true)}
              onBlur={() => setIsInputFocused(false)}
              autoFocus
              clearable
            />
            <div className={styles.modalFooter}>
              <button
                className={styles.cancelButton}
                onClick={handleCancelRemark}
              >
                取消
              </button>
              <button
                className={`${styles.confirmButton} ${
                  remark.trim() === '' ? styles.btnDisabled : ""
                }`}
                onClick={handleSaveRemark}
                disabled={renamingLoading || remark.trim() === ''}
              >
                确定
              </button>
            </div>
          </div>
        }
        closeOnAction
      />
       </div>
      }
      
    </div>
  );
};

export default DeviceDetail;
