import React from "react";
import ReactDOM from "react-dom";
import { <PERSON>hRouter as Router } from "react-router-dom";
import "@/index.css";
import "@/assets/index.css";
import App from "./App";
import ThemeDetector from "./utils/themeDetector";
import { AliveScope } from 'react-activation'; // 引入AliveScope组件，用于保持组件状态不被卸载

ReactDOM.render(
  <React.StrictMode>
    <Router>
      <ThemeDetector>
        <AliveScope>
          <App />
        </AliveScope>
      </ThemeDetector>
    </Router>
  </React.StrictMode>,
  document.getElementById('root')
);
