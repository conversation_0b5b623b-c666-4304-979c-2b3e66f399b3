import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import NavigatorBar from "@/components/NavBar";
import styles from "./index.module.scss";
import filterIcon from "@/Resources/icon/videoEdit.png";
import filterDarkIcon from "@/Resources/icon/videoEdit.png";
import dateSelect from '@/Resources/icon/dateSelect.png';
import nullData from "@/Resources/icon/null-page.png";
import nullDataDark from "@/Resources/icon/null-page-dark.png";
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import { useHistory, useLocation } from "react-router-dom";
import PopoverSelector from "@/components/PopoverSelector";
import DatePicker from "@/components/CameraPlayer/components/DatePicker/DatePicker";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useRequest, useInViewport } from "ahooks";
import { listRecordCamera, CameraInfo, getVideoRecord } from "@/api/ipc";
import VideoEditModal from "./VideoEditModal";
import { downloadFiles } from "@/api/fatWallJSBridge";
import { Toast } from "@/components/Toast/manager";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import { goToIPCPlayBack } from "@/api/cameraPlayer";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import event_error_img_dark from "@/Resources/camera_poster/camera_manager_eventLookback_poster_dark.png";
import event_error_img from "@/Resources/camera_poster/camera_manager_eventLookback_poster.png";

// 视频项接口，基于getVideoRecord接口返回的数据结构
interface VideoItem {
  camera_lens: string;
  event_name: string;
  time: string; // 时间戳
  media_duration: number;
  file: string;
  create_time: string;
  face_info: {
    uuid: string;
    name: string;
    profile_pic: string;
  }[];
  // 计算属性
  timeLabel?: string; // 显示的时间，如 "14:00"
  thumbnail?: string; // 缩略图
  cover_file?: string;
}

// 摄像机选项接口
interface CameraOption {
  label: string;
  value: string;
}

// 默认摄像机选项数据（作为fallback）
const defaultCameraOptions: CameraOption[] = [
  { label: "请选择摄像机", value: "" },
];
interface LocationState {
  cameraList?: CameraInfo[];
  isDataComplete?: boolean;
  ensureCompleteData?: () => void;
}

export default function VideoList() {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const location = useLocation<LocationState>();

  // 视频数据状态
  const [videoData, setVideoData] = useState<VideoItem[]>();
  const [hasMore, setHasMore] = useState<boolean>(true);
  const pageInfoRef = useRef({ size: 20, token: "" });

  // 无限滚动相关
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [inViewport] = useInViewport(loadMoreRef);

  // 摄像机数据状态
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  const [cameraOptions, setCameraOptions] = useState<CameraOption[]>(defaultCameraOptions);

  // 摄像机选择器状态
  const [selectedCamera, setSelectedCamera] = useState<string>("");
  const [cameraPopoverVisible, setCameraPopoverVisible] = useState<boolean>(false);

  // 用于跟踪是否是数据更新导致的摄像机变化，避免不必要的视频重新加载
  const isDataUpdatingRef = useRef<boolean>(false);

  // 日期选择器状态
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date()); // 初始值为今天
  const [datePickerVisible, setDatePickerVisible] = useState<boolean>(false);

  // 缩略图状态
  const [videoThumbnails, setVideoThumbnails] = useState<Map<string, string>>(new Map()); // 存储每个视频的缩略图

  // 编辑模态框状态
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);

  // 初始化摄像机数据 - 优先使用传递的数据
  useEffect(() => {
    const passedCameraList = location.state?.cameraList;
    const isDataComplete = location.state?.isDataComplete;
    const ensureCompleteData = location.state?.ensureCompleteData;

    if (passedCameraList && passedCameraList.length > 0) {
      // 使用传递的摄像机列表数据
      setCameraList(passedCameraList);

      // 转换为选项格式
      const options: CameraOption[] = passedCameraList.map((camera) => ({
        label: camera.name,
        value: camera.did,
      }));

      if (options.length > 0) {
        setCameraOptions(options);
        // 默认选择第一个摄像机
        setSelectedCamera(options[0].value);
      }

      // 检查数据完整性，如果数据不完整则静默获取完整数据
      if (isDataComplete === false && ensureCompleteData) {
        ensureCompleteData();
      }
    }
  }, [location.state?.cameraList, location.state?.isDataComplete, location.state?.ensureCompleteData]);

  // 监听父组件的数据更新事件
  useEffect(() => {
    const handleCameraDataUpdated = (event: CustomEvent) => {
      const { cameras, isComplete } = event.detail;
      if (isComplete && cameras) {
        // 标记正在进行数据更新
        isDataUpdatingRef.current = true;

        setCameraList(cameras);

        // 更新摄像机选项
        const options: CameraOption[] = cameras.map((camera: CameraInfo) => ({
          label: camera.name,
          value: camera.did,
        }));

        if (options.length > 0) {
          setCameraOptions(options);
          // 只有当前选择的摄像机不在新列表中时才改变选择
          // 这样可以避免不必要的selectedCamera变化，从而避免触发视频数据重新加载
          if (selectedCamera && !options.find(opt => opt.value === selectedCamera)) {
            setSelectedCamera(options[0].value);
          } else if (!selectedCamera && options.length > 0) {
            // 如果还没有选择摄像机，选择第一个
            setSelectedCamera(options[0].value);
          }
        }

        // 延迟重置标记，确保相关的useEffect不会被触发
        setTimeout(() => {
          isDataUpdatingRef.current = false;
        }, 100);
      }
    };

    window.addEventListener('cameraDataUpdated', handleCameraDataUpdated as EventListener);

    return () => {
      window.removeEventListener('cameraDataUpdated', handleCameraDataUpdated as EventListener);
    };
  }, [selectedCamera]);

  // 生成缩略图URL - 使用cover_file字段拼接/original.jpg
  const generateThumbnailUrl = useCallback((coverFile: string) => {
    if (!coverFile) return '';
    return `${coverFile}/original.jpg`;
  }, []);

  // 设置所有视频的缩略图URL - 使用cover_file字段
  const setAllThumbnails = useCallback(() => {
    if (videoData && videoData.length > 0) {
      const newThumbnails = new Map<string, string>();

      videoData.forEach(video => {
        if (video.cover_file) {
          const videoKey = `${video.camera_lens}-${video.time}`;
          const thumbnailUrl = generateThumbnailUrl(video.cover_file);
          newThumbnails.set(videoKey, thumbnailUrl);
        }
      });

      setVideoThumbnails(newThumbnails);
    }
  }, [videoData, generateThumbnailUrl]);



  // 请求摄像机列表 - 只在没有传递数据时调用
  useRequest(
    () => listRecordCamera({ did: [] }),
    {
      // 只有在没有传递摄像机数据时才自动执行
      ready: !location.state?.cameraList || location.state.cameraList.length === 0,
      onSuccess: (res) => {
        if (res.code === 0 && res.data?.camera) {
          const cameras = res.data.camera;
          setCameraList(cameras);

          // 转换为选项格式，使用model字段作为显示名称
          const options: CameraOption[] = cameras.map((camera) => ({
            label: camera.name,
            value: camera.did,
          }));

          if (options.length > 0) {
            setCameraOptions(options);
            // 如果当前选择的摄像机不在新列表中，选择第一个
            if (!options.find(opt => opt.value === selectedCamera)) {
              setSelectedCamera(options[0].value);
            }
          }
        }
      },
      onError: (err) => {
        console.error("获取摄像机列表失败:", err);
        // 保持使用默认数据
        setCameraOptions(defaultCameraOptions);
      },
    }
  );

  // 生成camera_lens参数
  const generateCameraLensParams = useMemo(() => {
    const selectedCameraInfo = cameraList.find(camera => camera.did === selectedCamera);
    if (selectedCameraInfo && selectedCameraInfo.key_frame.length > 0) {
      return selectedCameraInfo.key_frame.map(frame => `${selectedCameraInfo.did}_${frame.lens_id}`);
    }
    return []; // 返回空数组而不是包含空字符串的数组
  }, [selectedCamera, cameraList]);

  // 请求视频数据
  const { run: fetchVideoData } = useRequest(
    (params: any) => getVideoRecord(params),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          // 确保videos是数组，如果不存在则设为空数组
          const videosArray = res.data.videos || [];
          const videos = videosArray.map((video) => ({
            ...video,
            timeLabel: generateTimeRangeLabel(video.time, video.media_duration),
          }));

          // 处理分页逻辑
          if (pageInfoRef.current.token === "") {
            // 首次加载或重置，直接设置数据（包括空数组）
            setVideoData(videos);
          } else {
            // 加载更多，追加数据
            setVideoData((prevData) => [...(prevData || []), ...videos]);
          }

          // 更新分页token
          if (res.data.page && res.data.page.token) {
            pageInfoRef.current = { ...pageInfoRef.current, token: res.data.page.token };
          }

          // 检查是否还有更多数据
          if (videos.length < pageInfoRef.current.size) {
            setHasMore(false);
          } else {
            setHasMore(true);
          }
        }
      },
      onError: (err) => {
        console.error("获取视频数据失败:", err);
      },
    }
  );

  // 当视频数据更新后设置缩略图
  useEffect(() => {
    if (videoData && videoData.length > 0) {
      // 直接设置缩略图URL，无需延迟
      setAllThumbnails();
    }
  }, [videoData, setAllThumbnails]);

  // 获取当前选择的摄像机标签
  const selectedCameraLabel = useMemo(() => {
    const camera = cameraOptions?.find(option => option.value === selectedCamera);
    return camera?.label || "摄像机01";
  }, [selectedCamera, cameraOptions]);

  // 格式化日期显示
  const dateDisplayText = useMemo(() => {
    if (!selectedDate) {
      return "选择日期"; // 未选择日期时的默认显示
    }

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    // 判断是否为今天
    if (selectedDate.toDateString() === today.toDateString()) {
      return "今天";
    }

    // 判断是否为昨天
    if (selectedDate.toDateString() === yesterday.toDateString()) {
      return "昨天";
    }

    // 判断是否为今年
    if (selectedDate.getFullYear() === today.getFullYear()) {
      return format(selectedDate, 'M月dd日', { locale: zhCN });
    }

    // 其他年份显示年月日
    return format(selectedDate, 'yyyy年M月dd日', { locale: zhCN });
  }, [selectedDate]);

  // 统一的视频数据获取逻辑
  useEffect(() => {
    // 如果正在进行数据更新，跳过视频数据请求，避免不必要的loading
    if (isDataUpdatingRef.current) {
      return;
    }

    // 确保有有效的camera_lens参数（不为空且不是空字符串）
    if (generateCameraLensParams.length > 0 && generateCameraLensParams.every(lens => lens.trim() !== "")) {
      // 重置分页信息（当摄像机或日期改变时）
      pageInfoRef.current = { size: 20, token: "" };
      setHasMore(true);

      let params;

      if (selectedDate) {
        // 如果选择了日期，获取指定日期的视频数据
        const startOfDay = new Date(selectedDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(selectedDate);
        endOfDay.setHours(23, 59, 59, 999);

        params = {
          page: pageInfoRef.current,
          options: {
            option: ["camera_lens", "time"],
            camera_lens: generateCameraLensParams,
            time: {
              start: Math.floor(startOfDay.getTime()).toString(),
              end: Math.floor(endOfDay.getTime()).toString(),
            },
          },
        };
      } else {
        // 如果没有选择日期，获取所有视频数据
        params = {
          page: pageInfoRef.current,
          options: {
            option: ["camera_lens"],
            camera_lens: generateCameraLensParams,
          },
        };
      }

      fetchVideoData(params);
    }
  }, [selectedCamera, selectedDate, generateCameraLensParams, fetchVideoData]);

  // 页面曝光埋点
  useEffect(() => {
    window.onetrack?.('track', 'ipc_videoList_expose');
  }, []);

  // 加载更多数据
  const loadMoreData = useCallback(() => {
    if (!hasMore) return;

    // 确保有有效的camera_lens参数
    if (generateCameraLensParams.length === 0 || !generateCameraLensParams.every(lens => lens.trim() !== "")) {
      return;
    }

    // 使用当前的查询参数加载更多数据
    if (selectedDate) {
      const startOfDay = new Date(selectedDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(selectedDate);
      endOfDay.setHours(23, 59, 59, 999);

      const params = {
        page: pageInfoRef.current,
        options: {
          option: ["camera_lens", "time"],
          camera_lens: generateCameraLensParams,
          time: {
            start: Math.floor(startOfDay.getTime()).toString(),
            end: Math.floor(endOfDay.getTime()).toString(),
          },
        },
      };
      fetchVideoData(params);
    } else {
      const params = {
        page: pageInfoRef.current,
        options: {
          option: ["camera_lens"],
          camera_lens: generateCameraLensParams,
        },
      };
      fetchVideoData(params);
    }
  }, [hasMore, selectedDate, generateCameraLensParams, fetchVideoData]);

  // 无限滚动监听
  useEffect(() => {
    if (inViewport && hasMore && videoData && videoData.length > 0) {
      loadMoreData();
    }
  }, [inViewport, hasMore, videoData, loadMoreData]);

  const handleBack = () => {
    history.goBack()
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setDatePickerVisible(false);
  };

  // 打开编辑模态框
  const handleOpenEditModal = () => {
    setEditModalVisible(true);
  };

  // 关闭编辑模态框
  const handleCloseEditModal = () => {
    setEditModalVisible(false);
  };

  // 保存到本机
  const handleSaveToLocal = (selectedVideos: VideoItem[]) => {
    if (selectedVideos.length === 0) {
      Toast.show('请选择要下载的视频');
      return;
    }

    // 构建文件信息数组
    const fileList = selectedVideos.map(video => ({
      name: video.file.split('/').pop() || '', // 从路径中提取文件名
      path: video.file,
    }));

    downloadFiles(fileList, (res) => {
      if (res && res.code === 0) {
        Toast.show('正在下载');
      } else {
        Toast.show('下载失败，请稍后再试');
      }
    });
  };

  // 删除视频
  const handleDeleteVideos = (selectedVideos: VideoItem[]) => {
    console.log('删除视频:', selectedVideos);

    // 删除成功后重新获取视频数据
    const refreshVideoData = () => {
      // 确保有有效的camera_lens参数
      if (generateCameraLensParams.length === 0 || !generateCameraLensParams.every(lens => lens.trim() !== "")) {
        return;
      }

      // 重置分页信息
      pageInfoRef.current = { size: 20, token: "" };
      setHasMore(true);

      if (selectedDate) {
        // 如果有选择日期，使用时间参数获取数据
        const dateStr = format(selectedDate, 'yyyy-MM-dd');
        const params = {
          page: pageInfoRef.current,
          options: {
            option: ["camera_lens", "time"],
            camera_lens: generateCameraLensParams,
            time: dateStr,
          },
        };
        fetchVideoData(params);
      } else {
        // 如果没有选择日期，使用默认参数获取数据
        const params = {
          page: pageInfoRef.current,
          options: {
            option: ["camera_lens"],
            camera_lens: generateCameraLensParams,
          },
        };
        fetchVideoData(params);
      }
    };

    // 延迟一下再刷新，确保删除操作完成
    setTimeout(refreshVideoData, 200);
  };

  // 生成时间范围标签（开始时间-结束时间）
  const generateTimeRangeLabel = (startTimestamp: string, durationSeconds: number): string => {
    try {
      const startTime = parseInt(startTimestamp); // 毫秒时间戳
      const endTime = startTime + (durationSeconds * 1000); // 将秒转换为毫秒

      const startDate = new Date(startTime);
      const endDate = new Date(endTime);

      const startTimeStr = format(startDate, 'HH:mm');
      const endTimeStr = format(endDate, 'HH:mm');

      return `${startTimeStr}-${endTimeStr}`;
    } catch (error) {
      console.error('时间范围标签生成失败:', error);
      return '00:00-00:00';
    }
  };

  // 视频回放
  const lookBackDetail = useCallback(
    (video: VideoItem) => {
      // 调用客户端播放器，传入当前视频数据
      goToIPCPlayBack([video], 0, "Storage")
        .then(() => {
          console.log('跳转播放成功');
        })
        .catch((error) => {
          console.error('跳转播放失败:', error);
        });
    },
    []
  );

  // 渲染空状态
  const renderEmptyState = () => (
    <div className={styles.emptyState}>
      <div className={styles.emptyIcon}>
        <PreloadImage
          src={isDarkMode ? nullDataDark : nullData}
          alt="暂无数据"
        />
      </div>
      <div className={styles.emptyText}>暂无内容</div>
    </div>
  );

  const renderVideoItem = (video: VideoItem, index: number) => {
    const videoKey = `${video.camera_lens}-${video.time}`;
    const thumbnailUrl = videoThumbnails.get(videoKey);

    return (
      <div key={`${video.camera_lens}-${video.time}-${index}`} className={styles.videoItem}>
        <div className={styles.thumbnailContainer}>
          <div className={styles.thumbnail} onClick={() => lookBackDetail(video)}>
            {thumbnailUrl ? (
              // 显示真实的缩略图
              <PreloadImage
                src={splitURL(thumbnailUrl)}
                alt=""
                className={styles.thumbnailImage}
                needHeader={true}
                errorImage={isDarkMode ? event_error_img_dark : event_error_img}
              />
            ) : null}
            {/* 占位图或加载状态 */}
            <div className={`${styles.thumbnailPlaceholder} ${thumbnailUrl ? styles.hidden : ''}`}>
            </div>

            {/* 播放图标  */}
            {thumbnailUrl && (
              <div className={styles.playIcon}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" fill="rgba(255,255,255,0.8)" />
                  <polygon points="10,8 16,12 10,16" fill="#333" />
                </svg>
              </div>
            )}
          </div>
        </div>
        <div className={styles.timeLabel}>{generateTimeRangeLabel(video.time, video.media_duration) || video.timeLabel }</div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <NavigatorBar
        backIcon={isDarkMode ? arrowLeftDark : arrowLeft}
        onBack={handleBack}
        right={
          videoData && videoData.length > 0 ? (
            <div onClick={handleOpenEditModal} style={{ cursor: 'pointer' }}>
              <PreloadImage
                src={isDarkMode ? filterDarkIcon : filterIcon}
                style={{ width: '40px', height: '40px' }}
              />
            </div>
          ) : (
            <div style={{ cursor: 'not-allowed', opacity: 0.5 }}>
              <PreloadImage
                src={isDarkMode ? filterDarkIcon : filterIcon}
                style={{ width: '40px', height: '40px' }}
              />
            </div>
          )
        }
      />
      <div className={styles.title}>存储管理</div>

      {/* 选择器容器 */}
      <div className={styles.selectorContainer}>
        {/* 摄像机选择器 */}
        <PopoverSelector
          visible={cameraPopoverVisible}
          onVisibleChange={setCameraPopoverVisible}
          value={selectedCamera}
          options={cameraOptions}
          onChange={setSelectedCamera}
          placement="bottom-start"
        >
          <div className={styles.selector}>
            <span className={styles.selectorText}>{selectedCameraLabel}</span>
            <PreloadImage
              src={dateSelect}
              className={styles.selectorArrow}
            />
          </div>
        </PopoverSelector>

        {/* 日期选择器 */}
        <div className={styles.selector} onClick={() => setDatePickerVisible(true)}>
          <span className={styles.selectorText}>{dateDisplayText}</span>
          <PreloadImage
            src={dateSelect}
            className={styles.selectorArrow}
          />
        </div>
      </div>

      {/* 视频网格 */}
      <div className={styles.content}>
        {videoData && videoData.length > 0 ? (
          <>
            <div className={styles.videoGrid}>
              {videoData.map((video, index) => renderVideoItem(video, index))}
            </div>

            {/* 无限滚动检测元素 */}
            {hasMore && (
              <div ref={loadMoreRef} className={styles.loadingTrigger}>
                <div className={styles.loadingIndicator}>
                  加载中...
                </div>
              </div>
            )}

            {/* 没有更多数据提示 */}
            {!hasMore && videoData.length > 0 && (
              <div className={styles.noMoreData}>
                没有更多数据了
              </div>
            )}
          </>
        ) : (
          renderEmptyState()
        )}
      </div>

      {/* 日期选择器弹窗 */}
      <DatePicker
        isShow={datePickerVisible}
        onCancel={() => setDatePickerVisible(false)}
        onSelect={handleDateSelect}
      />

      {/* 编辑模态框 */}
      <VideoEditModal
        visible={editModalVisible}
        onClose={handleCloseEditModal}
        videoData={videoData || []}
        videoThumbnails={videoThumbnails}
        onSaveToLocal={handleSaveToLocal}
        onDelete={handleDeleteVideos}
        generateTimeRangeLabel={generateTimeRangeLabel}
      />
    </div>
  );
}
