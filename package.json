{"name": "my-app", "version": "0.1.0", "private": true, "homepage": ".", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@testing-library/user-event": "^13.5.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.24.6", "antd-mobile": "^5.38.1", "axios": "^1.7.9", "compression-webpack-plugin": "^11.1.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "express": "^4.21.2", "react": "^17.0.2", "react-activation": "^0.13.4", "react-dom": "^17.0.2", "react-router-dom": "^5.3.4", "react-scripts": "5.0.1", "sass": "^1.83.4", "typescript": "^5.9.2", "web-vitals": "^2.1.4", "xgplayer": "^3.0.22", "xgplayer-hls": "^3.0.23"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-react": "^7.26.3", "@craco/craco": "^7.1.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/react": "^17.0.37", "@types/react-dom": "^17.0.11", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^6.1.0"}}