import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { TV_SIDE_BAR_KEY } from "../SideBar";

export interface focusPublicProps {
    id: string;
    row: number;
    col: number;
    onClick?: () => void;
    currentItemCallback?: (item: FocusableElement, keyEvent: KeyboardEvent) => void;
    closeAutoFind?: { closeDirect: 'row' | 'col' } // 关闭自动查找焦点元素
}

interface IFocus extends focusPublicProps {
    children: React.ReactNode;
    style?: React.CSSProperties;
    className?: string;
    focusEvent?: (item: FocusableElement) => void; // 通过div聚焦事件获取焦点元素
    blurEvent?: (item: FocusableElement) => void; // 通过div失焦事件失去焦点元素
}

export interface FocusableElement extends focusPublicProps {
    ref: React.RefObject<HTMLElement>;
    needEnter: boolean
};

interface IFocusContext {
    register: (el: FocusableElement) => void;
    unregister: (id: string) => void;
    setFocusDisabled: (disabled: boolean) => void;
}

const FocusContext = React.createContext<IFocusContext>(null!);

export const FocusProvider = ({ children }: { children: React.ReactNode }) => {
    //被注册的元素
    const [elements, setElements] = useState<FocusableElement[]>([]);
    //当前焦点
    const [currentFocus, setCurrentFocus] = useState<string | null>(null);
    //焦点是否被禁用
    const [focusDisabled, setFocusDisabled] = useState<boolean>(false);
    const location = useLocation();
    const history = useHistory();

    const needHistoryReturn = useMemo(() => [
        '/filmAndTelevisionWall_tv/search', '/filmAndTelevisionWall_tv/allRecentlyAdd', '/filmAndTelevisionWall_tv/allRecentlyPlay', '/filmAndTelevisionWall_tv/videoDetails'
    ], []); // 需要退格键事件返回上一个页面的path

    //注册
    const register = useCallback((el: FocusableElement) => {
        setElements(prev => {
            const p = [...prev, el];
            const sideBarEl = p.filter(it => it.id.includes('sideBar'));
            const normalEl = p.filter(it => !it.id.includes('sideBar')).sort((a, b) => a.row - b.row);
            sideBarEl.sort((a, b) => b.row - a.row).forEach(item => {
                normalEl.unshift(item); // 将侧边栏放在前面
            })
            return normalEl;
        })
    }, []);

    //注销
    const unregister = useCallback((id: string) => setElements(prev => prev.filter(e => e.id !== id)), []);

    // 返回侧边栏
    const returnSideBar = useCallback(() => {
        let localKey = localStorage.getItem(TV_SIDE_BAR_KEY); // 缓存key
        if (!localKey) {
            // 没有缓存key，则返回侧边栏第一个元素
            localStorage.setItem(TV_SIDE_BAR_KEY, elements[1].id); // 缓存侧边栏第一个元素key
            localKey = elements[1].id; // 缓存侧边栏第一个元素key
        }
        const result = elements.find(it => it.id === localKey) || elements[1];
        console.log(`即将返回侧边栏元素,当前缓存key:${localKey},即将返回的元素:${result.id}`);
        return result;
    }, [elements])

    // const scrollStep = 50;//滚动步长

    // const easeInOutQuad = (t: number) => {
    //     return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    // }

    // const scrollWithAnimation = useCallback((element: any, targetY: number) => {
    //     const startY = element.scrollTop;
    //     const distance = targetY - startY;
    //     const duration = Math.abs(distance) * 2; // 动画时长（ms）

    //     function animate() {
    //         const currentTime = Date.now() - startTimestamp;
    //         const progress = currentTime / duration;
    //         if (progress >= 1) {
    //             element.scrollTop = targetY;
    //             return;
    //         }
    //         element.scrollTop = startY + distance * easeInOutQuad(progress);
    //         requestAnimationFrame(animate);
    //     }

    //     const startTimestamp = Date.now();
    //     element.scrollTop = startY; // 触发重绘
    //     requestAnimationFrame(animate);
    // }, [])

    // const containerScroll = useCallback((direction: number) => {
    //     const scrollContainer = document.querySelector('.nasTV_optPage_mainContainer') || document.body;
    //     const scrollTop = scrollContainer.scrollTop;
    //     if (direction === 1) {
    //         const maxScroll = scrollContainer.scrollHeight - scrollContainer.clientHeight;
    //         const newScrollTop = scrollTop + scrollStep;
    //         if (newScrollTop < maxScroll || scrollTop !== maxScroll) {
    //             scrollWithAnimation(scrollContainer, newScrollTop);
    //         }
    //     } else {
    //         const newScrollTop = scrollTop - scrollStep;
    //         if (newScrollTop > 0) {
    //             scrollWithAnimation(scrollContainer, newScrollTop);
    //         }
    //     }
    // }, [scrollWithAnimation])

    //查找最近的垂直元素
    const findClosestVertical = useCallback((current: FocusableElement, direction: number) => {
        const allRows = [...new Set(elements.map(e => e.row))].sort((a, b) => a - b);

        //边界检查
        if ((direction === 1 && current.row === allRows[allRows.length - 1]) || (direction === -1 && current.row === allRows[0])) {
            //containerScroll(direction);
            return undefined;
        }

        //按方向查询元素并以方向排序
        const directCandidates = elements.filter(el => el.col === current.col &&
            ((direction === 1) ? el.row > current.row : el.row < current.row)).sort((a, b) => (a.row - b.row) * direction);

        if (directCandidates.length > 0) return directCandidates[0]; //有的话直接返回最近的元素

        // 如果关闭自动查找,则返回
        if (current.closeAutoFind && current.closeAutoFind.closeDirect === 'col') return;

        //如果没有的话找横向
        const targetRowCondition = (el: FocusableElement) => direction === 1 ?
            elements.some(e => e.col === el.col && e.row > current.row) : //向下，找有下方元素的列;
            elements.some(e => e.col === el.col && e.row < current.row); //向上，找上方元素的列;

        const currentRowElements = elements.filter(el => el.row === current.row && el.col !== current.col && targetRowCondition(el)); //排除当前列，使用targetRowCondition来寻找该列存在目标元素

        //左右候选排序
        const candidates = currentRowElements.reduce<{ left: FocusableElement[], right: FocusableElement[] }>((acc, el) => {
            const diff = el.col - current.col;
            if (diff < 0) {
                acc.left.push(el);
            } else {
                acc.right.push(el);
            }
            return acc;
        }, { left: [], right: [] });

        //开始排序
        const leftCandidate = candidates.left.sort((a, b) => b.col - a.col)[0];
        const rightCandidate = candidates.right.sort((a, b) => a.col - b.col)[0];

        //选择最近列
        const getClosestColumn = () => {
            if (!leftCandidate && !rightCandidate) return undefined;
            if (!leftCandidate) return rightCandidate;
            if (!rightCandidate) return leftCandidate;

            const leftDistance = current.col - leftCandidate.col;
            const rightDistance = rightCandidate.col - rightCandidate.col;
            //距离相同就先返回左列，向上的话优先返回右列
            return leftDistance === rightDistance ? (direction === 1 ? leftCandidate : rightCandidate) : leftDistance < rightDistance ? leftCandidate : rightCandidate
        }

        const closestColumn = getClosestColumn();
        if (!closestColumn) return undefined;

        //按方向排序返回最近的
        const resultList = elements.filter(el => el.col === closestColumn.col &&
            (direction === 1 ? el.row > current.row : el.row < current.row)).sort((a, b) => (a.row - b.row) * direction);

        if (resultList.length > 0) {
            const item: FocusableElement = resultList[0];
            if (item.id.includes('sideBar')) return;
            return resultList[0];
        }
    }, [elements])

    // 查找最近水平元素
    const findClosestHorizontal = useCallback((current: FocusableElement, direction: number) => {
        const allCols = [...new Set(elements.map(e => e.col))].sort((a, b) => a - b);

        // 边界检查
        if ((direction === 1 && current.col === allCols[allCols.length - 1]) || (direction === -1 && current.col === allCols[0])) {
            return undefined;
        }

        const directCandidates = elements
            .filter(el =>
                el.row === current.row &&
                (direction === 1 ? el.col > current.col : el.col < current.col)
            )
            .sort((a, b) => (a.col - b.col) * direction); // 按方向排序

        // 同行情况下，如果直接方向有元素则优先返回该方向的第一个元素（已按距离排序）
        if (directCandidates.length > 0) {
            let result: FocusableElement | undefined = undefined;
            const item: FocusableElement = directCandidates[0];

            // 如果resultList的长度大于1，判断最近的元素是否为侧边栏，如是的话按照缓存结果返回
            if (item.id.includes('sideBar')) {
                result = returnSideBar();
                return result;
            }

            return item; // 优先直接方向元素
        }

        // 如果关闭自动查找,则返回
        if (current.closeAutoFind && current.closeAutoFind.closeDirect === 'row') return;
        const targetColCondition = (el: FocusableElement) =>
            direction === 1
                ? elements.some(e => e.row === el.row && e.col > current.col) // 向右：找有右侧元素的列
                : elements.some(e => e.row === el.row && e.col < current.col); // 向左：找有左侧元素的列

        const currentColElements = elements.filter(el => el.col === current.col && el.row !== current.row && targetColCondition(el));

        // 上下候选排序逻辑
        const candidates = currentColElements.reduce<{ upper: FocusableElement[], lower: FocusableElement[] }>((acc, el) => {
            const diff = el.row - current.row;
            if (diff < 0) {
                acc.upper.push(el);
            } else {
                acc.lower.push(el);
            }
            return acc;
        }, { upper: [], lower: [] });

        // 找到最近的上下候选
        const upperCandidate = candidates.upper.sort((a, b) => b.row - a.row)[0];
        const lowerCandidate = candidates.lower.sort((a, b) => a.row - b.row)[0];

        // 选择最近行逻辑
        const getClosestRow = () => {
            if (!upperCandidate && !lowerCandidate) return undefined;
            if (!upperCandidate) return lowerCandidate;
            if (!lowerCandidate) return upperCandidate;

            const upperDistance = current.row - upperCandidate.row;
            const lowerDistance = lowerCandidate.row - current.row;

            // 距离相同时向右优先下行，向左优先上行
            return upperDistance === lowerDistance ? (direction === 1 ? lowerCandidate : upperCandidate) : upperDistance < lowerDistance ? upperCandidate : lowerCandidate;
        };

        const closestRow = getClosestRow();
        if (!closestRow) return undefined;

        // 获得最近的元素list
        const resultList = elements.filter(el => el.row === closestRow.row &&
            (direction === 1 ? el.col > current.col : el.col < current.col)).sort((a, b) => (a.col - b.col) * direction);

        let result: FocusableElement | undefined = undefined;

        if (resultList.length > 0) {
            // 如果resultList的长度大于1，判断最近的元素是否为侧边栏，如是的话按照缓存结果返回
            const item: FocusableElement = resultList[0];
            if (item.id.includes('sideBar')) {
                result = returnSideBar();
                return result;
            }
            result = item;
        }

        return result;
    }, [elements, returnSideBar])

    // 退格事件
    const handleBackspace = useCallback(() => {
        const pathName = location.pathname;

        // 是否需要返回上一页
        if (needHistoryReturn.includes(pathName)) {
            history.goBack();
            return;
        }

        // 判断当前所处元素是否为侧边栏,如果是,返回侧边栏[1]
        if (currentFocus && currentFocus.includes('sideBar')) {
            const result = elements[1];
            console.log(`即将返回的元素:${result.id}`);
            return result;
        }

        // 否则，尝试返回侧边栏
        const result = returnSideBar();
        return result;
    }, [currentFocus, elements, history, location.pathname, needHistoryReturn, returnSideBar])

    //键盘事件
    const handleKeyEvent = useCallback((e: KeyboardEvent) => {
        // 如果焦点被禁用，则不处理键盘事件
        if (focusDisabled) return;
        if (!currentFocus) return;
        const current = elements.find(el => el.id === currentFocus);//当前焦点元素是否存在
        const isKey = ["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight", "Escape"].includes(e.key);
        if (!isKey) return; // 不是我们需要处理的按键，直接返回

        if (!current) {
            console.log(`当前焦点元素不存在，尝试初始化焦点`);
            if (elements.length > 0) {
                const path = location.pathname.split('/')[2];
                // 寻找当前path对应的元素们，如果没有则默认选中侧边栏元素[1]
                console.log(`当前path:${path}`);
                const findElements = elements.filter((it) => it.id.split('-')[2] === path);
                console.log(`尝试寻找当前path对应的元素们:`, findElements);
                if (findElements.length === 0) {
                    if (path === 'allRecentlyAdd' || path === 'allRecentlyPlay') {
                        setCurrentFocus(elements[0].id);
                        elements[0].ref.current?.focus();
                        return;
                    }

                    console.log(`未找到对应元素，默认根据当前页面注册的所有元素:${elements[1].id}`);
                    setCurrentFocus(elements[1].id);
                    elements[1].ref.current?.focus();
                    return;
                }
                setCurrentFocus(findElements[0].id);
                findElements[0].ref.current?.focus();
            }
            return;
        }
        let target: FocusableElement | undefined;

        switch (e.key) {
            case "ArrowUp":
                target = findClosestVertical(current, -1); break;
            case "ArrowDown":
                target = findClosestVertical(current, 1); break;
            case "ArrowLeft":
                target = findClosestHorizontal(current, -1); break;
            case "ArrowRight":
                target = findClosestHorizontal(current, 1); break;
            // case "Enter":
            //     current.onClick && current.needEnter && current.onClick(); break;
            case "Escape":
                target = handleBackspace(); break;
            default: return;
        }

        //设置对应键盘键操作完成后的焦点
        if (target) {
            console.log(`即将设置焦点:${target.id}`);
            if (isKey) e.preventDefault();
            setCurrentFocus(target.id);
            target.currentItemCallback && target.currentItemCallback(target, e);
            target.ref.current?.focus();//获取焦点
            if (target.onClick && !target.needEnter) target.onClick();
            return;
        }
    }, [focusDisabled, currentFocus, elements, location.pathname, findClosestVertical, findClosestHorizontal, handleBackspace]);

    //监听键盘
    useEffect(() => {
        window.addEventListener("keydown", handleKeyEvent);

        return () => {
            window.removeEventListener("keydown", handleKeyEvent)
        };
    }, [handleKeyEvent])

    //初始化焦点
    useEffect(() => {
        // 确保仅在首次加载时初始化
        if (elements.length > 0 && currentFocus === null) {
            console.log(`首次加载，尝试初始化焦点`);
            const savedFocus = localStorage.getItem("tv-focus");
            const initial = elements.find(el => el.id === savedFocus) || elements[1];
            setCurrentFocus(initial.id);
            initial.ref.current?.focus();
        }
    }, [currentFocus, elements])

    //当前焦点变化时保存到localStorage
    useEffect(() => {
        if (currentFocus) {
            // console.log(`当前焦点变化时保存到localStorage`, currentFocus);
            localStorage.setItem("tv-focus", currentFocus);
        }
    }, [currentFocus])

    // 当currentFocus存在时（切换新的路由）
    useEffect(() => {
        const current = elements.find(el => el.id === currentFocus);
        // console.log(location.pathname, `尝试修正焦点`, current);
        if (current) {
            current.ref.current?.focus();
        } else {
            // console.log(`当前页面焦点元素不存在，尝试初始化焦点`);
            if (elements.length > 0) {
                const path = location.pathname.split('/')[2];
                // 寻找当前path对应的元素们，如果没有则默认选中侧边栏元素[1]
                console.log(`当前path:${path}`);
                const findElements = elements.filter((it) => it.id.split('-')[2] === path);
                console.log(`尝试寻找当前path对应的元素们:`, findElements);
                if (findElements.length === 0) {
                    if (path === 'allRecentlyAdd' || path === 'allRecentlyPlay' || path === 'videoDetails') {
                        setCurrentFocus(elements[0].id);
                        elements[0].ref.current?.focus();
                        return;
                    }

                    console.log(`未找到对应元素，默认根据当前页面注册的所有元素:${elements[1].id}`);
                    setCurrentFocus(elements[1].id);
                    elements[1].ref.current?.focus();
                    return;
                }
                setCurrentFocus(findElements[0].id);
                findElements[0].ref.current?.focus();
            }
            return;
        }
    }, [currentFocus, elements, location.pathname])

    //提供注册注销方法
    return (
        <FocusContext.Provider value={{ register, unregister, setFocusDisabled }}>
            {children}
        </FocusContext.Provider>
    )
}
interface ITvFocusComponentsWeight {
    [key: string]: {
        row: number,
        col: number
    }
}

const tvFocusComponentsWeight: ITvFocusComponentsWeight = {
    sideBar: { row: 0, col: 0 },
    sideBarUser: { row: 0, col: 0 },
    sideBarSearch: { row: 0, col: 0 },
    recentlyPlay: { row: 0, col: 1 },
    recentlyAdd: { row: 0, col: 1 },
    recentlyAddMore: { row: 0, col: 1 },
    recentlyPlayMore: { row: 0, col: 1 },
    recentlyAddList: { row: 0, col: 1 },
    recentlyPlayList: { row: 0, col: 1 },
    allRecentlyPlay: { row: 0, col: 1 },
    allRecentlyAdd: { row: 0, col: 1 },
    all: { row: 0, col: 1 },
    allMenus: { row: 0, col: 1 },
    search: { row: 0, col: 1 },
    searchItem: { row: 0, col: 1 },
    dramasOrMovie: { row: 0, col: 1 },
    videoDetails: { row: 0, col: 1 },
    episodeList: { row: 0, col: 1 },
    actorDetail: { row: 0, col: 1 },
    error: { row: 0, col: 1 },
    collect: { row: 1, col: 1 },
    played: { row: 1, col: 1 },
    library: { row: 1, col: 1 }
}

const TVFocusable = (props: IFocus) => {
    const { id, row, col, children, style, className, onClick, currentItemCallback, focusEvent, blurEvent, closeAutoFind } = props;
    const ref = useRef<HTMLDivElement>(null);
    const { register, unregister } = useContext(FocusContext);

    // 使用useRef来存储稳定的函数引用
    const onClickRef = useRef(onClick);
    const currentItemRef = useRef(currentItemCallback);

    // 更新ref中的函数引用
    useEffect(() => {
        onClickRef.current = onClick;
        currentItemRef.current = currentItemCallback;
    }, [onClick, currentItemCallback]);

    // 查询组件权重，并根据当前路径动态调整行列值
    const element: FocusableElement = useMemo(() => {
        const type = id.split("-")[2] || "sideBar";
        const needEnterArray: string[] = ['videoDetails', 'episodeList', 'actorDetail', 'allRecentlyPlay', 'allRecentlyAdd', 'recentlyPlay', 'recentlyAdd',
            'all', 'searchItem', 'search', 'collect', 'played', 'library', 'sideBarSearch', 'recentlyPlayMore', 'recentlyAddMore', 'sideBarUser'];
        return {
            id, row: tvFocusComponentsWeight[type].row + row,
            col: tvFocusComponentsWeight[type].col + col, ref,
            onClick: () => onClickRef.current?.(),
            currentItemCallback: (item: FocusableElement, keyEvent: KeyboardEvent) => currentItemRef.current?.(item, keyEvent),
            needEnter: needEnterArray.includes(type),
            closeAutoFind: closeAutoFind
        }
    }, [id, row, col, closeAutoFind]);

    useEffect(() => {
        register(element);

        return () => unregister(id);
    }, [element, id, register, unregister])

    return (
        <div ref={ref} tabIndex={0} className={`focus_item ` + className} style={{ outline: 'none', ...style }} onClick={onClick}
            onFocus={(e) => {
                e.preventDefault();
                e.stopPropagation();
                focusEvent && focusEvent(element);
            }} onBlur={(e) => {
                e.preventDefault();
                e.stopPropagation();
                blurEvent && blurEvent(element)
            }}>
            {children}
        </div>
    )
}

// 自定义Hook用于控制焦点禁用状态
export const useFocusControl = () => {
    const { setFocusDisabled } = useContext(FocusContext);
    return { setFocusDisabled };
};

export default TVFocusable;