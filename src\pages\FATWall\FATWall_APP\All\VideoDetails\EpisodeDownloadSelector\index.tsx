import React, { useState, useCallback, useMemo } from 'react';
import { Toast, Checkbox } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { MediaFileInfo } from '@/api/fatWall';
import { downloadFiles } from '@/api/fatWallJSBridge';
// import { modalShow } from '@/components/List';

interface EpisodeDownloadSelectorProps {
    visible: boolean;
    mediaFiles: MediaFileInfo[];
    onClose: () => void;
    classes: string;
    videoinfo: any
}

const EpisodeDownloadSelector: React.FC<EpisodeDownloadSelectorProps> = ({
    visible,
    mediaFiles,
    onClose,
    classes,
    videoinfo
}) => {
    const [isDownloading, setIsDownloading] = useState<boolean>(false);
    const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
    console.log("videoinfo", videoinfo);

    // 处理文件选择
    const handleFileSelect = useCallback((fileId: number) => {
        if (isDownloading) return;

        setSelectedFiles(prev => {
            if (prev.includes(fileId)) {
                return prev.filter(id => id !== fileId);
            } else {
                return [...prev, fileId];
            }
        });
    }, [isDownloading]);

    // 处理全部下载
    const handleDownloadAll = useCallback(() => {
        if (isDownloading) return;
        if (!mediaFiles || mediaFiles.length === 0) {
            Toast.show({
                content: '暂无可下载的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        setIsDownloading(true);

        // 构造文件信息列表，过滤掉无效的文件数据
        const validFiles = mediaFiles.filter(file => file && file.path && typeof file.file_size === 'number');
        if (validFiles.length === 0) {
            Toast.show({
                content: '没有可下载的文件',
                position: 'bottom',
                duration: 1500,
            });
            setIsDownloading(false);
            return;
        }

        const fileList = validFiles.map(file => ({
            name: file.path.split('/').pop() || `第${file.episode || 1}集`,
            path: file.path,
            mtime: '', // mediaFiles中没有修改时间信息，设为空字符串
            size: file?.file_size || 0
        }));

        downloadFiles(fileList, (res) => {
            if (res.code === 0) {
                Toast.show({
                    content: '下载任务已添加到任务中心',
                    position: 'bottom',
                    duration: 1500,
                });
                onClose();
            } else {
                Toast.show({
                    content: `下载失败: ${res.msg}`,
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }).catch((error) => {
            Toast.show({
                content: error.message || '下载失败',
                position: 'bottom',
                duration: 1500,
            });
        }).finally(() => {
            setIsDownloading(false);
        });
    }, [mediaFiles, isDownloading]);

    // 处理选中文件下载
    const handleDownloadSelected = useCallback(() => {
        if (isDownloading) return;
        if (selectedFiles.length === 0) {
            Toast.show({
                content: '请选择要下载的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        setIsDownloading(true);

        // 根据选中的文件ID筛选文件
        const selectedMediaFiles = mediaFiles.filter(file => selectedFiles.includes(file.file_id));

        const fileList = selectedMediaFiles.map(file => ({
            name: file.path.split('/').pop() || `第${file.episode}集`,
            path: file.path,
            mtime: '',
            size: file.file_size || 0
        }));

        downloadFiles(fileList, (res) => {
            if (res.code === 0) {
                Toast.show({
                    content: '下载任务已添加到任务中心',
                    position: 'bottom',
                    duration: 1500,
                });
                onClose();
            } else {
                Toast.show({
                    content: `下载失败: ${res.msg}`,
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }).catch((error) => {
            Toast.show({
                content: error.message || '下载失败',
                position: 'bottom',
                duration: 1500,
            });
        }).finally(() => {
            setIsDownloading(false);
        });
    }, [selectedFiles, mediaFiles, isDownloading, onClose]);

    // 处理关闭
    const handleClose = useCallback(() => {
        if (isDownloading) return;
        onClose();
    }, [onClose, isDownloading]);

    // 渲染集数列表
    const episodeList = useMemo(() => {
        if (!mediaFiles || mediaFiles.length === 0) return [];

        return mediaFiles
            .filter(file => file && file.episode)
            .sort((a, b) => a.episode - b.episode)
            .map(file => ({
                episode: file.episode,
                fileId: file.file_id,
                file: file
            }));
    }, [mediaFiles]);

    if (!visible) return null;

    return (
        <div className={styles.overlay}>
            <div className={styles.container}>
                {/* 头部 */}
                <div className={styles.header}>
                    <button className={styles.closeButton} onClick={handleClose} disabled={isDownloading}>
                        <CloseOutline fontSize={24} />
                    </button>

                </div>
                <h1 className={styles.title}>选择下载视频</h1>
                {/* 电视剧集数网格 */}
                {classes === '电视剧' && (<div className={styles.episodeGrid}>
                    {episodeList.map(({ episode, fileId, file }) => (
                        <div
                            key={fileId}
                            className={`${styles.episodeItem} ${selectedFiles.includes(fileId) ? styles.selected : ''}`}
                            onClick={() => handleFileSelect(fileId)}
                        >
                            <span className={styles.episodeNumber}>{episode}</span>
                        </div>
                    ))}
                </div>)}
                {classes === '电影' && (<div className={styles.cardList}>
                    {mediaFiles.map((file, index) => (<div
                        key={index}
                        className={styles.cardItem}
                        onClick={() => handleFileSelect(file.file_id)}
                    >
                        <img className={styles.imgsize} src={videoinfo.posterUrl} alt="" />
                        <div>
                            <span className={styles.episodeNumber}>{videoinfo.title}</span>
                            <span>{videoinfo.duration}</span>
                        </div>
                        <div className={styles.movieCheckbox}>
                            <Checkbox
                                checked={selectedFiles.includes(file.file_id)}
                                onChange={(checked) => {
                                    // 阻止事件冒泡，避免重复触发
                                    handleFileSelect(file.file_id);
                                }}
                                onClick={(e) => e.stopPropagation()}
                            />
                        </div>
                    </div>))}
                </div>)}

                {/* 底部按钮 */}
                <div className={styles.footer}>
                    <span
                        className={styles.downloadAllButton}
                        onClick={handleDownloadAll}
                    >
                        全部下载
                    </span>
                    <span
                        className={`${styles.downloadSelectedButton} ${selectedFiles.length === 0 ? styles.disabled : ''}`}
                        onClick={handleDownloadSelected}
                    >
                        下载选中 ({selectedFiles.length})
                    </span>
                </div>
            </div>
        </div>
    );
};

export default EpisodeDownloadSelector;