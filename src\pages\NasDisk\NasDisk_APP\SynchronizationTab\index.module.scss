// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
  color: var(--secondary-text-color, #8C93B0);
  
  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.loading {
  text-align: center;
  padding: 40px 0;
  color: var(--secondary-text-color, #8C93B0);
  font-size: 14px;
}

// VIP按钮样式
.vipButton {
  height: 48px;
  border-radius: 16px;
  background-color: #402c00;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #e2ae1e;

  &:active {
    background-color: #6b4220;
  }
}

.vipActiveButton {
  background: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 16px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;

  &:active {
    background-color: #357abd;
  }
}

// 任务列表样式（参考 ScheduledDownload）
.taskContainer {
  padding: 0 16px 16px;
  width: 100%;
}

.taskHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 12px;
}

.taskHeaderTitle {
  color: #979797;
  font-size: 14px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  position: relative;
  
  .taskCount {
    margin-left: 4px;
    font-size: 14px;
    color: #999;
    font-weight: 400;
  }
}

.newBadge {
  font-family: MiSans W;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: right;
  color: rgba(97, 163, 255, 1);
  cursor: pointer;

  &.disabled {
    color: #ccc;
    cursor: not-allowed;
  }
}

.taskList {
  width: 100%;
}

.taskItem {
  background-color: var(--background-color);
  margin-bottom: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.taskMainInfo {
  display: flex;
  align-items: center;
  background-color: var(--camera-background-color);
  padding: 12px 0 12px 12px;
  border-radius: 10px;
}

.taskSubInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  padding: 4px 14px 10px;
}

.taskIcon {
  margin-right: 12px;
  flex-shrink: 0;
}

.taskContent {
  flex: 1;
  min-width: 0;
}

.taskName {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.progressContainer {
  position: relative;
  margin-bottom: 4px;
}

.statusContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.fileCount {
  font-size: 12px;
  color: #333;
}

.taskProgress {
  height: 4px;
  margin-bottom: 4px;
}

.taskStatus {
  font-size: 12px;
  color: #666;
}

.taskPath {
  color: #999;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.taskTime {
  color: #999;
  font-size: 12px;
  text-align: right;
}

.taskActions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: 12px;
  gap: 8px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-top: 5px;
  cursor: pointer;

  &.disabled {
    opacity: 0.5;
    cursor: default;
  }
}

.playIcon,
.pauseIcon {
  font-size: 14px;
}

// 编辑模式样式
.editModeContainer {
  position: fixed;
  top: 35px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.editContent {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
}

.editHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
  position: relative;
}

.closeIconContainer {
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeIcon {
  width: 26px;
  height: 26px;
}

.editTitle {
  font-family: MiSans W;
  font-weight: 400;
  font-size: 28px;
  line-height: 100%;
  letter-spacing: 0%;
  color: var(--title-color);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}



.taskCheckbox {
  margin-right: 8px;
}

.checkboxContainer {
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.deleteFooter {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.deleteButton {
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-direction: column;

  &.disabled {
    opacity: 0.5;
  }
}

.photoEditDeleteIcon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
}
