.container {
  padding: 10px;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 35px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .rightHeaderContent {
    display: flex;
    align-items: center;
  }

  .title {
    font-size: 32px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px;
  }
}

.cameraStatusContainer {
  padding: 80px 32px;
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.mainContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  margin-top: 76px;
}

.iconContainer {
  margin-bottom: 24px;
}

.textContainer {
  color: var(--title-color);
}

.message {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}

.subMessage {
  font-size: 13px;
  color: #999;
}

.footer {
  padding: 24px;
  
  .actionButton {
    --border-radius: 16px;
    height: 48px;
    font-size: 16px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    font-weight: 500;

    &:disabled {
      opacity: 0.5;
    }
  }
} 