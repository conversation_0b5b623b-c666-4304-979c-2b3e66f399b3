import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Button, Breadcrumb } from 'antd';
import { FolderOutlined, HeartFilled } from '@ant-design/icons';
import styles from './index.module.scss';
import { useRequest } from 'ahooks';
import { useTheme } from '@/utils/themeDetector';
import { Toast } from '@/components/Toast/manager';
import { getPoolInfo, listDirectory, StoragePool } from '@/api/fatWall';
import { PreloadImage } from '@/components/Image';
import createIcon from "@/Resources/icon/create.png";
import createIconDark from "@/Resources/icon/create-dark.png";
import backIcon_light from "@/Resources/icon/backIcon_light.png";
import backIcon_dark from "@/Resources/icon/backIcon_dark.png";
import CreateFolder from '@/pages/NasDisk/NasDisk_PC/components/CreateFolder';
import { getWebDavInfo, WebDavInfo } from '@/utils/DeviceType';


interface FileItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  time?: string;
  path: string;
  dataDir?: string; // 存储池的data_dir
  isDirectory: boolean;
  isLiked?: boolean;
  itemCount?: number;
}

interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

interface FileSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (path: string, displayPath?: string) => void;
  title?: string;
}

const FileSelector: React.FC<FileSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  title = '选择来源'
}) => {
  // 当前文件路径（面包屑）
  const [currentPath, setCurrentPath] = useState<BreadcrumbItem[]>([]);

  // 当前显示的文件列表
  const [currentFiles, setCurrentFiles] = useState<FileItem[]>([]);

  // 存储池列表
  const [storagePools, setStoragePools] = useState<StoragePool[]>([]);

  // webDAV配置信息
  const [webDAVConfig, setWebDAVConfig] = useState<{
    alias_root: string;
  } | null>(null);

  // webDav配置信息（用于CreateFolder组件）
  const [webDavConfig, setWebDavConfig] = useState<WebDavInfo>();

  // 新建文件夹弹窗状态
  const [showCreateFolder, setShowCreateFolder] = useState<boolean>(false);

  // 当前调用 list_directory 时传递的 path.parent 值
  const [currentPathParent, setCurrentPathParent] = useState<string>('');

  // 选中的文件夹路径（单选）
  const [selectedPath, setSelectedPath] = useState<string>('');

  // 是否已经初始化到第一个存储池
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  const { isDarkMode } = useTheme() as any;
  // 获取存储池信息
  const { run: fetchPoolInfo, loading: poolLoading } = useRequest(
    getPoolInfo,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          setStoragePools(response.data.internal_pool);

          // 保存webDAV配置
          if (response.data.webDAV) {
            setWebDAVConfig({
              alias_root: response.data.webDAV.alias_root
            });
          }
          const webDavInfo = getWebDavInfo();
          setWebDavConfig(webDavInfo);
          // 自动进入第一个存储池
          if (response.data.internal_pool.length > 0 && !isInitialized) {
            const firstPool = response.data.internal_pool[0];

            // 设置面包屑为第一个存储池
            const newBreadcrumb: BreadcrumbItem = {
              id: 'pool_0',
              name: firstPool.name,
              path: firstPool.data_dir
            };
            setCurrentPath([newBreadcrumb]);

            // 构造正确的path.parent：data_dir + alias_root
            let pathParent = firstPool.data_dir;
            if (response.data.webDAV?.alias_root) {
              const dataDir = firstPool.data_dir.endsWith('/') ? firstPool.data_dir.slice(0, -1) : firstPool.data_dir;
              const aliasRoot = response.data.webDAV.alias_root;
              pathParent = aliasRoot + dataDir;
            }

            setCurrentPathParent(pathParent);
            setIsInitialized(true);

            // 获取第一个存储池的目录列表
            fetchDirectory({
              path: {
                parent: pathParent,
                recursion: false
              }
            });
          } else {
            // 如果没有存储池或已经初始化过，显示存储池列表
            const poolItems: FileItem[] = response.data.internal_pool.map((pool: StoragePool, index: number) => ({
              id: `pool_${index}`,
              name: pool.name,
              type: 'folder' as const,
              time: '',
              path: pool.data_dir,
              dataDir: pool.data_dir,
              isDirectory: true
            }));
            setCurrentFiles(poolItems);
            setCurrentPath([]);
          }
        }
      },
      onError: (error) => {
        console.error('获取存储池信息失败：', error);
        Toast.show('获取存储池信息失败，请重试', { duration: 2000 });
        setCurrentFiles([]);
        setCurrentPath([]);
      },
    }
  );

  // 获取目录列表
  const { run: fetchDirectory, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          // 转换API返回的文件列表为本地格式
          const files: FileItem[] = response.data.files
            .filter((file: any) => file.xattr.directory) // 只显示文件夹
            .map((file: any, index: number) => ({
              id: `file_${index}`,
              name: file.name,
              type: 'folder' as const,
              time: new Date(parseInt(file.modified_time)).toLocaleDateString(),
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite
            }));
          setCurrentFiles(files);
        }
      },
      onError: (error) => {
        console.error('获取目录列表失败：', error);
        Toast.show('获取目录失败，请重试', { duration: 2000 });
        // 显示空列表或返回上级目录
        setCurrentFiles([]);
      },
    }
  );

  // 初始化时获取存储池信息
  useEffect(() => {
    if (visible) {
      setIsInitialized(false); // 重置初始化状态
      setSelectedPath(''); // 清空选中状态
      fetchPoolInfo({});
    }
  }, [visible, fetchPoolInfo]);

  // 进入文件夹
  const navigateToFolder = useCallback((folder: FileItem) => {
    // 清空选中状态，因为进入了新的层级
    setSelectedPath('');

    if (currentPath.length === 0) {
      // 如果是顶层（选择存储池），添加到面包屑并获取该存储池的目录
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path
      };
      setCurrentPath([newBreadcrumb]);

      // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
      let pathParent = folder.path;
      if (webDAVConfig?.alias_root && folder.dataDir) {
        // 确保data_dir末尾没有斜杠
        const dataDir = folder.dataDir.endsWith('/') ? folder.dataDir.slice(0, -1) : folder.dataDir;
        const aliasRoot = webDAVConfig.alias_root;
        pathParent = aliasRoot + dataDir;
      }

      // 保存当前的 path.parent 值
      setCurrentPathParent(pathParent);

      // 调用目录列表API
      fetchDirectory({
        path: {
          parent: pathParent,
          recursion: false
        }
      });
    } else {
      // 如果已经在某个目录中，继续深入
      const newBreadcrumb: BreadcrumbItem = {
        id: folder.id,
        name: folder.name,
        path: folder.path
      };
      setCurrentPath(prev => [...prev, newBreadcrumb]);

      // 保存当前的 path.parent 值
      setCurrentPathParent(folder.path);

      // 调用目录列表API
      fetchDirectory({
        path: {
          parent: folder.path,
          recursion: false
        }
      });
    }
  }, [currentPath, fetchDirectory, webDAVConfig]);

  // 处理新建文件夹
  const handleCreateFolder = () => {
    setShowCreateFolder(true);
  };

  // 处理创建文件夹成功
  const handleCreateFolderSuccess = () => {
    setShowCreateFolder(false);

    // 刷新当前目录
    if (currentPathParent) {
      fetchDirectory({
        path: {
          parent: currentPathParent,
          recursion: false
        }
      });
    }
  };

  // 处理创建文件夹取消
  const handleCreateFolderCancel = () => {
    setShowCreateFolder(false);
  };
  const navigateToBreadcrumb = useCallback((index: number) => {
    // 清空选中状态，因为切换了层级
    setSelectedPath('');

    // 如果点击存储池名称（index为0），返回到存储池的根目录
    if (index === 0) {
      const poolItem = currentPath[0];
      setCurrentPath([poolItem]);

      // 构造存储池根目录的path.parent
      const poolData = storagePools.find(pool => pool.name === poolItem.name);
      if (poolData && webDAVConfig?.alias_root) {
        const dataDir = poolData.data_dir.endsWith('/') ? poolData.data_dir.slice(0, -1) : poolData.data_dir;
        const aliasRoot = webDAVConfig.alias_root;
        const pathParent = aliasRoot + dataDir;

        setCurrentPathParent(pathParent);

        fetchDirectory({
          path: {
            parent: pathParent,
            recursion: false
          }
        });
      }
      return;
    }

    // 截取路径到指定索引
    const newPath = currentPath.slice(0, index + 1);
    setCurrentPath(newPath);

    // 获取指定路径的目录列表
    const targetPath = newPath[newPath.length - 1].path;

    // 如果是第一层（存储池层级），需要构造特殊的路径
    if (newPath.length === 1) {
      // 找到对应的存储池数据
      const poolData = storagePools.find(pool => pool.name === newPath[0].name);
      if (poolData && webDAVConfig?.alias_root) {
        // 构造正确的path.parent：data_dir + alias_root，避免双斜杠
        const dataDir = poolData.data_dir.endsWith('/') ? poolData.data_dir.slice(0, -1) : poolData.data_dir;
        const aliasRoot = webDAVConfig.alias_root;
        const pathParent = aliasRoot + dataDir;

        // 保存当前的 path.parent 值
        setCurrentPathParent(pathParent);

        fetchDirectory({
          path: {
            parent: pathParent,
            recursion: false
          }
        });
      }
    } else {
      // 普通的文件夹层级
      // 保存当前的 path.parent 值
      setCurrentPathParent(targetPath);

      fetchDirectory({
        path: {
          parent: targetPath,
          recursion: false
        }
      });
    }
  }, [currentPath, fetchDirectory, storagePools, webDAVConfig]);

  // 选择当前路径
  const handleSelect = useCallback(() => {
    if (!selectedPath) {
      Toast.show('请选择一个文件夹', { duration: 2000 });
      return;
    }

    // 找到对应的文件项
    const file = currentFiles.find(f => f.path === selectedPath);
    let displayPath = selectedPath;

    if (file) {
      // 构建完整的显示路径：存储池名称 + 文件夹名称
      const poolName = currentPath.length > 0 ? currentPath[0].name : '';
      displayPath = poolName ? `${poolName}/${file.name}` : file.name;
    }

    onSelect(selectedPath, displayPath);
    onClose();

    // 重置状态
    setCurrentPath([]);
    setCurrentFiles([]);
    setCurrentPathParent('');
    setSelectedPath('');
  }, [selectedPath, currentFiles, currentPath, onSelect, onClose]);

  // 关闭弹窗时重置状态
  const handleClose = useCallback(() => {
    setCurrentPath([]);
    setCurrentFiles([]);
    setCurrentPathParent('');
    setSelectedPath('');
    setIsInitialized(false);
    onClose();
  }, [onClose]);

  // 生成面包屑项
  const breadcrumbItems = currentPath.map((pathItem, index) => ({
    title: (
      <span
        onClick={() => navigateToBreadcrumb(index)}
        style={{ cursor: 'pointer' }}
      >
        {pathItem.name}
      </span>
    )
  }));
  const customTitle = (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
      {/* 左侧图标 */}
      <PreloadImage
        src={isDarkMode ? backIcon_dark : backIcon_light}
        alt="返回"
        style={{ width: 40, height: 40 }}
        onClick={handleClose}
      />
      {/* 中间标题 */}
      <span style={{ fontSize: "20px" }}>选择来源</span>
      {/* 右侧图标 - 这里示例使用关闭图标，并可绑定关闭事件 */}
      <PreloadImage
        src={isDarkMode ? createIconDark : createIcon}
        alt="新建文件夹"
        style={{ width: 40, height: 40 }}
        onClick={handleCreateFolder}
      />
    </div>
  );
  return (
    <Modal
      title={customTitle}
      open={visible}
      // onCancel={handleClose}

      closeIcon={null}
      footer={null}
      width={600}
      centered
      className={styles.modal}
      destroyOnClose={true}
      maskClosable={false}
    >
      <div className={styles.container}>
        {/* 面包屑 */}
        <div className={styles.breadcrumbContainer}>
          <Breadcrumb
            items={breadcrumbItems}
            separator=">"
            className={styles.breadcrumb}

          />
        </div>

        {/* 加载状态 */}
        {(poolLoading || directoryLoading) && (
          <div className={styles.loadingContainer}>
            <span>加载中...</span>
          </div>
        )}

        {/* 文件列表 */}
        <div className={styles.fileList}>
          {currentFiles.map(file => (
            <div
              key={file.id}
              className={`${styles.fileItem} ${selectedPath === file.path ? styles.selected : ''}`}
            >
              <div className={styles.fileIcon}>
                <FolderOutlined />
              </div>
              <div
                className={styles.fileInfo}
                onClick={() => {
                  // 点击文件内容进入下一级
                  if (file.type === 'folder') {
                    navigateToFolder(file);
                  }
                }}
              >
                <div className={styles.fileName}>
                  {file.name}
                  {file.isLiked && <HeartFilled className={styles.heartIcon} />}
                </div>
                <div className={styles.fileDetails}>
                  {file.time && `${file.time}`}
                  {file.itemCount && ` | ${file.itemCount}项`}
                </div>
              </div>
              {/* 选中状态指示器 */}
              <div className={styles.checkboxContainer}>
                <div
                  className={`${styles.customCheckbox} ${selectedPath === file.path ? styles.checked : ''}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    // 只有点击checkbox才选中
                    if (selectedPath === file.path) {
                      setSelectedPath('');
                    } else {
                      setSelectedPath(file.path);
                    }
                  }}
                />
              </div>
            </div>
          ))}

          {!poolLoading && !directoryLoading && currentFiles.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className={styles.footer}>
          <div className={styles.cancelButton}>
            <Button
              className={styles.cancelButton}
              onClick={handleClose}
            >
              取消
            </Button>
          </div>
          <div className={styles.confirmButton}>
            <Button
              onClick={handleSelect}
              className={styles.confirmButton}
              disabled={!selectedPath}
            // type="primary"
            >
              确定
            </Button>
          </div>

        </div>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateFolder
        visible={showCreateFolder}
        onCancel={handleCreateFolderCancel}
        onSuccess={handleCreateFolderSuccess}
        currentPath={currentPathParent}
        webDavConfig={webDavConfig}
      />
    </Modal>
  );
};

export default FileSelector; 