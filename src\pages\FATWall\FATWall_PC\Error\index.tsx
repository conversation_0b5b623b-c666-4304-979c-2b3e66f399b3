import styles from '@/pages/FATWall/FATWall_PC/Recently/RecentlyPlay/index.module.scss';
import { FC, useState, useCallback } from 'react';
import content_null_img from '@/Resources/icon/content_null.png';
import content_null_img_dark from '@/Resources/icon/content_null_dark.png';
import content_error_img from '@/Resources/icon/content_error.png';
import content_error_img_dark from '@/Resources/icon/content_error_dark.png';
import { PreloadImage } from '@/components/Image';
import { useTheme } from '@/utils/themeDetector';
import CreateLibraryModal from '@/pages/FATWall/FATWall_PC/LibraryManagement/CreateLibraryModal';
import { useHistory } from 'react-router-dom';
import { getDeviceType, DeviceType } from '@/utils/DeviceType';
import { useRequest } from 'ahooks';
import { getFullLibraryData } from '@/api/fatWall';
import { useLibraryList } from '..';

const FATErrorComponents: FC<{ span: string, canTry: boolean, subSpan?: string, refresh: () => void, show: boolean }> = (props) => {
  const { span, canTry, subSpan, refresh, children, show } = props;
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const { libs, setLibs } = useLibraryList();
  const { runAsync: getLibraryData } = useRequest(getFullLibraryData, { manual: true });

  // 处理创建媒体库
  const handleCreateLibrary = useCallback(() => {
    if (getDeviceType() === DeviceType.Mobile) {
      console.log('mobile', getDeviceType());

      history.push('/filmAndTelevisionWall_app/createLibrary');
    } else {
      setIsCreateModalOpen(true);
    }
  }, [history]);

  // 处理创建弹窗关闭
  const handleCreateModalClose = useCallback(() => {
    setIsCreateModalOpen(false);
  }, []);

  // 刷新媒体库列表
  const refreshLibraryList = useCallback(async () => {
    try {
      const res = await getLibraryData({ sort_type: 0, asc: 0 });
      if (res && res.code === 0 && res.data) {
        const myLibs = res.data.my_libs.libs.map((item: any) => ({ lib_id: item.lib_id, name: item.name, count: item.media_count, status: item.scan_status }));
        const otherLibs = res.data.share2me.libs.map((item: any) => ({ lib_id: item.lib_id, name: item.name, count: item.media_count, status: item.scan_status }));
        const allLibs = myLibs.concat(otherLibs);
        setLibs(allLibs);
      }
    } catch (error) {
      console.log('刷新媒体库列表失败：', error);
    }
  }, [getLibraryData, setLibs]);

  // 处理创建成功
  const handleCreateSuccess = useCallback(async () => {
    // 刷新媒体库列表
    await refreshLibraryList();
    // 创建成功后跳转到最近添加页面
    history.push('/filmAndTelevisionWall_pc/recently/recentlyAdd');
  }, [history, refreshLibraryList]);

  return (
    <>
      {
        show ? (
          <div className={styles.result_not_container}>
            <div className={styles.search_not_result_show_icon}>
              {
                canTry ? <PreloadImage className={styles.content_null_img} src={isDarkMode ? content_error_img_dark : content_error_img} alt='content_null' /> :
                  <PreloadImage className={styles.content_null_img} src={isDarkMode ? content_null_img_dark : content_null_img} alt='content_null' />
              }
            </div>
            <span className={styles.result_not_container_span}>{span}</span>
            <span className={styles.result_not_container_span}>{subSpan}</span>
            {
              subSpan ? <></> : canTry ?
                <div className={styles.search_buttons_item} onClick={() => refresh()} >重试</div>
                :
                <div className={styles.search_buttons_item} onClick={handleCreateLibrary}>创建媒体库</div>
            }
          </div>
        ) : children
      }

      {/* 创建媒体库弹窗 */}
      <CreateLibraryModal
        visible={isCreateModalOpen}
        onClose={handleCreateModalClose}
        onSuccess={handleCreateSuccess}
        libraryCount={libs.length} // 使用当前媒体库数量
      />
    </>
  )
}

export default FATErrorComponents;