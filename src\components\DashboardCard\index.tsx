import { getDeviceType } from '@/utils/DeviceType';
import { PreloadImage } from '../Image';
import styles from './index.module.scss';
import cardIconFrame from '@/Resources/icon/Frame.png';

interface IDashboardCard {
  content: React.ReactNode;
  icon?: string;
  title: string;
  subtitle?: string; // 副标题，只有pc有
  extra?: React.ReactNode;
  onClick?: () => void;
}

const DashboardCard = (props: IDashboardCard) => {
  const { content, icon, title, extra, onClick, subtitle } = props;
  const deviceType = getDeviceType(); // 0为mobile 1为pc
  const stylesString = deviceType === 0 ? 'mobile' : deviceType === 1 ? 'pc' : 'tv';
  return (
    <div className={`${styles.card_container} ${styles[stylesString]}`} >
      <div className={`${styles.card_status_bar} ${styles[stylesString]}`}>
        <PreloadImage src={icon || cardIconFrame} alt='icon' />
        <div className={`${styles.card_status_bar_text} ${styles[stylesString]}`}>
          <span>{title}</span>
          <span className={styles.subtitle}>{subtitle}</span>
        </div>
        {extra && <div className={`${styles.card_status_bar_extra} ${styles[stylesString]}`}>{extra}</div>}
      </div>
      <div className={`${styles.card_content} ${styles[stylesString]}`} onClick={onClick}>
        {content}
      </div>
    </div>
  )
}

export default DashboardCard;