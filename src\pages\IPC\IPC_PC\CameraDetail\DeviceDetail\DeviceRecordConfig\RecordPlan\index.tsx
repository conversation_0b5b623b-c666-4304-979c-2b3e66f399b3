import React, { useState, useEffect, useCallback } from "react";
import { Image } from "antd-mobile";
import type { RadioValue } from "antd-mobile/es/components/radio";
import classNames from "classnames";
import styles from "./index.module.scss";
import { useTheme } from "@/utils/themeDetector";
import rightArrow from "@/Resources/icon/enter_right.png";
import rightArrowDark from "@/Resources/icon/enter_right_dark.png";
import picker from "@/Resources/icon/picker.png";
import RecordCustomize from "./RecordCustomize";
import { ICollapsePanel } from "@/layouts/Layout";
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { IDeviceDetail } from "../..";
import { PreloadImage } from "@/components/Image";
import finish from "@/Resources/icon/finish.png";
import finishDark from "@/Resources/icon/finish-dark.png";
import editDark from "@/Resources/icon/edit-dark.png";
import edit from "@/Resources/icon/edit.png";
import Modal from "@/components/Modal";
import { px2rem } from "@/utils/setRootFontSize";

export interface TimeSlot {
  id: number;
  start: string;
  end: string;
  repeat_policy: string;
  customized: string[];
  enabled: boolean;
  selected?: boolean;
}

const RecordPlanPC = (props: { propsCamera: (ICollapsePanel & ICameraDetail & IDeviceDetail), callback: (value: any, key: any, paramName: any) => void }) => {
  const { isDarkMode } = useTheme();
  const { propsCamera, callback } = props;
  const [customizePlanShow, setCustomizePlanShow] = useState<boolean>(false);

  // 自定义计划配置
  const [isEditing, setIsEditing] = useState(false);
  const [slots, setSlots] = useState<TimeSlot[]>([]);

  // 获取原始录制配置
  const [originalConfig, setOriginalConfig] = useState<any>(null);
  const [selectedPlan, setSelectedPlan] = useState<RadioValue>("全时段");
  const [scheduleCount, setScheduleCount] = useState<number>(0);

  const toggleEditMode = useCallback(() => {
    if (isEditing) {
      // 直接退出编辑模式
      setIsEditing(false);

      // 重置所有选择状态
      setSlots(slots.map(slot => ({
        ...slot,
        selected: false
      })));

      // 只有在有摄像头的情况下才保存
      if (propsCamera?.did) {
        // 准备录制计划
        const scheduleItems = slots.map((slot) => ({
          start: slot.start,
          end: slot.end,
          repeat_policy: slot.repeat_policy,
          customized: slot.customized,
          enabled: slot.enabled,
        }));

        callback({
          type: "customized",
          schedule: scheduleItems,
        }, 'recordPlan', 'record_schedule')
      }
    } else {
      // 进入编辑模式
      setIsEditing(true);
    }
  }, [callback, propsCamera?.did, isEditing, slots]);

  // 当摄像机ID可用时获取配置
  useEffect(() => {
    if (propsCamera) {
      // 设置计划类型
      if (propsCamera.data.recordConfig.recordPlan.type) {
        setSelectedPlan(propsCamera.data.recordConfig.recordPlan.type === "full_time" ? "全时段" : "自定义");

        // 设置时段数量
        if (propsCamera.data.recordConfig.recordPlan.schedule) {
          setScheduleCount(propsCamera.data.recordConfig.recordPlan.schedule.length);
        }
      }
    }
  }, [propsCamera]);

  const handlePlanChange = (value: RadioValue) => {
    // 如果点击已选中的全时段，则切换为未选中状态（显示自定义）
    if (value === selectedPlan && value === "全时段") {
      setSelectedPlan("自定义");

      // 保存设置到服务器
      if (propsCamera?.did) {
        callback({
          type: "customized",
          schedule: [],
        }, 'recordPlan', 'record_schedule')
      }
      return;
    }

    // 正常切换选中状态
    if (value === selectedPlan) return;

    setSelectedPlan(value);

    // 保存设置到服务器
    if (propsCamera?.did) {
      callback({
        type: value === "全时段" ? "full_time" : "customized",
        schedule: [],
      }, 'recordPlan', 'record_schedule')
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.title}>录制计划</div>
      <div className={styles.planCards}>
        <div
          className={classNames(styles.planCard, {
            [styles.selected]: selectedPlan === "全时段"
          })}
          onClick={() => handlePlanChange("全时段")}
        >
          <div className={styles.planContent}>
            <div className={styles.checkIconWrapper}>
              {selectedPlan === "全时段" && (
                <Image src={picker} className={styles.checkIcon} />
              )}
            </div>
            <div className={styles.planInfo}>
              <div className={styles.planTitle}>全时段</div>
            </div>
          </div>
        </div>

        <div
          className={classNames(styles.planCard, {
            [styles.selected]: selectedPlan === "自定义"
          })}
          onClick={() => {
            handlePlanChange("自定义");
          }}
        >
          <div className={styles.planContent}>
            <div className={styles.checkIconWrapper}>
              {selectedPlan === "自定义" && (
                <Image src={picker} className={styles.checkIcon} />
              )}
            </div>
            <div className={styles.planInfo}>
              <div className={styles.planTitle}>自定义</div>
              {selectedPlan === "自定义" && scheduleCount > 0 && (
                <div className={styles.planSubtitle}>{scheduleCount}个时段</div>
              )}
              {selectedPlan === "自定义" && scheduleCount === 0 && (
                <div className={styles.planSubtitle}>无时段</div>
              )}
            </div>
            <Image
              src={isDarkMode ? rightArrowDark : rightArrow}
              className={styles.arrowIcon}
              onClick={() => setCustomizePlanShow(true)}
            />

            <Modal title="自定义" isShow={customizePlanShow} onCancel={() => setCustomizePlanShow(false)} footer={null} content={
              <RecordCustomize isEditing={isEditing} setIsEditing={setIsEditing} camera={propsCamera} setOriginalConfig={setOriginalConfig} originalConfig={originalConfig} setSlots={setSlots} slots={slots} callback={callback} />
            } right={<PreloadImage style={{ cursor: 'pointer', height: px2rem("28px"), width: px2rem("28px"), display: slots.length > 0 ? 'block' : 'none' }} src={isEditing ? isDarkMode ? finishDark : finish : isDarkMode ? editDark : edit} onClick={toggleEditMode} />} contentStyle={{ width: px2rem("500px"), height: px2rem("450px") }} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default RecordPlanPC;