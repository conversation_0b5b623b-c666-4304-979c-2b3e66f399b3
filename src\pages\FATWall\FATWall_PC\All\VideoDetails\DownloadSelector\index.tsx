import React, { useState, useCallback } from 'react';
import { Modal, message, Checkbox } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import { MediaFileInfo } from '@/api/fatWall';
import { downloadFiles } from '@/api/fatWallJSBridge';
import {Toast} from '@/components/Toast/manager';

interface DownloadSelectorProps {
  visible: boolean;
  mediaFiles: MediaFileInfo[];
  onClose: () => void;
  classes: string;
  mediaDetails: any;
}

const DownloadSelector: React.FC<DownloadSelectorProps> = ({
  visible,
  mediaFiles,
  onClose,
  classes,
  mediaDetails
}) => {
  const [selectedFiles, setSelectedFiles] = useState<number[]>([]);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  // 处理单个文件选择
  const handleFileSelect = useCallback((fileId: number, event?: React.MouseEvent) => {
    if (isDownloading) return;

    // 阻止事件冒泡，避免触发整行点击
    if (event) {
      event.stopPropagation();
    }

    setSelectedFiles(prev => {
      if (prev.includes(fileId)) {
        return prev.filter(id => id !== fileId);
      } else {
        return [...prev, fileId];
      }
    });
  }, [isDownloading]);

  // 处理全部下载
  const handleDownloadAll = useCallback(() => {
    if (isDownloading) return;
    if (!mediaFiles || mediaFiles.length === 0) {
      message.error('暂无可下载的文件');
      return;
    }

    setIsDownloading(true);

    // 构造文件信息列表
    const fileList = mediaFiles.map(file => ({
      name: file.path.split('/').pop() || `第${file.episode}集`,
      path: file.path,
      mtime: file.mtime || '',
      size: file.file_size || 0
    }));

    // 调用下载接口
    downloadFiles(fileList, (res) => {
      if (res.code === 0) {
        Toast.show('下载任务已添加到任务中心');
        onClose();
      } else {
        Toast.show(`下载失败: ${res.msg}`);
      }
      setIsDownloading(false);
    }).catch((error) => {
      Toast.show(error.message || '下载失败');
      setIsDownloading(false);
    });
  }, [mediaFiles, isDownloading, onClose]);

  // 处理选中文件下载
  const handleDownloadSelected = useCallback(() => {
    if (isDownloading) return;
    if (selectedFiles.length === 0) {
      Toast.show('请选择要下载的文件');
      return;
    }

    setIsDownloading(true);

    // 根据选中的文件ID筛选文件
    const selectedMediaFiles = mediaFiles.filter(file => selectedFiles.includes(file.file_id));
    
    const fileList = selectedMediaFiles.map(file => ({
      name: file.path.split('/').pop() || `第${file.episode}集`,
      path: file.path,
      mtime: file.mtime || '',
      size: file.file_size || 0
    }));

    // 调用下载接口
    downloadFiles(fileList, (res) => {
      if (res.code === 0) {
        Toast.show('下载任务已添加到任务中心');
        onClose();
      } else {
        Toast.show(`下载失败: ${res.msg}`);
      }
      setIsDownloading(false);
    }).catch((error) => {
      Toast.show(error.message || '下载失败');
      setIsDownloading(false);
    });
  }, [selectedFiles, mediaFiles, isDownloading, onClose]);

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    if (!seconds) return '00:00';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  };

  // 渲染电视剧模式（网格布局）
  const renderTVSeries = () => {
    return (
      <div className={styles.episodeGrid}>
        {mediaFiles.map((file) => (
          <div
            key={file.file_id}
            className={`${styles.episodeItem} ${selectedFiles.includes(file.file_id) ? styles.selected : ''}`}
            onClick={() => handleFileSelect(file.file_id)}
          >
            <span className={styles.episodeNumber}>{file.episode}</span>
          </div>
        ))}
      </div>
    );
  };

  // 渲染电影模式（列表布局）
  const renderMovie = () => {
    return (
      <div className={styles.movieList}>
        {mediaFiles.map((file) => (
          <div
            key={file.file_id}
            className={styles.movieItem}
            onClick={() => handleFileSelect(file.file_id)}
          >
            <div className={styles.moviePoster}>
              {mediaDetails?.poster && mediaDetails.poster.length > 1 ? (
                <img src={mediaDetails.poster[1]} alt={mediaDetails.trans_name || mediaDetails.origin_name} />
              ) : (
                <div className={styles.placeholderPoster}>
                  {(mediaDetails?.trans_name || mediaDetails?.origin_name || '未知')?.charAt(0)}
                </div>
              )}
            </div>
            <div className={styles.movieInfo}>
              <div className={styles.movieTitle}>
                {mediaDetails?.trans_name || mediaDetails?.origin_name || '未知影片'}
              </div>
              <div className={styles.movieDuration}>
                {formatDuration(file.duration || 0)}
              </div>
            </div>
            <div className={styles.movieCheckbox}>
              <Checkbox
                checked={selectedFiles.includes(file.file_id)}
                onChange={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡，避免重复触发
                  handleFileSelect(file.file_id);
                }}
              />
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
      className={styles.downloadModal}
      closeIcon={<CloseOutlined />}
      title="选择下载视频"
    >
      <div className={styles.content}>
        {classes === '电视剧' ? renderTVSeries() : renderMovie()}
      </div>
      
      <div className={styles.footer}>
        <button
          className={styles.downloadAllButton}
          onClick={handleDownloadAll}
          disabled={isDownloading}
        >
          全部下载
        </button>
        <button
          className={`${styles.downloadSelectedButton} ${selectedFiles.length === 0 ? styles.disabled : ''}`}
          onClick={handleDownloadSelected}
          disabled={isDownloading || selectedFiles.length === 0}
        >
          下载选中 ({selectedFiles.length})
        </button>
      </div>
    </Modal>
  );
};

export default DownloadSelector;
