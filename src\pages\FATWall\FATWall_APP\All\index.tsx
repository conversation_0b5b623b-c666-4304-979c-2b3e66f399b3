import { Dispatch, FC, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.scss';
import { PreloadImage } from '@/components/Image';
import arrow from '@/Resources/icon/arrow.png';
import arrow_dark from '@/Resources/icon/arrow_dark.png';
import { useTheme } from '@/utils/themeDetector';
import { useHistory, useRouteMatch, useLocation } from 'react-router-dom';
// import VideoDetails from '@/pages/FATWall/FATWall_APP/VideoDetails';
import close from '@/Resources/icon/close.png';
import close_dark from '@/Resources/icon/close_white.png';
import FilterFilmCard from '../../../../components/FATWall_APP/FilterFilmCard';
import FilmFilter from '../../../../components/FATWall_APP/FilmFilter';
import Pull2Refresh from '@/components/Pull2Refresh';
import FloatPanel from '@/components/FloatPanel';
import { CheckList } from 'antd-mobile';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { px2rem } from '@/utils/setRootFontSize';
import { defaultFiltersByApp, filterItemType, filterTypeList } from '@/components/FATWall_APP/FATWALL_CONST';
import { useEventListener, useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { getMediaListFromLib, mediaProps } from '@/api/fatWall';
import { useLibraryListApp } from '..';
import FATErrorComponents from '../../FATWall_PC/Error';
import { defaultPageParam } from '../Recently';
import { Toast } from '@/components/Toast/manager';
import XMLoading from '@/components/XMLoading';

// 处理筛选条件请求属性
export const handleFilter = (filter: { [key: string]: any }) => {
  let f = { ...filter };
  // 处理收藏、已观看筛选条件
  switch (filter.collect) {
    case 'all-collect': {
      delete f.seen;
      delete f.favourite;
      break;
    }
    case 'collect': {
      f.favourite = 1;
      break;
    }
    case 'viewed': {
      f.seen = 1;
      break;
    }
  }
  delete f.collect;

  // 处理年份筛选条件
  switch (filter.year) {
    case 'all-year': break;
    case '1990s': {
      f.year_start = 1990;
      f.year_end = 1999;
      break;
    }
    case '1980s': {
      f.year_start = 1980;
      f.year_end = 1989;
      break;
    }
    case '2000s': {
      f.year_start = 2000;
      f.year_end = 2009;
      break;
    }
    case '2010s': {
      f.year_start = 2010;
      f.year_end = 2019;
      break;
    }
    case 'other-year': {
      f.year_start = null;
      f.year_end = 1980;
      break;
    }
    default: {
      // const [end, start] = filter.year.split('-');
      // f.year_start = parseInt(start);
      // f.year_end = parseInt(end);
      f.year_start = parseInt(filter.year);
      f.year_end = parseInt(filter.year);
      break;
    }
  }
  delete f.year;

  // 处理影片范围筛选条件
  switch (filter.classes) {
    case 'all-classes': delete f.classes; break;
    case 'tv': f.classes = '电视剧'; break;
    case 'movie': f.classes = '电影'; break;
  }

  let newFilter: { [key: string]: any } = { ...f };
  Object.keys(f).forEach((key) => {
    const item = filterTypeList.find((it) => it.type === key);
    if (item) {
      const it = item.typeList.find((it) => it.key === f[key]);
      newFilter[key] = it ? it.title : '全部';
    }
  })

  Object.keys(newFilter).forEach((key) => {
    if (newFilter[key] === '全部' || newFilter[key] === undefined) {
      delete newFilter[key];
    }
  })

  if (newFilter['kind']) {
    newFilter['kind'] = [newFilter['kind']];
  }

  return newFilter;
}

const All: FC<{
  showFloatPanel: boolean;
  setShowFloatPanel: Dispatch<SetStateAction<boolean>>
}> = (props) => {
  const { isDarkMode } = useTheme();
  const { showFloatPanel, setShowFloatPanel } = props;
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByApp);
  const history = useHistory();
  const { path } = useRouteMatch();
  const location = useLocation<{ needRefreshLibraries?: boolean; shouldRefresh?: boolean }>();
  const [collapse, setCollapse] = useState<boolean>(true); // app端默认折叠
  const containerRef = useRef<HTMLDivElement | null>(null);

  const { libs: libraries, refreshLibraries } = useLibraryListApp();
  const { runAsync: getMediasFromLib } = useRequest(getMediaListFromLib, { manual: true }); // 根据媒体库获取所有资源
  const [medias, setMedias] = useState<mediaProps[]>([]);
  const [filterItem, setFilterItem] = useState<filterItemType>({ sort_type: 0, asc: 0 });
  const filterItemRef = useRef<filterItemType>({ sort_type: 0, asc: 0 });
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByApp); // 记录之前的筛选条件，用于判断筛选条件是否变化

  const [isError, setIsError] = useState<boolean>(false); // 是否出现错误信息

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const timer = useRef<NodeJS.Timeout | null>(null); // 防抖定时器
  const [libraryLoading, setLibraryLoading] = useState<boolean>(false); // 媒体库加载状态

  const [needSticky, setNeedSticky] = useState<boolean>(false); // 筛选栏是否需要吸顶

  useEventListener('scroll', () => {
    if (containerRef.current && containerRef.current.scrollTop === 0) setNeedSticky(false);
  }, { target: containerRef });

  // 当吸顶时触碰到filmCard应该折叠筛选栏
  const filmCardTouch = useCallback(() => {
    if (needSticky) setCollapse(true);
  }, [needSticky]);

  // 初始化判断媒体库是否扫描完成
  useEffect(() => {
    const checkLibs = libraries.some(it => it.status !== '扫描完成');
    if (checkLibs) {
      console.log('当前媒体库有未扫描完成的项目');
      setLibraryLoading(true);
      timer.current = setInterval(() => {
        refreshLibraries(true);
      }, 3000);
    } else {
      console.log('当前媒体库已扫描完成');
      if (timer.current) { // 当有定时器时才显示toast
        timer.current && clearInterval(timer.current);
        Toast.show('媒体库已扫描完成,请刷新资源');
      }
      setLibraryLoading(false);
    }

    return () => {
      console.log('离开首页，清除定时器');
      timer.current && clearInterval(timer.current);
    }
  }, [libraries, refreshLibraries])

  // 媒体库数据
  const filterFilm = useMemo(() => {
    return medias.map((item) => {
      return { label: item.trans_name, score: item.score || 0, time: `${item.year}`, cover: item.poster.length > 0 ? item.poster[0] : '', isLike: item.favourite, lib_id: 0, media_id: item.media_id, classes: item.classes }
    })
  }, [medias])

  const initMediaInfo = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    const mediaRes = await getMediasFromLib({ lib_id: 0, filter: { ...pageOptRef.current, ...filterItemRef.current, ...filter } }).catch((e) => console.log('获取媒体库影视列表失败：', e));
    if (mediaRes && mediaRes.code === 0 && mediaRes.data) {
      if (mediaRes.data.count < pageOptRef.current.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      callback(mediaRes.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      return;
    }
    setIsError(true);
  }, [getMediasFromLib])

  // 初始化数据
  useEffect(() => {
    initMediaInfo((data) => setMedias(data), handleFilter(defaultFiltersByApp));
  }, [initMediaInfo])

  // 监听从其他页面返回时的刷新需求
  useEffect(() => {
    if (location.state?.shouldRefresh) {
      // 重置分页参数
      setHasMore(false);
      pageOptRef.current = defaultPageParam;
      // 重新获取数据
      initMediaInfo((data) => setMedias(data), handleFilter(filters));

      // 清除state中的刷新标识，避免重复刷新
      history.replace({ ...location, state: { ...location.state, shouldRefresh: false } });
    }
  }, [location.state?.shouldRefresh, initMediaInfo, filters, history, location])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      initMediaInfo((data) => setMedias(p => [...p, ...data]), handleFilter(filters));
    }
  }, [inViewport, initMediaInfo])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilter(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(false);
    pageOptRef.current = defaultPageParam; // 重置页数
    initMediaInfo((data) => setMedias(data), filter);
  }, [initMediaInfo])

  useUpdateEffect(() => {
    pageOptRef.current = defaultPageParam; // 重置页数
    filterItemRef.current = { ...filterItemRef.current, sort_type: filterItem.sort_type, asc: filterItem.asc }; // 记录之前的筛选条件，用于判断筛选条件是否变化
    initMediaInfo((data) => setMedias(data), handleFilter(filters));
  }, [filterItem])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    // 筛选条件更新的时候 滚动回顶部
    containerRef.current && containerRef.current.scrollTo({ top: 0, behavior: 'smooth' });
    getDataByFilters(filters); // 筛选条件更新的时候，重置页数，重置是否还有更多数据
  }, [filters, getDataByFilters])

  const clearAndRefresh = useCallback(() => {
    setFilters(defaultFiltersByApp); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByApp)) {
        getDataByFilters(defaultFiltersByApp);
      }
    }
  }, [getDataByFilters])

  // 检测是否需要刷新媒体库列表
  useEffect(() => {
    if (location.state?.needRefreshLibraries) {
      refreshLibraries();
      initMediaInfo((data) => setMedias(data), handleFilter(defaultFiltersByApp)); // 更新完lib后需要再请求一次数据
      // 清除state中的标志，避免重复刷新
      history.replace({ pathname: location.pathname, state: {} });
    }
  }, [location.state, refreshLibraries, history, location.pathname, initMediaInfo])

  // 改变筛选条件
  const checkListOnchange = useCallback((val: CheckListValue[], type: 'asc' | 'sort_type') => {
    setFilterItem(prev => {
      let p: filterItemType | any = { ...prev };
      p[type] = val[0];
      return p;
    })

    setShowFloatPanel(false); // 关闭筛选面板
  }, [setShowFloatPanel])

  const toDarmaOrMovie = useCallback((item: any) => {
    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });

    window.onetrack?.('track', 'mediacenter_allPageFilm_click', { media_id: item.media_id });

    history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
  }, [history, path])

  useEffect(() => {
    // 全部页面的曝光事件
    window.onetrack?.('track', 'mediacenter_allPage_expose');

    // if (isError || medias.length === 0) {
    //   setCollapse(true); // 如果没有影片数据，则默认收起筛选条件
    // }

    // if (medias.length > 0 && pageOptRef.current.offset === 0) {
    //   setCollapse(false); // 如果有影片数据，则默认展开筛选条件
    // }
  }, [isError, medias.length])

  // useEffect(() => {
  //   // 当筛选条件展开后,滚动到顶部
  //   if (!collapse && containerRef && containerRef.current) {
  //     containerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
  //   }
  // }, [collapse])

  return (
    <div className={styles.all_container}>

      {/* 媒体库 */}
      <div className={styles.collapse_library_container}>
        <div className={styles.collapse_library_scroll_container}>
          <div className={styles.collapse_library_scroll_container_item_container}>
            {
              libraries.length > 0 && libraries.slice(0, 10).map((item) => (
                <div className={styles.collapse_library_container_content} key={item.name} onClick={() => history.push({
                  pathname: `/${path.split('/')[1]}/library`,
                  state: { title: item.name, lib_id: item.lib_id }
                })}>
                  <span>{item.name}</span>
                  <span className={styles.collapse_library_container_content_right}>
                    {
                      libraryLoading && item.status !== '扫描完成' ? <div className={styles.loading_container}>
                        <span>{item.percent && `${item.percent > 100 ? 100 : item.percent.toFixed(1)}%`}</span>
                        <XMLoading size={'small'} loading={true} />
                      </div> : <>
                        <span>{item.count}</span>
                        <PreloadImage className={styles.collapse_library_container_content_right_img} style={{ transform: 'rotate(90deg)' }}
                          src={isDarkMode ? arrow_dark : arrow} alt='enter_arrow' />
                      </>
                    }
                  </span>
                </div>
              ))
            }
            {
              libraries.length > 10 &&
              (
                <div className={styles.collapse_library_container_content} style={{ width: px2rem('60px') }} onClick={() => history.push(`/${path.split('/')[1]}/allLibrary`)}>
                  <span>更多</span>
                </div>
              )
            }
          </div>
        </div>
      </div>

      <div className={styles.container} ref={containerRef}>

        {/* 条件筛选 */}
        {
          (!isError && libraries.length !== 0) &&
          <FilmFilter filters={filters} stickyCallback={setNeedSticky} stickyValue={needSticky} filterTypeList={filterTypeList} controlFilters={setFilters} value={collapse} onChange={setCollapse} />
        }

        {/* 下拉刷新组件 */}
        <Pull2Refresh onTouchStart={filmCardTouch} style={{ height: `${filterFilm.length === 0 ? `calc(100% - 14px - ${collapse ? px2rem('40px') : px2rem('320px')})` : ''}` }}
          onRefresh={clearAndRefresh} isTrigger={collapse} containerRef={containerRef}>
          <FATErrorComponents refresh={clearAndRefresh} show={isError || libraries.length === 0 || filterFilm.length === 0} span={isError ? '获取失败' : '暂无内容'}
            canTry={isError} subSpan={(isError || libraries.length === 0) ? undefined : filterFilm.length === 0 ? '请在媒体库关联的文件夹中添加视频' : undefined}>

            {/* 影片渲染 */}
            <div className={styles.filter_films_container}>
              {
                filterFilm.map((item, index) => (
                  <FilterFilmCard type='app' {...item} key={item.label + index} title={item.label} subtitle={item.time} score={item.score} cover={item.cover} isLike={item.isLike ? true : false} onCardClick={() => toDarmaOrMovie(item)} />
                ))
              }
            </div>
          </FATErrorComponents>
        </Pull2Refresh>

        {
          hasMore && (<div ref={loaderRef} style={{ padding: px2rem('20px'), height: px2rem('20px') }}></div>)
        }

        {/* 弹出浮动面板 */}
        <FloatPanel showFloatPanel={showFloatPanel} setShowFloatPanel={setShowFloatPanel}>
          <div className={styles.filter_float_panel_container}>
            <div className={styles.filter_float_panel_navBar}>
              <PreloadImage src={isDarkMode ? close_dark : close} alt='close' onClick={() => setShowFloatPanel(false)} />
              <span>筛选</span>
            </div>
            <div className={styles.filter_float_panel_content}>
              <span className={styles.filter_float_panel_content_list_title}>筛选</span>
              <div className={styles.filter_float_panel_content_check_list_container}>
                <CheckList value={[filterItem.sort_type]} onChange={(v) => checkListOnchange(v, 'sort_type')}>
                  <CheckList.Item style={{ color: filterItem.sort_type === 0 ? 'var(--primary-color)' : '' }} value={0}>添加时间</CheckList.Item>
                  <CheckList.Item style={{ color: filterItem.sort_type === 1 ? 'var(--primary-color)' : '' }} value={1}>评分</CheckList.Item>
                </CheckList>
              </div>
              <span className={styles.filter_float_panel_content_list_title}>筛选</span>
              <div className={styles.filter_float_panel_content_check_list_container}>
                <CheckList value={[filterItem.asc]} onChange={(v) => checkListOnchange(v, 'asc')}>
                  <CheckList.Item style={{ color: filterItem.asc === 0 ? 'var(--primary-color)' : '' }} value={0}>正序</CheckList.Item>
                  <CheckList.Item style={{ color: filterItem.asc === 1 ? 'var(--primary-color)' : '' }} value={1}>倒序</CheckList.Item>
                </CheckList>
              </div>
            </div>
          </div>
        </FloatPanel>
      </div>
    </div >
  )
}

export default All;