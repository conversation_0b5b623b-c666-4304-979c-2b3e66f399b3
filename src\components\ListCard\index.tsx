import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '../Image';
import styles from './index.module.scss';
import arrow from '@/Resources/icon/arrow.png';
// import moreIcon from "@/Resources/icon/moreOps.png";
import arrow_dark from '@/Resources/icon/arrow_dark.png';
import more_dark from '@/Resources/icon/moreOps_dark.png';
import more_light from '@/Resources/icon/moreOps.png';
import computer from '@/Resources/icon/computer_dark.png';
import computer_light from '@/Resources/icon/computer_light.png';


import { Popover, Toast } from 'antd-mobile';
import { useState } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { modalShow } from '../List';

type listCardType = 'arrow' | 'more'

export interface IListCard {
  lib_id?: number;
  avatar?: string;
  title: string;
  subtitle?: string;
  type: listCardType
  isDel?: boolean
  scan_percent?: number;
  scan_status?: string;
  tv_visable?: number;
  scan_error_code?: string;
  onCallback?: (v: Omit<IListCard, 'onCallback'>) => void;
  onDelete?: (lib_id?: number) => void;
  onEdit?: (item: IListCard) => void;
  onScan?: (lib_id?: number) => void;
}


const ListCard = (props: IListCard) => {
  const { avatar, title, type, subtitle, onCallback, isDel, onDelete, onEdit, onScan, scan_status, scan_percent, scan_error_code, tv_visable } = props;
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const routeMatch = useRouteMatch();
  const path = routeMatch?.path.split('/')[1] || '';

  const [moreVisible, setMoreVisible] = useState(false);

  const handleScan = () => {
    setMoreVisible(false);
    if (onScan && props.lib_id) {
      onScan(props.lib_id);
    }
  };

  // 根据真实扫描状态返回显示文本和样式
  const getScanningText = () => {
    switch (scan_status) {
      case '扫描中':
        return `扫描中... ${Math.round(scan_percent || 0)}%`;
      case '扫描完成':
        return subtitle;
      case '扫描异常':
        return `扫描失败：${scan_error_code || '未知错误'}`;
      case '未开始':
      default:
        return subtitle;
    }
  };

  // 判断是否显示扫描状态样式
  const isScanning = scan_status === '扫描中';
  const isScanFailed = scan_status === '扫描异常';
  const isScanCompleted = subtitle === '扫描完成'; // 检测是否是扫描完成状态

  const deleteSelectedItems = () => {
    modalShow(
      "退出当前媒体库",
      <div className={styles.modalConfirmText}>{`是否确认退出"${title}"分享的媒体库？`}</div>,
      (m) => {
        m.destroy();
        Toast.show({
          content: "删除成功",
          duration: 2000,
          position: "bottom",
        });
        onDelete && onDelete(props.lib_id);
      },
      () => { }, // onCancel
      false, // onlyShow
      {
        okBtnText: "退出",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom",
      }
    );
  };


  return (
    <div className={styles.container} style={{ cursor: onCallback ? 'pointer' : '' }} onClick={() => onCallback && onCallback(props)}>
      <div className={styles.left}>
        <div className={styles.avatar}>
          <PreloadImage src={avatar || ''} alt='avatar' />
        </div>
        <div className={styles.left_content}>
          <span className={styles.title}>{title}</span>
          <div className={styles.infoItem}>
            <span className={styles.subtitle}>
              {tv_visable === 1 ? <PreloadImage src={isDarkMode? computer :computer_light || ''} style={{ width: '20px', height: '20px' }} alt='avatar' /> : null}
              {tv_visable === 1 ? '电视可见 | ' : null}
            </span>
            {isScanning ? (
              <span className={styles.scanningText}>
                {getScanningText()}
              </span>
            ) : isScanFailed ? (
              <span className={styles.scanningFailedText}>
                {getScanningText()}
              </span>
            ) : isScanCompleted ? (
              <span className={styles.scanCompletedText}>
                {getScanningText()}
              </span>
            ) : (
              <span className={styles.subtitle}>{getScanningText()}</span>
            )}
          </div>

        </div>
      </div>
      <div className={styles.right}>
        {type === 'arrow' && (<PreloadImage style={{ transform: 'rotate(90deg)' }} src={isDarkMode ? arrow_dark : arrow} alt='arrow' />)}
        {type === 'more' && (
          <div className={styles.right}>
            <Popover
              className={styles.morePopoverContainer}
              visible={moreVisible}
              onVisibleChange={setMoreVisible}
              content={

                <div className={styles.morePopover}>
                  {isDel && (
                    <div className={styles.morePopoverItem} >
                      <p className={styles.morePopoverText} onClick={deleteSelectedItems}>退出媒体库</p>
                    </div>
                  )}
                  {!isDel && (
                    <div className={styles.morePopoverItem} >
                      <p className={styles.morePopoverText} onClick={() => {
                        setMoreVisible(false);
                        onEdit ? onEdit(props) : history.push({
                          pathname: `/${path}/createLibrary`,
                          state: { isEdit: true }
                        });
                      }}>编辑</p>
                      <p className={styles.morePopoverText} onClick={handleScan}>扫描</p>
                    </div>
                  )}
                </div>
              }
              trigger='click'
              placement='bottom-end'
              style={{ '--arrow-size': '0px' } as React.CSSProperties}
            >
              <PreloadImage src={isDarkMode ? more_dark : more_light} style={{ height: '40px', width: '40px' }} alt="more" onClick={() => setMoreVisible(true)} />
            </Popover>
          </div>)}
      </div>
    </div>
  )
}

export default ListCard;