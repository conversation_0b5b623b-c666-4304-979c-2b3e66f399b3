import React, { <PERSON> } from "react";
import { Popup } from "antd-mobile";
import styles from "./index.module.scss";
import picker from '@/Resources/icon/picker.png'

export interface Option {
  label: string;
  value: string;
}

interface UnitSelectorProps {
  visible: boolean;
  onClose: () => void;
  value: string;
  options: Option[];
  onChange: (value: string) => void;
  title?: string;
}

const UnitSelector: FC<UnitSelectorProps> = ({
  visible,
  onClose,
  value,
  options,
  onChange,
  title = "选择单位",
}) => {
  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      position="bottom"
      bodyStyle={{
        borderTopLeftRadius: "16px",
        borderTopRightRadius: "16px",
      }}
    >
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.title}>{title}</div>
        </div>
        <div className={styles.optionList}>
          {options.map((option) => (
            <div
              key={option.value}
              className={styles.optionItem}
              onClick={() => {
                onChange(option.value);
                onClose();
              }}
            >
              <span className={styles.optionLabel}>{option.label}</span>
              {value === option.value && (
                <img
                  src={picker}
                  alt=""
                  className={styles.checkIcon}
                />
              )}
            </div>
          ))}
        </div>
        {/* <div className={styles.footer}>
          <div className={styles.cancelButton} onClick={onClose}>
            取消
          </div>
          <div className={styles.confirmButton} onClick={onClose}>
            确定
          </div>
        </div> */}
      </div>
    </Popup>
  );
};

export default UnitSelector;
