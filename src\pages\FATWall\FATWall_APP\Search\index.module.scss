// 搜索界面
.matchCorrectionOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-color);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.searchContainer {
  padding: 14px 16px;
  padding-top: 35px;
  display: flex;
  align-items: center;
  background-color: var(--background-color);
  flex-shrink: 0; /* 防止搜索栏被压缩 */
}

.searchInputWrapper {
  flex: 1;
  background-color: var(--cancel-btn-background-color);
  border-radius: 16px;
  overflow: hidden;
  padding: 0;
  display: flex;
  align-items: center;
}

.searchIconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2px 0 10px;
}

.searchIcon {
  font-size: 18px;
  color: var(--list-value-text-color);
}

.searchInput {
  --font-size: 16px;
  --color: var(--text-color);
  --placeholder-color: var(--thinLine-background-color);
  height: 36px;
  flex: 1;
  border: none;
  background: transparent;
  padding: 0 10px 0 0;
  outline: none;
  font-size: 16px;
  color: var(--text-color);

  &::placeholder {
    color: var(--thinLine-background-color);
  }
}

.cancelButton {
  margin-left: 12px;
  padding: 8px 0;
  font-size: 16px;
  color: var(--primary-color);
  cursor: pointer;
  white-space: nowrap;
}

.searchResults {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  -webkit-overflow-scrolling: touch; // 为iOS提供平滑滚动
  background-color: var(--background-color);

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
    /* Chrome, Safari, Opera */
  }


/* 搜索状态容器 */
.searchStateContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: 20px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--thinLine-background-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  margin-top: 12px;
  font-size: 14px;
  color: var(--list-value-text-color);
}

.errorBlock {
  margin-bottom: 16px;
}

.retryButton {
  margin-top: 16px;
  font-size: 16px;
}

/* 固定的分类标签栏样式 */
.search_category_tabs_fixed {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  margin-bottom: 8px;
  min-height: 60px; /* 确保有足够的高度 */
  flex-shrink: 0; /* 防止被压缩 */

  .category_tab {
    background-color: var(--search-tab-bg);
    // width: 65px;
    height: 29px;
    flex: 0 0 auto;
    line-height: 29px;
    text-align: center;
    border-radius: 14.5px;
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
    padding: 0 16px;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.8;
    }

    &.active_tab {
      background-color: var(--search-tab-active-bg);
      color: #3482ff;
      font-weight: 600;
    }
  }
}

/* 可滚动的搜索结果区域 */
.search_results_scrollable {
  flex: 1;
  overflow-y: auto;
  height: calc(100vh - 180px); /* 减去顶部固定区域的高度 */
}

.filter_films_container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 0 8px;
  padding-bottom: 80px;
}
