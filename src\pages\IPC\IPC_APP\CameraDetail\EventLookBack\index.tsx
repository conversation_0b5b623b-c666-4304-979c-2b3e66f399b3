import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import styles from "./index.module.scss";
import { endOfDay, format, startOfDay } from "date-fns";
import { zhCN } from "date-fns/locale";
import rightArrow from "@/Resources/icon/<EMAIL>";
import rightArrow_dark from "@/Resources/icon/<EMAIL>";
import { PreloadImage } from "@/components/Image";
import { Divider } from 'antd-mobile';
// import { useHistory } from "react-router-dom";
// import { IlLookBackData } from "@/components/CameraPlayer/components/plugin/ControlPlugin/PlayerControl";
import DatePicker from "@/components/CameraPlayer/components/DatePicker/DatePicker";
import EventList from "@/components/CameraPlayer/components/EventList/EventList";
import { eventDefinition, EventLookBackType, EventType } from "@/components/CameraPlayer/constants";
import { IEventVideo } from "..";
import { IEventListCard } from "@/components/CameraPlayer/components/EventList/EventListCard";
import { CameraEventData, getVideoRecord } from "@/api/ipc";
import { useInViewport, useUpdateEffect } from "ahooks";
import { Toast } from "@/components/Toast/manager";
import { goToIPCPlayBack } from "@/api/cameraPlayer";
import { useTheme } from "@/utils/themeDetector";

const now = new Date();
const defaultPageOptions = { size: 5, token: '' };

const EventLookBack = () => {
  const [startTime, setStartTime] = useState<Date>(now);
  const [endTime, setEndTime] = useState<Date>(now);
  const [eventData, setEventData] = useState<IEventVideo[]>([]);
  const [selectStartTime, setSelectStartTime] = useState<boolean>(false);
  const [selectEndTime, setSelectEndTime] = useState<boolean>(false);
  const [eventFilter, setEventFilter] = useState<EventLookBackType[]>(['all']);
  const pageRef = useRef<{ size: number, token: string }>(defaultPageOptions);
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);
  const { isDarkMode } = useTheme();
  const eventParamsRef = useRef<string[]>(['all']);
  const timeRef = useRef<{ start: number, end: number }>({ start: startOfDay(startTime).getTime(), end: endOfDay(endTime).getTime() });

  // 获取当日事件数据
  const getTodayEventData = useCallback(async (callback: (data: CameraEventData) => void) => {
    let eventName: string[] = [];
    eventParamsRef.current.forEach(it => {
      if (it === 'all') {
        eventName = Object.keys(EventType) as EventLookBackType[];
        return;
      }
      eventName.push(it);
    })

    const params = {
      page: pageRef.current,
      options: {
        option: ['event_name', 'time'],
        event_name: eventName,
        time: {
          start: timeRef.current.start,
          end: timeRef.current.end
        }
      }
    }
    const res = await getVideoRecord(params).then().catch(() => null);

    if (res && res.code === 0 && res.data) {
      callback(res.data);
      pageRef.current = { ...pageRef.current, token: res.data.page.token }

      // 根据列表返回的数量判断是否还有更多数据
      if (res.data.videos.length < pageRef.current.size) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
    }

    if (res && res.code === 5000) {
      Toast.show('请求超时，请稍后再试');
    }
  }, [])

  // 筛选事件类型
  useUpdateEffect(() => {
    if (eventFilter) {
      eventParamsRef.current = eventFilter;
      pageRef.current = defaultPageOptions;
      getTodayEventData((data) => {
        setEventData(data.videos.map((it, index) => ({ ...it, id: it.camera_lens + index })));
      });
    }
  }, [eventFilter, getTodayEventData])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      getTodayEventData((data) => {
        setEventData(p => [...p, ...data.videos.map((it, index) => ({ ...it, id: it.camera_lens + index }))]);
      });
    }
  }, [inViewport, getTodayEventData])

  // 初始化的时候根据时间获取当日事件数据
  useEffect(() => {
    if (startTime.getTime() > endTime.getTime()) {
      Toast.show('开始时间不能大于结束时间');
      setStartTime(endTime);
      return;
    }
    timeRef.current = { start: startOfDay(startTime).getTime(), end: endOfDay(endTime).getTime() };
    pageRef.current = defaultPageOptions; // 时间更新的时候重置分页信息，重新获取数据
    getTodayEventData((data) => {
      setEventData(data.videos.map((it, index) => ({ ...it, id: it.camera_lens + index })));
    });
  }, [endTime, getTodayEventData, startTime])

  const eventList: IEventListCard[] = useMemo(() => {
    const temp: (IEventListCard)[] = [];
    eventData.filter((it) => it.event_name !== '').forEach(async (it) => {
      temp.push({
        id: it.id,
        icon: eventDefinition[it.event_name].icon,
        title: eventDefinition[it.event_name].label,
        subtitle: format(new Date(Number(it.time)), 'HH:mm', { locale: zhCN }),
        poster: `${it.cover_file}/original.jpg`,
        name: it.event_name,
        file: it.file,
        urls: eventData.filter((item) => item.event_name === it.event_name).map((it) => it.file)
      })
    })
    return temp;
  }, [eventData])

  const timeFormat = useMemo(() => {
    return {
      startTime: format(startTime, 'yyyy年MM月dd日', { locale: zhCN }),
      endTime: format(endTime, 'yyyy年MM月dd日', { locale: zhCN })
    }
  }, [endTime, startTime])

  // const history = useHistory();

  const lookBackDetail = useCallback(async (item) => {
    // 事件回放详情页跳转逻辑
    try {
      const index = eventData.findIndex(it => it.id === item.id);
      if (index !== -1) {
        await goToIPCPlayBack(eventData, index, '').catch(e => console.error('跳转事件回看详情页失败', e && JSON.stringify(e)));
      }
    } catch (e) {
      console.error('跳转事件回看详情页失败', e && JSON.stringify(e));
    }
    // const lookBackData: IlLookBackData = {
    //   type: 'movie',
    //   url: item.file || '',
    //   eventOptions: {
    //     type: item.name ? item.name : 'all',
    //     label: `${item.subtitle} ${item.title}`,
    //     urls: item.urls || []
    //   }
    // }
    // history.push({
    //   pathname: '/cameraManagement_app/cameraDetail/lookBackDetail', state: {
    //     lookBackData: lookBackData
    //   }
    // })
  }, [eventData])

  return (
    <>
      <div className={styles.eventLookBack_container}>
        <div className={styles.eventLookBack_title}>事件回看</div>
        <div className={styles.eventLookBack_eventFilter}>
          <span className={styles.eventLookBack_event_span}>事件筛选</span>
          <div className={styles.eventLookBack_eventFilter_container}>
            {
              Object.keys(eventDefinition).map((item: any) => {
                return <div className={`${styles.eventLookBack_eventFilter_item} ${eventFilter.includes(item) ? styles[`selected_${item}`] : ''}`} key={item} onClick={() => {
                  setEventFilter(e => {
                    let es = [...e];
                    if (es.includes('all')) {
                      if (item !== 'all') {
                        const ess = es.filter((it) => it !== 'all');
                        ess.push(item);
                        return ess;
                      }
                      return es;
                    }
                    if (item === 'all') {
                      return ['all'];
                    }
                    if (es.includes(item)) {
                      return es.filter((it) => it !== item);
                    }
                    es.push(item);
                    // if (es.includes('human') && es.includes('pet') && es.includes('noise') && es.includes('move') && es.includes('fire')) {
                    if (es.includes('human') && es.includes('pet') && es.includes('fire')) {
                      return ['all'];
                    }
                    return es;
                  });
                }}>
                  <PreloadImage src={eventDefinition[item].icon} alt="icon" />
                  {eventDefinition[item].label}
                </div>
              })
            }
          </div>
        </div>
        <div className={styles.eventLookBack_timeFilter}>
          <span className={styles.eventLookBack_event_span}>时间筛选</span>
          <div className={styles.eventLookBack_timeFilter_container}>
            <div className={styles.eventLookBack_timeFilter_item}>
              <span className={styles.eventLookBack_timeFilter_item_label}>开始时间</span>
              <div className={styles.eventLookBack_timeFilter_item_date} onClick={() => setSelectStartTime(true)}>
                <span>{timeFormat.startTime}</span>
                <PreloadImage src={isDarkMode ? rightArrow_dark : rightArrow} />
              </div>
            </div>
            <div className={styles.eventLookBack_timeFilter_item}>
              <span className={styles.eventLookBack_timeFilter_item_label}>结束时间</span>
              <div className={styles.eventLookBack_timeFilter_item_date} onClick={() => setSelectEndTime(true)}>
                <span>{timeFormat.endTime}</span>
                <PreloadImage src={isDarkMode ? rightArrow_dark : rightArrow} />
              </div>
            </div>
          </div>
        </div>
        <Divider />
        <div className={styles.eventLookBack_lookBack}>
          <span className={styles.eventLookBack_event_span}>事件回看</span>
          <div className={styles.eventLookBack_lookBack_container}>
            {
              eventList.length === 0 ? <div className={styles.eventLookBack_noData}>暂无数据</div>
                : (
                  <>
                    <EventList data={eventList} callback={(item) => lookBackDetail(item)} title="" needHeader={true} />
                    {hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)}
                  </>
                )
            }

          </div>
        </div>
      </div >
      <DatePicker onCancel={() => setSelectStartTime(false)} isShow={selectStartTime} onSelect={setStartTime} />
      <DatePicker onCancel={() => setSelectEndTime(false)} isShow={selectEndTime} onSelect={setEndTime} />
    </>
  )
}

export default EventLookBack;