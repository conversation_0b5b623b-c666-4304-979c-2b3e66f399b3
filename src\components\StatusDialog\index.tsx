import React from "react";
import { Mo<PERSON>, Button } from "antd";
import styles from "./index.module.scss";
import close from "@/Resources/icon/close.png";

export type StatusType = "loading" | "success" | "error" | "empty";

export interface StatusConfig {
  icon: string | React.ReactNode;
  title: string;
  subTitle?: string;
  iconClassName?: string;
  closeIcon?: string | React.ReactNode;
}

export interface StatusButtonConfig {
  text: string;
  onClick: () => void;
}

interface StatusDialogProps {
  visible: boolean;
  onClose: () => void;
  statusType: StatusType;
  statusConfig: StatusConfig;
  buttonConfig?: StatusButtonConfig;
  modalTitle?: string;
  headerIcon?: React.ReactNode;
  closeIcon?: string | React.ReactNode;
}

const StatusDialog: React.FC<StatusDialogProps> = ({
  visible,
  onClose,
  statusType,
  statusConfig,
  buttonConfig,
  modalTitle = "配置中",
  headerIcon,
  closeIcon,
}) => {
  // 根据状态类型确定容器高度
  const getContainerHeight = () => {
    if (statusType === 'loading') {
      return styles.statusContainerLoading;
    } else if (statusType === 'success' || statusType === 'error' || statusType === 'empty') {
      return styles.statusContainerOther;
    }
    return '';
  };

  const renderTitle = () => {
    // 使用传入的图标，如果没有传入则使用状态配置的图标，如果都没有则使用默认图标
    const actualCloseIcon = closeIcon || statusConfig.closeIcon || close;

    return (
      <div className={styles.modalHeader}>
        {typeof actualCloseIcon === "string" ? (
          <img
            src={actualCloseIcon}
            alt="关闭"
            className={styles.closeButton}
            onClick={onClose}
          />
        ) : (
          <div className={styles.closeButton} onClick={onClose}>
            {actualCloseIcon}
          </div>
        )}
        <div className={styles.modalTitle}>{modalTitle}</div>
        {headerIcon && (
          <div className={styles.headerIconContainer}>{headerIcon}</div>
        )}
      </div>
    );
  };

  const renderContent = () => {
    return (
      <div className={`${styles.statusContainer} ${getContainerHeight()}`}>
        <div
          className={`${styles.iconContainer} ${
            statusConfig.iconClassName || ""
          }`}
        >
          {typeof statusConfig.icon === "string" ? (
            <img src={statusConfig.icon} alt="" className={styles.statusIcon} />
          ) : (
            statusConfig.icon
          )}
        </div>
        <div className={styles.statusTitle}>{statusConfig.title}</div>
        {statusConfig.subTitle && (
          <div className={styles.statusSubTitle}>{statusConfig.subTitle}</div>
        )}
      </div>
    );
  };

  const renderFooter = () => {
    if (!buttonConfig) return null;

    return (
      <Button
        type="primary"
        className={styles.actionButton}
        onClick={buttonConfig.onClick}
      >
        {buttonConfig.text}
      </Button>
    );
  };

  return (
    <Modal
      title={renderTitle()}
      open={visible}
      onCancel={onClose}
      footer={renderFooter()}
      width={546}
      centered
      maskClosable={false}
      className={styles.statusDialog}
      closeIcon={null}
    >
      <div className={styles.modalContent}>{renderContent()}</div>
    </Modal>
  );
};

export default StatusDialog;
