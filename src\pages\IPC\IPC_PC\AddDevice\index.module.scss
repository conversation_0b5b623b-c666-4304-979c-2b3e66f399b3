.addDeviceModal {
  :global {
    .ant-modal-content {
      border-radius: 32px;
      overflow: hidden;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-header {
      padding: 16px 0 0;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-body {
      height: 446px !important;
      padding: 0;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-footer {
      padding: 16px 32px;
      border-top: none;
      text-align: center;
    }
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .outline {
    width: 40px;
    height: 40px;
    cursor: pointer;
  }
.closeButton{
  width: 26px;
  height: 26px;
  cursor: pointer;
}
  .modalTitle {
    font-size: 20px;
    font-weight: 500;
    text-align: center;
    flex: 1;
    color: var(--title-color);
  }

  .outlineButton {
    width: 26px;
    height: 26px;
    cursor: pointer;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px 0;
  height: 100%;
  width: 100%;

  .searchingText {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--primary-color);

    .blueIndicator {
      border-radius: 50%;
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
  }
}

.errorContainer,
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  padding: 0 20px;

  .errorIcon,
  .emptyIcon {
    margin-bottom: 16px;

    .placeholderCircle {
      width: 120px;
      height: 80px;
    }
  }

  .errorTitle,
  .emptyTitle {
    font-size: 16px;
    color: var(--text-color);
  }

  .retryButton,
  .supportButton {
    width: 180px;
    height: 40px;
    border-radius: 8px;
  }
}

.deviceListContainer {
  width: 100%;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  .searchResult {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--primary-color);
    padding: 16px 0;
    span {
      margin-left: 10px;
    }
  }

  .deviceList {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
    margin-left: 0;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

.deviceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  .deviceInfo {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    margin-right: 16px;

    .deviceThumb {
      width: 24px !important;
      height: 24px !important;
      margin-right: 16px;
      object-fit: contain;
      flex-shrink: 0;
      min-width: 24px; // 确保最小宽度
      min-height: 24px; // 确保最小高度
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      overflow: hidden;
    }

    .deviceName {
      font-size: 15px;
      color: var(--text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .checkboxContainer {
    flex-shrink: 0;
    width: 45px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
  }

  .customCheckbox {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background-color: var(--thinLine-background-color);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &.checked {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .checkIcon {
      width: 5px;
      height: 10px;
      border-right: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transform: rotate(45deg) translate(-1px, -1px);
    }
  }
}

.nextButton {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
  background-color: var(--primary-color);
  border: none;
  font-weight: normal;

  &:hover,
  &:focus {
    background-color: var(--primary-color);
  }

  &:disabled {
    background-color: #91bbff;
    color: #fff;
    cursor: not-allowed;
  }
}
