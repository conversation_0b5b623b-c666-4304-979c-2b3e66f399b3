import { useState, useCallback, useRef } from 'react';
import { getUserListInfo } from '@/utils/DeviceType';
import { Toast } from '@/components/Toast/manager';

// 用户信息接口
export interface FatUser {
  id: string;
  name: string;
  position: string;
  avatar?: string;
  uid?: string;
  permission?: 'admin' | 'user';
  group?: string[];
  status?: 'active' | 'inactive';
}

// 自定义Hook返回类型
export interface UseFatUserReturn {
  users: FatUser[];
  isLoading: boolean;
  loadUsers: () => Promise<void>;
  refreshUsers: () => Promise<void>;
  error: string | null;
}

// 自定义Hook
export const useFatUser = (): UseFatUserReturn => {
  const [users, setUsers] = useState<FatUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const loadingRef = useRef(false); // 防止重复加载的标志位

  // 从原始用户信息转换为本地格式
  const convertUserListData = useCallback((userInfoList: any[]): FatUser[] => {
    return userInfoList.map((userInfo: any) => ({
      id: userInfo.uid,
      name: userInfo.nickname || userInfo.uid, // 如果没有nickname，使用uid
      position: userInfo.permission === 'admin' ? '管理员' : '普通用户',
      avatar: userInfo.imgSrc, // 使用用户头像，如果没有则使用默认头像
      uid: userInfo.uid,
      permission: userInfo.permission,
      group: userInfo.group || [],
      status: userInfo.status
    }));
  }, []);

  // 加载用户列表
  const loadUsers = useCallback(async () => {
    // 如果正在加载中，直接返回
    if (loadingRef.current) {
      return;
    }

    try {
      loadingRef.current = true;
      setIsLoading(true);
      setError(null);

      const userInfoList = getUserListInfo();
      
      if (userInfoList && userInfoList.length > 0) {
        // 转换数据格式
        const convertedUsers = convertUserListData(userInfoList);
        setUsers(convertedUsers);
        console.log('从账号信息加载用户列表成功:', convertedUsers);
      } else {
        // 如果没有获取到用户信息，设置为空数组
        console.warn('未获取到用户信息');
        setUsers([]);
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
      setError('加载用户列表失败');
      setUsers([]);
      Toast.show('加载用户列表失败', { duration: 2000 });
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [convertUserListData]);

  // 刷新用户列表（强制重新加载）
  const refreshUsers = useCallback(async () => {
    loadingRef.current = false; // 重置加载标志
    await loadUsers();
  }, [loadUsers]);

  return {
    users,
    isLoading,
    loadUsers,
    refreshUsers,
    error
  };
};

export default useFatUser;