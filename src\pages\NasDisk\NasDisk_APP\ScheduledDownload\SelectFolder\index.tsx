import React, { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON>, Toast, Checkbox } from "antd-mobile";
import { HeartFill } from "antd-mobile-icons";
import styles from "./index.module.scss";
import NavigatorBar from "@/components/NavBar";
import { useHistory, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import { getBaiduNetdiskFileList, BaiduFileItem, downloadFromBaiduNetdisk, BaiduDownloadPathItem, getPoolInfo } from "@/api/nasDisk";
import { modalShow } from "@/components/List";
import EmptyState from "../../components/EmptyState";
import { PreloadImage } from "@/components/Image";
import fileIcon from "@/Resources/icon/file-icon.png";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import { useTheme } from "@/utils/themeDetector";
import { useUser } from "@/utils/UserContext";
import Downlocation from "../../Downlocation";
// 最大任务数量限制
const MAX_TASK_COUNT = 5;

interface FileItem {
  id: string;
  name: string;
  type: "folder" | "file";
  time: string;
  isLiked?: boolean;
  path: string;
  disabled?: boolean; // 添加disabled属性表示该文件夹是否已在下载
}

const SelectFolder: React.FC = () => {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const location = useLocation<{
    isVip?: boolean;
    downloadPath?: string;
    downloadDisplayPath?: string;
    selectedFolders?: string[];
    existingFolderPaths?: string[]; // 添加已有任务的文件夹路径
    existingTaskCount?: number; // 添加已有任务数量
  }>();
    const { userInfo } = useUser();
  const { nas_vip } = userInfo || {};
  const { existingFolderPaths = [], existingTaskCount = 0 } = location.state || {};
  
  // 剩余可选择的文件夹数量
  const remainingSelections = MAX_TASK_COUNT - existingTaskCount;

  // 顶层文件夹列表
  const [topLevelFolders, setTopLevelFolders] = useState<FileItem[]>([]);

  // 选中的文件夹ID列表
  const [selectedFolders, setSelectedFolders] = useState<string[]>(
    location.state?.selectedFolders || []
  );

  // 下载位置显示文本
  const [downloadLocation, setDownloadLocation] = useState<string>(
    location.state?.downloadDisplayPath || "内部存储01>百度网盘"
  );

  // 下载位置路径
  const [downloadPath, setDownloadPath] = useState<string>(
    location.state?.downloadPath || ""
  );

  // Downlocation组件显示状态
  const [showDownlocation, setShowDownlocation] = useState<boolean>(false);

  // 处理Downlocation选择
  const handleDownlocationSelect = useCallback((downloadPath: string, downloadDisplayPath: string) => {
    setDownloadPath(downloadPath);
    setDownloadLocation(downloadDisplayPath);
    setShowDownlocation(false);
    Toast.show({
      content: `已设置自动下载位置：${downloadDisplayPath}`,
      position: 'bottom',
      duration: 2000,
    });
  }, []);

  // 处理Downlocation关闭
  const handleDownlocationClose = useCallback(() => {
    setShowDownlocation(false);
  }, []);
  
  // 存储池和WebDAV配置信息
  const [defaultDownloadPath, setDefaultDownloadPath] = useState<string>('');
  const [, setWebDAVConfig] = useState<{alias_root?: string} | null>(null);
  
  // 获取存储池信息
  const { run: fetchPoolInfo } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response) => {
      if (response.code === 0 && response.data) {
        // 保存WebDAV配置
        if (response.data.webDAV) {
          setWebDAVConfig(response.data.webDAV);
        }

        // 获取第一个存储池的顶层目录作为默认下载路径
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;
            
            // 设置默认的下载路径
            const defaultPath = `${aliasRoot}${dataDir}/百度网盘`;
            setDefaultDownloadPath(defaultPath);
            
            // 如果没有传入downloadPath，则使用默认路径
            if (!location.state?.downloadPath) {
              setDownloadPath(defaultPath);
            }
          }
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
    }
  });
  
  // 组件挂载时获取存储池信息
  useEffect(() => {
    fetchPoolInfo();
  }, [fetchPoolInfo]);

  // 下载请求
  const { run: runDownload, loading: downloadLoading } = useRequest(
    (params: {
      remotePaths: BaiduDownloadPathItem[];
      localPath: string;
      autotask: number;
    }) => {
      // 对路径进行编码
    //   const encodedRemotePaths = params.remotePaths.map(item => ({
    //   ...item,
    //   path: encodeURIComponent(item.path)
    // }));
      return downloadFromBaiduNetdisk({
        action: "download",
        autotask: params.autotask,
        remotepath: params.remotePaths,
        localpath: params.localPath
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        if (result.code === 0) {
          Toast.show({
            icon: 'success',
            content: '自动下载任务添加成功',
          });
          
          // 导航到首页的自动下载标签页
          history.push({
            pathname: "/baiduNetdisk_app",
            state: {
              activeTab: "autoDownload", // 设置活动标签为自动下载
            },
          });
        } else {
          Toast.show({
            icon: 'fail',
            content: `下载任务添加失败`,
          });
          
          if (result.failed_paths && result.failed_paths.length > 0) {
            console.error('下载失败的路径:', result.failed_paths);
          }
        }
      },
      onError: (error) => {
        Toast.show({
          icon: 'fail',
          content: '下载请求出错，请重试',
        });
        console.error('下载请求出错:', error);
      }
    }
  );

  // 标准化路径，确保以/开头且不以/结尾
  const normalizePath = (path: string): string => {
    let normalizedPath = path;
    // 确保路径以/开头
    if (!normalizedPath.startsWith('/')) {
      normalizedPath = '/' + normalizedPath;
    }
    // 确保路径不以/结尾（除非就是根路径/）
    if (normalizedPath !== '/' && normalizedPath.endsWith('/')) {
      normalizedPath = normalizedPath.slice(0, -1);
    }
    return normalizedPath;
  };

  // 获取百度网盘文件列表
  const { run: fetchFileList, loading: directoryLoading } = useRequest(
    () => {
      // 调用真实接口
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: "/", // 固定只获取根目录
        order: "name",
        desc: 1,
        web: 1,
        folder: 1, // 只返回文件夹
      }, { loadingMode: 'icon' })
      .catch((error) => {
        console.error("获取文件列表失败，使用模拟数据", error);
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 将百度网盘文件列表转换为应用内文件列表格式
          const folders: FileItem[] = response.list
            .filter((item: BaiduFileItem) => item.isdir === 1) // 只保留文件夹
            .map((item: BaiduFileItem) => {
              const folderPath = normalizePath(item.path);
              
              // 检查此文件夹路径是否已存在于下载任务中
              const isDisabled = existingFolderPaths.some(taskPath => {
                const normalizedTaskPath = normalizePath(taskPath);
                return normalizedTaskPath === folderPath;
              });
              
              return {
                id: item.fs_id.toString(),
                name: item.server_filename,
                type: "folder",
                time: new Date(item.server_mtime * 1000).toLocaleString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                }),
                path: folderPath,
                isLiked: false,
                disabled: isDisabled, // 根据是否已存在于下载任务中来设置禁用状态
              };
            });

          setTopLevelFolders(folders);
        } else {
          console.error("获取文件列表失败:", response);
          Toast.show({
            content: "获取文件列表失败，请重试",
            position: "bottom",
            duration: 2000,
          });
          setTopLevelFolders([]);
        }
      },
      onError: (error) => {
        console.error("获取文件列表失败：", error);
        Toast.show({
          content: "获取文件列表失败，请重试",
          position: "bottom",
          duration: 2000,
        });
        setTopLevelFolders([]);
      },
    }
  );

  // 初始化时获取文件列表
  useEffect(() => {
    fetchFileList();
  }, [fetchFileList]);

  // 处理文件夹选中状态
  const handleFolderSelect = useCallback((folderId: string) => {
    // 找到对应的文件夹
    const folder = topLevelFolders.find(item => item.id === folderId);
    
    // 如果文件夹已禁用（已在下载中），则不允许选择
    if (folder?.disabled) {
      Toast.show({
        content: "该文件夹已在下载任务中",
        position: "bottom",
        duration: 2000,
      });
      return;
    }
    
    setSelectedFolders((prev) => {
      const isSelected = prev.includes(folderId);

      if (isSelected) {
        // 如果已选中，则移除
        return prev.filter((id) => id !== folderId);
      } else {
        // 如果未选中，检查是否超过限制
        if (prev.length >= remainingSelections) {
          Toast.show({
            content: `最多只能选择${remainingSelections}个文件夹`,
            position: "bottom",
            duration: 2000,
          });
          return prev;
        }
        // 添加到选中列表
        return [...prev, folderId];
      }
    });
  }, [topLevelFolders, remainingSelections]);

  // 处理返回
  const handleBack = () => {
    // 返回到自动下载tab
    history.push({
      pathname: "/baiduNetdisk_app",
      state: {
        activeTab: "autoDownload",
      },
    });
  };

  // 处理确定按钮
  const handleConfirm = () => {
    // 自动下载确定按钮埋点
    window.onetrack?.('track', 'nasDisk_autoDownload_confirm_click', {
      downloadPath: downloadLocation
    });

    if (selectedFolders.length === 0) {
      Toast.show({
        content: "请至少选择一个文件夹",
        position: "bottom",
        duration: 2000,
      });
      return;
    }

    // 获取选中的文件夹信息
    const selectedFolderItems = topLevelFolders.filter(folder =>
      selectedFolders.includes(folder.id)
    );

    // 构建下载参数
    const remotePaths: BaiduDownloadPathItem[] = selectedFolderItems.map(folder => ({
      type: 'directory', // 自动下载只支持文件夹
      path: folder.path || `/${folder.name}` // 如果没有path就构建一个
    }));

    // 调用下载接口
    runDownload({
      remotePaths,
      localPath: downloadPath || defaultDownloadPath,
      autotask: 1 // 自动下载任务
    });

    // 注意：不在这里跳转，而是在请求成功回调中处理
  };

  // 导航到下载位置选择页面
  const navigateToDownlocation = () => {
    // 自动下载位置选择埋点
    window.onetrack?.('track', 'nasDisk_autoDownload_location_click', {
      downloadPath: downloadLocation
    });

    if (nas_vip !== 1) {
      showVipModal();
      return;
    }

    // 显示Downlocation组件
    setShowDownlocation(true);
  };

  const showVipModal = () => {
    if (nas_vip === 1) {
      return;
    }

    // 百度网盘VIP弹窗显示埋点
    window.onetrack?.('track', 'nasDisk_baiduNetdisk_vip_modal_show');

    modalShow(
      "会员权益",
      "该功能为网盘NAS会员权益，是否要开启？",
      (m) => {
        // 确认按钮点击 - 开通百度会员埋点
        window.onetrack?.('track', 'nasDisk_baiduNetdisk_vip_open_click');
        m.destroy();
        history.push(`/baiduNetdisk_app/members`);
      },
      () => {
        // 取消按钮点击
      },
      false,
      {
        position: "bottom",
        okBtnText: "开通会员",
        cancelBtnText: "取消",
        okBtnStyle: { backgroundColor: "#402C00", color: "#E2AE1E" },
      }
    );
  };

  // 检查location中是否有下载路径信息
  useEffect(() => {
    if (location.state) {
      if (location.state.downloadDisplayPath) {
        setDownloadLocation(location.state.downloadDisplayPath);
      }

      if (location.state.downloadPath) {
        setDownloadPath(location.state.downloadPath);
      }

      if (location.state.selectedFolders) {
        setSelectedFolders(location.state.selectedFolders);
      }
    }
  }, [location.state]);

  return (
    <div className={styles.synchronizationContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar onBack={handleBack} backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
        <div className={styles.title}>选择文件夹（最多{remainingSelections}项）</div>
      </div>

      <div className={styles.scrollableContent}>
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {topLevelFolders.map((folder) => {
            const isSelected = selectedFolders.includes(folder.id);
            const isDisabled = folder.disabled;
            return (
              <div
                key={folder.id}
                className={`${styles.fileItem} ${
                  isSelected ? styles.selected : ""
                }`}
                onClick={() => !isDisabled && handleFolderSelect(folder.id)}
              >
                <div className={styles.fileIcon}>
                  <PreloadImage
                    src={fileIcon}
                    alt=""
                    style={{ width: 40, height: 40 }}
                  />
                </div>
                <div className={styles.fileInfo}>
                  <div className={styles.fileName}>
                    {folder.name}
                    {folder.isLiked && (
                      <HeartFill className={styles.heartIcon} />
                    )}
                  </div>
                  <div className={styles.fileDetails}>
                    {folder.time}
                  </div>
                </div>
                <div
                  className={styles.checkboxContainer}
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    if (!isDisabled) {
                      handleFolderSelect(folder.id);
                    }
                  }}
                >
                  <Checkbox checked={isSelected || isDisabled} disabled={isDisabled} />
                </div>
              </div>
            );
          })}

          {!directoryLoading && topLevelFolders.length === 0 && <EmptyState />}
        </div>
      </div>

      {/* 底部按钮 */}
      <div className={styles.footerButtons}>
        {nas_vip !== 1 && <div className={styles.vipTip}>VIP专享</div>}
        <Button
          block
          color="primary"
          className={styles.locationButton}
          onClick={navigateToDownlocation}
        >
          <span className={styles.pathText}>
            自动下载到:<span>{downloadLocation}</span>
          </span>
        </Button>
        <Button
          block
          color="primary"
          className={styles.downloadButton}
          onClick={handleConfirm}
          disabled={selectedFolders.length === 0 || downloadLoading}
          loading={downloadLoading}
        >
          {downloadLoading ? '添加中...' : `确定 (${selectedFolders.length})`}
        </Button>
      </div>

      {/* Downlocation组件 */}
      <Downlocation
        visible={showDownlocation}
        onClose={handleDownlocationClose}
        onSelect={handleDownlocationSelect}
        title="更改自动下载位置"
      />
    </div>
  );
};

export default SelectFolder;
