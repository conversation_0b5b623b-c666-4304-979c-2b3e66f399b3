import React, { useState, useEffect } from "react";
import { Modal, List, message  } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";

import { getSupportCameraList, SupportedCameraModel } from "@/api/ipc";
import { PreloadImage } from "@/components/Image";

interface SupportInformationProps {
  visible?: boolean;
  onClose?: () => void;
}

const formatApiData = (apiData: SupportedCameraModel[]) => {
  // 获取完整的URL前缀
  const origin = window.location.origin;
  const pathname = window.location.pathname;
  const needPath = pathname.split('/').slice(2, 5).join('/');

  return [
    {
      brand: "小米",
      models: apiData.map((camera, index) => ({
        id: index + 1,
        // 拼接完整的图片URL
        icon: `${origin}/${needPath}/${camera.icon}`,
        name: camera.model_name,
        model: camera.model,
      })),
    },
  ];
};

export default function SupportInformation({
  visible = true,
  onClose,
}: SupportInformationProps) {
  const [displayCameraList, setDisplayCameraList] = useState<any[]>([]);

  // 页面曝光埋点
  useEffect(() => {
    if (visible) {
      window.onetrack?.('track', 'ipc_supportCamera_expose');
    }
  }, [visible]);

  useRequest(getSupportCameraList, {
    onSuccess: (res) => {
      if (res && res.code === 0) {
        const formattedData = formatApiData(res.data.camera);
        setDisplayCameraList(formattedData);
      }else if(res && res.code !== 0){
        message.info(res?.result);
      } 
       else {
        console.log("接口返回数据为空或失败");
      }
    },
    onError: (error) => {
      console.error("获取支持的摄像机列表失败:", error);
      // 错误时设置为空数组，不再使用默认数据
      setDisplayCameraList([]);
    },
  });

  const handleCancel = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleCameraSelect = (camera: {
    id: number;
    icon: string;
    name: string;
    model?: string;
  }) => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <Modal
      title="支持的摄像机"
      open={visible}
      footer={null}
      onCancel={handleCancel}
      width={546}
      className={styles.cameraModal}
      closeIcon={<CloseOutlined style={{ color: "var(--title-color)" }} />}
      centered
      styles={{
        body: {
          height: "calc(636px - 55px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalWrapper}>
        {displayCameraList.length > 0 ? (
          displayCameraList.map((brand: any) => (
            <div key={brand.brand} className={styles.brandSection}>
              <div className={styles.brandTitle}>{brand.brand}</div>
              <List
                className={styles.cameraList}
                dataSource={brand.models}
                renderItem={(camera: any) => (
                  <List.Item
                    key={camera.id}
                    onClick={() => handleCameraSelect(camera)}
                    className={styles.cameraItem}
                  >
                    <List.Item.Meta
                      avatar={
                        <div className={styles.cameraIcon}>
                          <PreloadImage
                            src={camera.icon || ''}
                            alt={camera.name}
                            style={{
                              width: 24,
                              height: 24,
                              objectFit: 'contain'
                            }}
                          />
                        </div>
                      }
                      title={
                        <div className={styles.cameraName}>{camera.name}</div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          ))
        ) : (
          <div className={styles.emptyState}>
            <div className={styles.emptyText}>暂无内容</div>
          </div>
        )}
      </div>
    </Modal>
  );
}
