import Player from "xgplayer/es/player";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import pause from "@/Resources/icon/pause.png";
import record from "@/Resources/icon/record.png";
import fullscreen from "@/Resources/icon/fullscreen.png";
import screenshot from "@/Resources/icon/screenshotIcon.png";
import volume from "@/Resources/icon/volume.png";
import volume_dashboard from "@/Resources/icon/volume_dashboard.png";
import notSound_dashboard from "@/Resources/icon/notSound_dashboard.png";
import play_dashboard from "@/Resources/icon/play_dashboard.png";
import pause_dashboard from "@/Resources/icon/pause_dashboard.png";
import fullscreen_dashboard from "@/Resources/icon/fullscreen_dashboard.png";
import playbackRate from "@/Resources/icon/speed2x.png";
import play from "@/Resources/icon/play.png";
import moreOpt from "@/Resources/icon/moreOpt.png";
import close from "@/Resources/icon/close_white.png";
import openScreen from "@/Resources/icon/openScreen.png";
import hideScreen from "@/Resources/icon/hiddenScreen.png";
import './PlayerControl.css';
import { PreloadImage } from "@/components/Image";
import { useHistory, useRouteMatch } from "react-router-dom";
import { EventLookBackType } from "@/components/CameraPlayer/constants";
import { IDualOptions, IIconBtn, IMonitorPlayerOptions, PlayerType } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import ProgressPlugin from "@/components/CameraPlayer/components/plugin/ProgressPlugin/ProgressPlugin";
import PlaybackRatePlugin from "@/components/CameraPlayer/components/plugin/PlaybackRatePlugin/PlaybackRatePlugin";
import VolumePlugin from "@/components/CameraPlayer/components/plugin/VolumePlugin/VolumePlugin";
import MoreOptPlugin from "@/components/CameraPlayer/components/plugin/MoreOptPlugin/MoreOptPlugin";
import RecordPlugin from "@/components/CameraPlayer/components/plugin/RecordPlugin/RecordPlugin";
import TimeAxis, { IEventBlock } from "@/components/CameraPlayer/components/TimeAxis/TimeAxis";
import { px2rem } from "@/utils/setRootFontSize";
import { useFullscreen } from 'ahooks';
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { getDeviceType, getSystemType } from "@/utils/DeviceType";
import { StopRecordData } from "@/api/ipc";
import { Toast } from "@/components/Toast/manager";
import { downloadAndSaveWithPlayer, downloadToAlbumWithPlayerByApp } from "@/api/cameraPlayer";
import { getThumbnail } from "@/api/fatWall";

interface IPlayerControl {
  isFull: boolean // 是否全屏播放
  setIsFull: (v: boolean) => void; // 设置是否全屏播放
  autoHide: boolean
  cameraRef: React.MutableRefObject<Player | null>
  type: PlayerType
  isPlay: boolean
  setIsPlay: (v: boolean) => void
  isDashboard?: boolean
  options?: IMonitorPlayerOptions
  dualOptions?: IDualOptions
  isPause: boolean
  timeAxisEvents: {
    [key: string]: IEventBlock[]
  }
  setSelectedDate?: (v: Date) => void;
  selectedDate?: Date;
  width: string
  cameraDetail?: ICameraDetail;
  okCallback?: () => void; // 点击播放时重新获取直播拉流数据
  isHideScreen: boolean; // 是否隐藏副屏幕
  setIsHideScreen: React.Dispatch<React.SetStateAction<boolean>> // 设置是否隐藏副屏幕
}

// 播放速度列表
const rateList = [
  { label: "1x", rate: 1 },
  { label: "2x", rate: 2 },
  { label: "4x", rate: 4 },
]

export interface IlLookBackData {
  type: 'movie' | 'photo', // 控制视频还是图片
  url: string // 视频或图片的url
  photoName?: string
  eventOptions?: { // 控制事件回放的选项，不需要就不传
    type: EventLookBackType,
    label: string
    urls: string[]
  }
}

export interface customizePluginsProps {
  playPlugin: IIconBtn,
  pausePlugin: IIconBtn,
  volumePlugin: IIconBtn,
  screenshotPlugin: IIconBtn,
  recordPlugin: IIconBtn,
  playbackRatePlugin: IIconBtn,
  fullscreenPlugin: IIconBtn,
  progressPlugin: IIconBtn,
  moreOptPlugin: IIconBtn,
  dashboardFullscreenPlugin: IIconBtn,
  dashboardPlayPlugin: IIconBtn,
  dashboardPausePlugin: IIconBtn,
  dashboardVolumePlugin: IIconBtn,
  openScreenPlugin: IIconBtn,
  hideScreenPlugin: IIconBtn,
}

const PlayerControl = (props: IPlayerControl) => {
  const deviceType = getDeviceType() === 0 ? 'mobile' : 'pc'
  const isAndroid = getSystemType() === 'android';
  const isIos = getSystemType() === 'ios';
  const { isFull, cameraRef, setIsPlay, isPlay, type, autoHide, isDashboard, options, dualOptions, isPause,
    timeAxisEvents, setSelectedDate, selectedDate, width, cameraDetail, okCallback, setIsHideScreen, isHideScreen } = props;
  const temp_screenshot = useRef<string>('');
  const temp_screenshot_name = useRef<string>('');
  const temp_lookBackType = useRef<'movie' | 'photo'>('photo');
  const [finishScreenshot, setFinishScreenshot] = useState<boolean>(false);
  const [isRecord, setIsRecord] = useState<boolean>(false);
  const [temp_recordTime, setTemp_recordTime] = useState<string>('00:00:00'); // 录制时间
  const [recordUrl, setRecordUrl] = useState<string>('');
  const { path } = useRouteMatch();
  const recordTimeout = useRef<NodeJS.Timeout | null>(null);

  // 控制pc弹窗显示截图
  const pc_screenshot_ref = useRef(null);
  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(pc_screenshot_ref);
  const timeout = useRef<NodeJS.Timeout | null>(null);

  // 播放
  const onPlay = useCallback(async () => {
    okCallback && okCallback();
    if (cameraRef.current) {
      try {
        const res = await cameraRef.current.play();
        if (res) {
          setIsPlay(true);
        }
      }
      catch (e) {
        setIsPlay(false)
      }
    }
  }, [cameraRef, okCallback, setIsPlay])

  // 暂停
  const onPause = useCallback(() => {
    cameraRef.current?.pause();
    setIsPlay(false);
  }, [cameraRef, setIsPlay])

  //截图
  const onScreenshot = useCallback((w?: number, h?: number) => {
    if (cameraRef.current) {
      // 清空可能存在的录制操作
      setFinishScreenshot(false);
      temp_screenshot.current = '';

      // 设置截图信息
      const screenshot = cameraRef.current.getPlugin('screenshot'); //获取播放器截图插件
      screenshot.shot(w, h, { quality: 0.92, type: 'image/png' }).then(async (r: any) => {
        const res = r.replace(/^data:application\/octet-stream/, 'data:image/png');
        temp_screenshot.current = res;
        temp_lookBackType.current = 'photo';
        setFinishScreenshot(true);

        // 5s后清除信息
        timeout.current = setTimeout(() => {
          setFinishScreenshot(false);
          temp_screenshot.current = '';
          temp_screenshot_name.current = '';
        }, 5000)

        if (deviceType === 'mobile') {
          // 截图保存本地
          const name = `Screenshot_${new Date().getTime()}_ipccamera.png`;
          await downloadToAlbumWithPlayerByApp('1', (rs) => {
            if (rs.code === 0) {
              Toast.show('截图保存本地成功！');
              temp_screenshot_name.current = name;
            }
          }, res, undefined, name).catch((e) => console.log('截图保存在本地相册失败', e));
        } else {
          // 保存截图到本地
          const link = document.createElement('a');
          link.href = res;
          link.download = '录制截图.png';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      });
    }
  }, [cameraRef, deviceType])

  //录制回调
  const onRecord = useCallback(async (item: StopRecordData) => {
    if (!item) {
      Toast.show('录制失败，请稍后再试！');
      return;
    }

    // 清空可能存在的截图操作
    setFinishScreenshot(false);
    temp_screenshot.current = '';
    // 设置录制信息
    setRecordUrl(item.file);

    // 设置回看属性
    temp_lookBackType.current = 'movie';

    let cover = '';
    const posterRes = await getThumbnail({ path: item.file, size: 'medium' });
    if (posterRes instanceof ArrayBuffer) {
      // 检查JPEG文件头
      const uint8Array = new Uint8Array(posterRes);
      const isValidJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF;

      if (isValidJPEG) {
        const blob = new Blob([posterRes], { type: 'image/jpeg' });
        cover = URL.createObjectURL(blob);
      } else {
        // 尝试作为其他格式
        const blob = new Blob([posterRes], { type: 'image/png' });
        cover = URL.createObjectURL(blob);
      }
    }

    temp_screenshot.current = cover; // 设置悬浮的静态帧

    setFinishScreenshot(true); // 打开临时录制静态帧

    timeout.current = setTimeout(() => {
      setFinishScreenshot(false);
      temp_screenshot.current = '';

      // 清除录制定时器
      if (recordTimeout.current) {
        clearInterval(recordTimeout.current);
      }
      setTemp_recordTime('00:00:00'); // 重置录制时间
    }, 5000)

    await downloadAndSaveWithPlayer([item.file], (rs) => {
      if (rs.code !== 0) {
        Toast.show('保存失败，请稍后再试！');
      }
    }).catch((e) => console.log('保存录制视频失败', e));
  }, [])

  //全屏
  const onFullScreen = useCallback(async () => {
    if (cameraRef.current) {
      console.log(cameraRef.current, '在操作全屏');
      try {
        const fullscreen = cameraRef.current.getPlugin("fullscreen");
        fullscreen.handleFullscreen(); // 配置内根据环境切换全屏方式
      } catch (e) {
        console.log(`全屏失败:${e}`)
      }
    }
  }, [cameraRef])

  const iconEvent: { [key: string]: () => void } = useMemo(() => {
    return {
      play: onPlay,
      pause: onPause,
      screenshot: onScreenshot,
      fullscreen: onFullScreen,
      hideScreen: () => setIsHideScreen(true),
      openScreen: () => setIsHideScreen(false) // 控制是否隐藏副摄
    }
  }, [onFullScreen, onPause, onPlay, onScreenshot, setIsHideScreen])

  // 自定义插件配置
  const customizePlugins: customizePluginsProps = useMemo(() => {
    return {
      playPlugin: { type: "play", label: "play", icon: play, onclick: iconEvent["play"] },
      pausePlugin: { type: "pause", label: "pause", icon: pause, onclick: iconEvent["pause"] },
      volumePlugin: {
        type: "volume", label: "volume", icon: volume, onclick: iconEvent["volume"],
        components: <VolumePlugin isFull={isFull} isDashboard={isDashboard} options={options} initVolume={cameraRef.current?.volume}
          player={cameraRef.current} deviceType={deviceType} />
      },
      screenshotPlugin: { type: "screenshot", label: "screenshot", icon: screenshot, onclick: iconEvent["screenshot"] },
      recordPlugin: {
        type: "record", label: "record", icon: record, onclick: iconEvent["record"],
        components: <RecordPlugin dualOptions={dualOptions} isFull={isFull} isDashboard={isDashboard} options={options} cameraDetail={cameraDetail}
          isRecord={isRecord} setIsRecord={setIsRecord} callback={onRecord} cameraRef={cameraRef} deviceType={deviceType} />
      },
      playbackRatePlugin: {
        type: "playbackRate", label: "playbackRate", icon: playbackRate, onclick: iconEvent["playbackRate"],
        components: <PlaybackRatePlugin isFull={isFull} isDashboard={isDashboard} options={options} player={cameraRef.current} rateList={rateList} deviceType={deviceType} />
      },
      fullscreenPlugin: { type: "fullscreen", label: "fullscreen", icon: fullscreen, onclick: iconEvent["fullscreen"] },
      progressPlugin: {
        type: "progress", label: "progress", icon: "", onclick: () => null,
        components: <ProgressPlugin type={"notFull"} deviceType={deviceType} player={cameraRef.current} />
      },
      moreOptPlugin: {
        type: "moreOpt", label: "moreOpt", icon: moreOpt, onclick: iconEvent["moreOpt"],
        components: <MoreOptPlugin isFull={isFull} isDashboard={isDashboard} options={options} player={cameraRef} deviceType={deviceType} />
      },
      dashboardVolumePlugin: {
        type: "volume", label: "volume", icon: volume_dashboard, onclick: iconEvent["volume"],
        components: <VolumePlugin isFull={isFull} isDashboard={isDashboard} options={options} volumeIcon={volume_dashboard}
          mutedIcon={notSound_dashboard} initVolume={cameraRef.current?.volume} player={cameraRef.current} deviceType={deviceType} />
      },
      dashboardPausePlugin: { type: "pause", label: "pause", icon: pause_dashboard, onclick: iconEvent["pause"] },
      dashboardPlayPlugin: { type: "play", label: "play", icon: play_dashboard, onclick: iconEvent["play"] },
      dashboardFullscreenPlugin: { type: "fullscreen", label: "fullscreen", icon: fullscreen_dashboard, onclick: iconEvent["fullscreen"] },
      hideScreenPlugin: { type: "hideScreen", label: "hideScreen", icon: hideScreen, onclick: iconEvent["hideScreen"] },
      openScreenPlugin: { type: "openScreen", label: "openScreen", icon: openScreen, onclick: iconEvent["openScreen"] }
    }
  }, [cameraDetail, cameraRef.current, deviceType, dualOptions, iconEvent, isDashboard, isFull, isRecord, onRecord, options])

  // 直播控件按钮
  const LivePlugins: IIconBtn[] = useMemo(() => {
    const btns: IIconBtn[] = [
      isPlay ? customizePlugins.pausePlugin : customizePlugins.playPlugin,
      customizePlugins.volumePlugin,
      // customizePlugins.playbackRatePlugin,
      customizePlugins.screenshotPlugin,
      customizePlugins.recordPlugin,
      isHideScreen ? customizePlugins.openScreenPlugin : customizePlugins.hideScreenPlugin
    ]
    if (!isFull) {
      btns.push(customizePlugins.fullscreenPlugin);
    }

    return btns;
  }, [isPlay, customizePlugins.pausePlugin, customizePlugins.playPlugin, customizePlugins.volumePlugin, customizePlugins.screenshotPlugin, customizePlugins.recordPlugin, customizePlugins.openScreenPlugin, customizePlugins.hideScreenPlugin, customizePlugins.fullscreenPlugin, isHideScreen, isFull])

  // 点播控件按钮
  const MoviePlugins: IIconBtn[] = useMemo(() => {
    const btns: IIconBtn[] = [
      isPlay ? customizePlugins.pausePlugin : customizePlugins.playPlugin,
    ]
    if (!isFull) {
      btns.push(customizePlugins.progressPlugin);
    }
    btns.push(
      customizePlugins.playbackRatePlugin,
      customizePlugins.volumePlugin,
    )
    if (isFull) {
      btns.push(
        customizePlugins.screenshotPlugin,
        customizePlugins.moreOptPlugin,
      )
    }
    if (!isFull) {
      btns.push(customizePlugins.fullscreenPlugin);
    }
    return btns;
  }, [
    customizePlugins.fullscreenPlugin, customizePlugins.moreOptPlugin, customizePlugins.pausePlugin,
    customizePlugins.playPlugin, customizePlugins.playbackRatePlugin, customizePlugins.progressPlugin,
    customizePlugins.screenshotPlugin, customizePlugins.volumePlugin, isFull, isPlay
  ])

  // pc端大屏展示控件按钮
  const DashboardPlugins: IIconBtn[] = useMemo(() => {
    const btns = [
      customizePlugins.dashboardVolumePlugin,
      isPlay ? customizePlugins.dashboardPausePlugin : customizePlugins.dashboardPlayPlugin,
      customizePlugins.dashboardFullscreenPlugin
    ]
    return btns;
  }, [customizePlugins.dashboardFullscreenPlugin, customizePlugins.dashboardPausePlugin, customizePlugins.dashboardPlayPlugin, customizePlugins.dashboardVolumePlugin, isPlay])

  const ModalPlugins: IIconBtn[] = useMemo(() => {
    const btns: IIconBtn[] = [
      isPlay ? customizePlugins.pausePlugin : customizePlugins.playPlugin,
      customizePlugins.playbackRatePlugin,
      customizePlugins.volumePlugin,
      customizePlugins.screenshotPlugin,
      customizePlugins.recordPlugin,
      customizePlugins.moreOptPlugin,
      customizePlugins.fullscreenPlugin
    ]
    return btns;
  }, [isPlay, customizePlugins.pausePlugin, customizePlugins.playPlugin,
    customizePlugins.playbackRatePlugin, customizePlugins.volumePlugin, customizePlugins.screenshotPlugin,
    customizePlugins.recordPlugin, customizePlugins.moreOptPlugin, customizePlugins.fullscreenPlugin])

  const ControlButton: React.ReactNode = useMemo(() => {
    let btns: IIconBtn[] = type === 'Live' ? LivePlugins : MoviePlugins;
    if (isDashboard && !isFull) {
      btns = DashboardPlugins;
    }
    if (options && options.type === 'modal' && !isFull) {
      btns = ModalPlugins;
    }
    return btns.map((item: IIconBtn) => {
      return <div key={item.type} className={`iconBtn_container ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} onClick={item.onclick}>
        {
          item.components ? item.components : <PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} src={item.icon} alt={item.label} />
        }
      </div>
    })
  }, [DashboardPlugins, LivePlugins, ModalPlugins, MoviePlugins, deviceType, isAndroid, isDashboard, isFull, options, type])

  // 播放器焦点控制
  const controlsFocus = useCallback(() => {
    cameraRef.current?.focus({ autoHide: true, delay: 3000 });
  }, [cameraRef])

  const controlsBlur = useCallback(() => {
    cameraRef.current?.blur();
  }, [cameraRef])

  const ControlComponents: React.ReactNode = useMemo(() => {
    return (
      <div onMouseEnter={controlsFocus} onMouseLeave={controlsBlur} onTouchStart={controlsFocus} onTouchMove={controlsFocus} onTouchCancel={controlsBlur}
        className={`monitorPlayer_content ${type} ${isFull ? 'full' : 'notFull'} ${options?.type} ${deviceType} ${isIos ? 'ios' : ''} ${isAndroid ? 'android' : ''} ${isDashboard ? 'dashboard' : ''}`}
        style={{ visibility: autoHide ? "visible" : "hidden", opacity: autoHide ? 1 : 0 }}>
        <div className={`monitorPlayer_controls ${deviceType} ${type} ${isFull ? 'full' : 'notFull'}  ${options?.type} ${isIos ? 'ios' : ''} ${isAndroid ? 'android' : ''}`}>
          {
            (type === 'Movie' && isFull) || options?.type === 'modal' ? <ProgressPlugin type={"full"} deviceType={deviceType} player={cameraRef.current} /> : <></>
          }
          <div className={`monitorPlayer_controls_btns ${isFull ? 'full' : 'notFull'}  ${options?.type}`}>
            {ControlButton}
          </div>
        </div>
      </div>
    )
  }, [ControlButton, autoHide, cameraRef, controlsBlur, controlsFocus, deviceType, isAndroid, isDashboard, isFull, isIos, options?.type, type])


  // 录制开始时显示录制时间
  useEffect(() => {
    if (isRecord) {
      let time: number = 0;
      recordTimeout.current = setInterval(() => {
        time++;
        let hours = Math.floor(time / 3600);
        let minutes = Math.floor(time / 60 % 60);
        let seconds = Math.floor(time % 60);
        setTemp_recordTime(`${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`);
      }, 1000);
    }
    return () => { if (recordTimeout.current) clearInterval(recordTimeout.current); } // 清除定时器
  }, [isRecord]);

  useEffect(() => {
    return timeout.current ? () => {
      if (timeout.current)
        clearTimeout(timeout.current);
    } : () => null;
  }, [])

  const history = useHistory();

  const onDetail = useCallback(() => {
    if (deviceType === 'pc' && temp_lookBackType.current === 'photo') { // 如果在pc的话直接全屏展开
      enterFullscreen();
      if (timeout.current) clearTimeout(timeout.current); // 清除悬浮定时器
      return;
    }
    const lookBackData: IlLookBackData = {
      type: temp_lookBackType.current,
      url: temp_lookBackType.current === 'photo' ? temp_screenshot.current : recordUrl, // 根据回看类型设置不同的url
      // 如果是截图，则携带截图信息，如果是视频，则携带视频信息
      photoName: temp_screenshot_name.current
    }

    // 跳转图片、视频详情页，携带对应参数，视频时应携带该视频的相关属性
    history.push({
      pathname: `${path}/lookBackDetail`, state: {
        lookBackData: lookBackData
      }
    })
  }, [deviceType, enterFullscreen, history, path, recordUrl])

  const getPhotoWidth = useCallback(() => {
    if (/%/ig.test(width)) {
      const num = Number(width.split('%')[0]) / 100;
      const radio = 125 / 392; // 125:392
      const w = window.innerWidth * num * radio; // 计算宽度
      console.log(window.innerWidth, num, radio, w);
      const h = w * 0.5625; // 16:9比例

      return { width: w, height: h }; // 返回rem值
    } else {
      const w = Number(width.split('px')[0]) * 0.295; // 如果是px则直接计算
      const h = w * 0.5625; // 16:9比例
      return { width: w, height: h }; // 返回rem值
    }
  }, [width])

  return (
    <>
      {ControlComponents}

      {
        type === 'Live' && isFull ? <div onMouseEnter={controlsFocus} onMouseLeave={controlsBlur} onTouchStart={controlsFocus} onTouchMove={controlsFocus} onTouchCancel={controlsBlur}
          className={`timeAxis_container ${isIos ? 'ios' : ''} ${deviceType}`} style={{ visibility: autoHide ? "visible" : "hidden", opacity: autoHide ? 1 : 0 }}>
          <TimeAxis current={selectedDate} type={type} isFull={isFull} deviceType={deviceType} events={timeAxisEvents} playerOpt={{
            player: {
              main: cameraRef,
              secondary: dualOptions?.cameraRef_secondary || null
            },
            url: {
              main: dualOptions?.urls.main || '',
              secondary: dualOptions?.urls.secondary || ''
            }
          }} isPause={isPause} setCurrent={setSelectedDate} />
        </div>
          : <></>
      }
      {
        finishScreenshot ?
          <div style={{ width: px2rem(`${getPhotoWidth().width + 6}`), height: px2rem(`${getPhotoWidth().height + 6}`) }}
            className={`screenshot_save ${isFull ? 'full' : 'notFull'}${isIos ? 'ios' : ''} ${isAndroid ? 'android' : ''}`}
            onClick={onDetail}>
            <PreloadImage style={{ width: px2rem(`${getPhotoWidth().width}px`) }} src={temp_screenshot.current} alt="temp_screenshot" />
            {
              temp_lookBackType.current === 'movie' ? <div className="screenshot_save_play">
                <PreloadImage className="screenshot_save_play_img" src={play} alt="play" />
                <span className="screenshot_save_play_span">{temp_recordTime}</span>
              </div> : <></>
            }
          </div> : <></>
      }

      {
        isRecord ?
          <div className={`record_monitoring_time ${type} ${deviceType}`}>
            <div className={`record_monitoring_time_rec ${type} ${deviceType}`}></div>
            {temp_recordTime}
          </div> : <></>
      }

      { /* 截图预览 */}
      <div style={{ visibility: isFullscreen ? 'visible' : 'hidden' }} ref={pc_screenshot_ref} className="control_player_screenshot_modal">
        <div className="control_player_screenshot_modal_close">
          <PreloadImage className="control_player_screenshot_modal_close_img" src={close} alt="close" onClick={() => {
            exitFullscreen();
            cameraRef.current?.exitFullscreen();
            temp_screenshot.current = ''; setFinishScreenshot(false);
          }} /> {/* 清除截图信息 */}
        </div>
        <PreloadImage style={{ height: '100%' }} src={temp_screenshot.current} alt="screenshot" />
      </div>
    </>
  )
}

export default PlayerControl;