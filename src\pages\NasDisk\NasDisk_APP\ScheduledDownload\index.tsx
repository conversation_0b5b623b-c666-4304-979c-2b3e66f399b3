import { ProgressBar, Toast, Checkbox, Loading } from "antd-mobile";
import styles from "./index.module.scss";
import { useHistory, useRouteMatch } from "react-router-dom";
import { useState, useEffect, useCallback } from "react";
import { useRequest } from "ahooks";

import tipImg from "@/Resources/icon/automatic-download.png";
import GuidePanel from "@/pages/NasDisk/NasDisk_APP/components/GuidePanel";
import request from "@/request";
import { TaskInfo, controlTask, ControlTaskParams } from "@/api/nasDisk";
import fileIcon from "@/Resources/icon/file-icon.png";
import start from "@/Resources/icon/start.png";
import pause from "@/Resources/icon/startIcon.png";
import closeIcon from "@/Resources/icon/close.png";
import closeDark from "@/Resources/icon/close_white.png";
import { PreloadImage } from "@/components/Image";
import { useTheme } from "@/utils/themeDetector";
import deletes from "@/Resources/icon/delete.png";
import deletesDark from "@/Resources/icon/delete_white.png";
import { modalShow } from "@/components/List";
import { formatBytes } from "../SynchronizationTab";
import outlineIcon from "@/Resources/icon/outline.png";
import outlineDarkIcon from "@/Resources/icon/outline-dark.png";

// 最大任务数量限制
const MAX_TASK_COUNT = 5;

// 格式化时间 - 处理毫秒时间戳
const formatTime = (timestamp: string): string => {
  if (!timestamp) return "";
  const date = new Date(parseInt(timestamp));
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 获取任务显示名称
const getTaskDisplayName = (task: TaskInfo): string => {
  if (task.src && task.src.length > 0) {
    // 获取最后一个路径部分
    const path = decodeURIComponent(task.src[task.src.length - 1]);
    // 移除前导斜杠"/"
    return path.startsWith('/') ? path.substring(1) : path;
  }
  return task.dst || `任务${task.task_id}`;
};

export default function ScheduledDownload(props: { 
  isVip?: boolean;
  onTasksChange?: (hasAnyTasks: boolean) => void;
  isEditMode?: boolean;
  setIsEditMode?: (isEditMode: boolean)=> void
}) {
  const history = useHistory();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();
  const { isEditMode, setIsEditMode } = props || {}

  // 下载任务列表
  const [downloadingTasks, setDownloadingTasks] = useState<TaskInfo[]>([]);
  const [, setHasError] = useState(false);
  const [, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [longPressTimeout, setLongPressTimeout] =
    useState<NodeJS.Timeout | null>(null);

  // 使用useRequest处理任务控制
  const { run: runControlTask } = useRequest(
    (params: ControlTaskParams) => controlTask(params),
    {
      manual: true,
      onSuccess: (result, params) => {
        if (result.code === 0) {
          const commandText: Record<string, string> = {
            pause: "暂停",
            continue: "启动",
            cancel: "取消",
            restart: "重启",
          };
          const command = params[0].command;
          Toast.show({
            content: `任务已${commandText[command] || "操作成功"}`,
            position: "bottom",
          });
          // 重新获取任务列表
          fetchAutoDownloadTasks();
        } else {
          Toast.show({
            content: result.result || "操作失败",
            position: "bottom",
          });
        }
      },
      onError: (error) => {
        console.error("任务操作失败:", error);
        Toast.show({
          content: "操作失败，请重试",
          position: "bottom",
        });
      },
    }
  );

  // 获取自动下载任务
  const fetchAutoDownloadTasks = useCallback(async () => {
    // 只有在有任务时才显示加载状态，避免空状态时的闪动
    if (downloadingTasks.length > 0) {
      setIsLoading(true);
    }
    
    try {
      // 获取所有自动下载任务
      const response = await request.post(
        "/taskcenter/get_taskinfo",
        {
          selector: [
            {
              key: "module",
              value: ["bpan"],
            },
            {
              key: "type",
              value: ["active"],
            },
            {
              key: "action",
              value: ["auto_download"],
            },
          ],
        },
        { showLoading: false }
      ); // 禁用loading动画

      // 处理任务
      if (response?.data?.info) {
        const tasks = response.data.info;
        // 按状态排序：running > paused > waiting > success_waiting > 其他
        const sortedTasks = tasks.sort((a: TaskInfo, b: TaskInfo) => {
          const statusOrder: Record<string, number> = {
            running: 0,
            paused: 1,
            waiting: 2,
            success_waiting: 3,
            failed: 4,
            cancelled: 5,
            "partial error": 6,
          };

          const orderA = statusOrder[a.status] ?? 999;
          const orderB = statusOrder[b.status] ?? 999;

          return orderA - orderB;
        });

        setDownloadingTasks(sortedTasks);
      } else {
        setDownloadingTasks([]);
      }

      setHasError(false);
    } catch (error) {
      console.error("获取自动下载任务失败:", error);
      setHasError(true);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [downloadingTasks.length]);

  // 初始化数据
  useEffect(() => {
    // 立即获取数据
    fetchAutoDownloadTasks();
  }, [fetchAutoDownloadTasks]);

  // 根据任务数量设置轮询
  useEffect(() => {
    // 只有当有任务时才设置轮询
    let timer: NodeJS.Timeout | null = null;
    
    if (downloadingTasks.length > 0) {
      timer = setInterval(fetchAutoDownloadTasks, 5000); // 每5秒轮询任务
    }

    // 通知父组件任务状态变化
    if (props.onTasksChange) {
      props.onTasksChange(downloadingTasks.length > 0);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [downloadingTasks.length, fetchAutoDownloadTasks, props.onTasksChange]);

  // 导航到选择文件夹页面
  const handleSelectFolder = () => {
    // 检查是否已达到最大任务数量
    if (downloadingTasks.length >= MAX_TASK_COUNT) {
      Toast.show({
        content: `最多只能添加${MAX_TASK_COUNT}个自动下载任务，请删除后再添加`,
        position: "bottom",
      });
      return;
    }

    // 获取已有任务的文件夹路径列表
    const existingFolderPaths = downloadingTasks.map(task => {
      if (task.src && task.src.length > 0) {
        // 使用完整路径作为标识
        return task.src;
      }
      return [];
    }).flat();

    history.push({
      pathname: `${path}/selectFolder`,
      state: {
        fromTab: "download", // 记录来源tab
        isVip: props?.isVip,
        existingFolderPaths: existingFolderPaths,
        existingTaskCount: downloadingTasks.length // 传递已有任务数量
      },
    });
  };

  // 暂停/启动任务
  const handleTogglePause = (taskId: string, currentStatus: string) => {
    // 实际应用中应该调用API暂停/启动任务
    try {
      const command = (currentStatus === "running" || currentStatus === 'waiting') ? "pause" : "continue";
      runControlTask({
        task_id: [taskId],
        command: command,
      });
    } catch (error) {
      console.log("error: ", error);
    }
  };

  // 处理长按开始
  const handleLongPressStart = (taskId: string) => {
     if (!setIsEditMode) return;
    const timeout = setTimeout(() => {
      setIsEditMode(true);
      setSelectedTasks([]); // 进入编辑模式时不选中任何任务
    }, 500); // 500ms长按触发
    setLongPressTimeout(timeout);
  };

  // 处理长按结束
  const handleLongPressEnd = () => {
    if (longPressTimeout) {
      clearTimeout(longPressTimeout);
      setLongPressTimeout(null);
    }
  };

  // 处理退出编辑模式
  const handleExitEditMode = () => {
     if (!setIsEditMode) return;
    setIsEditMode(false);
    setSelectedTasks([]);
  };

  // 处理选择/取消选择任务
  const handleToggleSelectTask = (taskId: string) => {
    if (selectedTasks.includes(taskId)) {
      setSelectedTasks(selectedTasks.filter((id) => id !== taskId));
    } else {
      setSelectedTasks([...selectedTasks, taskId]);
    }
  };

  // 处理全选/取消全选
  const handleToggleSelectAll = () => {
    if (selectedTasks.length === downloadingTasks.length) {
      // 如果已全选，则取消全选
      setSelectedTasks([]);
    } else {
      // 否则全选
      setSelectedTasks(downloadingTasks.map((task) => task.task_id));
    }
  };

  // 处理批量删除任务
  const handleBatchDelete = () => {
    if (selectedTasks.length === 0) {
      Toast.show({
        content: "请选择要删除的任务",
        position: "bottom",
      });
      return;
    }

    // 显示删除确认弹窗
    modalShow(
      "删除自动下载任务",
      <div className={styles.deleteConfirmContent}>仅删除自动下载任务，不会删除已下载文件</div>,
      (modal) => {
        // 使用控制任务接口取消任务
        try {
          runControlTask({
            task_id: selectedTasks,
            command: "cancel",
          });
          // 立即更新本地状态，提升用户体验
          setDownloadingTasks((prevTasks) =>
            prevTasks.map((task) =>
              selectedTasks.includes(task.task_id)
                ? { ...task, status: "cancelled" }
                : task
            )
          );
          handleExitEditMode();
        } catch (error) {
          console.log("error: ", error);
          Toast.show({
            content: "删除任务失败，请重试",
            position: "bottom",
          });
        }
        modal.destroy();
      },
      () => {
        // 取消删除
        console.log("取消删除任务");
      },
      false,
      {
        position: "bottom",
        okBtnText: "确定",
        cancelBtnText: "取消",
      }
    );
  };

  // 空状态UI
  const renderEmptyState = () => (
    <GuidePanel
      imageSrc={tipImg}
      title="文件夹自动下载至存储"
      description="百度网盘的文件夹设置为自动下载后，智能存储会定期（非休眠期间每小时）检查此文件夹是否有新文件加入，如果有新文件，将会自动把新增文件下载到智能存储"
      buttonText="新增自动下载任务"
      onButtonClick={handleSelectFolder}
      buttonClassName={styles.primaryButton}
    />
  );

  // 获取任务状态图标
  const getTaskStatusIcon = (task: TaskInfo) => {
    if (task.status === "running" || task.status === "waiting") {
      return <img alt="" src={pause} className={styles.pauseIcon} />; // 运行中显示暂停图标
    } else {
      return <img alt="" src={start} className={styles.playIcon} />; // 其他状态显示播放图标
    }
  };

  // 获取任务路径显示文本
  const getTaskPathDisplay = (task: TaskInfo) => {
    if (!task.src || task.src.length === 0) return "内部存储";

    // 处理路径，移除前导斜杠
    const cleanPaths = task.src.map(path =>{
      const decodePath = decodeURIComponent(path)
      return decodePath.startsWith('/') ? decodePath.substring(1) : decodePath
    } 
    );

    // 只显示前两级路径
    if (cleanPaths.length <= 2) {
      return cleanPaths.join(" > ");
    }

    // 如果路径超过两级，显示第一级和最后一级
    const firstPath = cleanPaths[0];
    const lastPath = cleanPaths[cleanPaths.length - 1];

    if (cleanPaths.length === 3) {
      return `${firstPath} > ${cleanPaths[1]} > ${lastPath}`;
    }

    return `${firstPath} > ${lastPath}`;
  };

  // 获取任务状态文本
  const getTaskStatusText = (task: TaskInfo) => {
    switch (task.status) {
      case "waiting":
        return "等待中";
      case "running":
        return `${formatBytes(task.detail?.handle_size || 0)}/${formatBytes(task.detail?.total_size || 0)}` || "下载中";
      case "paused":
        return "已暂停";
      case "success_waiting":
        return "已完成";
      case "failed":
        return "下载失败";
      case "cancelled":
        return "已取消";
      case "partial error":
        return "部分失败";
      default:
        return "未知状态";
    }
  };

  // 获取文件计数显示
  const getFileCountText = (task: TaskInfo) => {
    if (!task.detail) return "";
    if (task.status === "success_waiting") return "";
    const { finish_file_cnt = 0, total_file_cnt } = task.detail;
    return `${finish_file_cnt}/${total_file_cnt}`;
  };

  // 计算任务的更新时间显示
  const getTaskUpdateTime = (task: TaskInfo) => {
    const timestamp = task.status === "success_waiting" ? task.detail?.finish_time : task.create_time;
    if (!timestamp) return "";

    const taskTime = parseInt(timestamp);
    const now = Date.now();
    const diffMinutes = Math.floor((now - taskTime) / (60 * 1000));

    if (diffMinutes < 1) return "刚刚更新";
    if (diffMinutes < 60) return `${diffMinutes}分钟前更新`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}小时前更新`;

    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 30) return `${diffDays}天前更新`;

    return formatTime(timestamp);
  };

  // 判断是否已达到最大任务数量
  const hasReachedMaxTasks = downloadingTasks.length >= MAX_TASK_COUNT;

  // 编辑模式UI
  const renderEditMode = () => (
    <div className={styles.editModeContainer}>
      <div className={styles.editHeader}>
        <div onClick={handleExitEditMode}>
          <img src={isDarkMode ? closeDark : closeIcon} alt="关闭" className={styles.closeIcon} />
        </div>
        <PreloadImage
          src={isDarkMode ? outlineDarkIcon : outlineIcon}
          alt="全选"
          style={{ width: "40px", height: "40px" }}
          onClick={handleToggleSelectAll}
      />
      </div>
      <div className={styles.editTitle}>
        删除任务
      </div>

      <div className={styles.editContent}>
        {downloadingTasks.map((task) => (
          <div
            key={task.task_id}
            className={`${styles.taskItem} ${
              selectedTasks.includes(task.task_id) ? styles.selected : ""
            }`}
            onClick={() => handleToggleSelectTask(task.task_id)}
          >
            <div className={styles.taskMainInfo}>
              <div className={styles.taskIcon}>
                <PreloadImage
                  src={fileIcon}
                  alt=""
                  style={{ width: 40, height: 40 }}
                />
              </div>
              <div className={styles.taskContent}>
                <div className={styles.taskName}>
                  {getTaskDisplayName(task)}
                </div>
                <div className={styles.progressContainer}>
                  <ProgressBar
                    percent={task.detail?.progress || 0}
                    text={false}
                    className={styles.taskProgress}
                    style={
                      {
                        "--fill-color": "var(--primary-color)",
                      } as React.CSSProperties
                    }
                  />
                </div>
                <div className={styles.statusContainer}>
                  <div className={styles.taskStatus}>
                    {getTaskStatusText(task)}
                  </div>
                  {task.detail && task.detail.total_file_cnt > 0 && (
                    <div className={styles.fileCount}>
                      {getFileCountText(task)}
                    </div>
                  )}
                </div>
              </div>
              <div
                className={styles.checkboxContainer}
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡
                  handleToggleSelectTask(task.task_id);
                }}
              >
                <Checkbox
                  checked={selectedTasks.includes(task.task_id)}
                  className={styles.taskCheckbox}
                />
              </div>
            </div>
            <div className={styles.taskSubInfo}>
              <div className={styles.taskPath}>{getTaskPathDisplay(task)}</div>
              <div className={styles.taskTime}>{getTaskUpdateTime(task)}</div>
            </div>
          </div>
        ))}
      </div>

      <div className={styles.deleteFooter}>
        <div
          className={`${styles.deleteButton} ${
            selectedTasks.length === 0 ? styles.disabled : ""
          }`}
          onClick={selectedTasks.length > 0 ? handleBatchDelete : undefined}
        >
          <img
            alt=""
            src={isDarkMode ? deletesDark : deletes}
            className={styles.photoEditDeleteIcon}
            style={{ width: 25, height: 25 }}
          />
          <span>删除</span>
        </div>
      </div>
    </div>
  );

  // 任务列表UI
  const renderTaskList = () => (
    <div className={styles.taskContainer}>
      <div className={styles.taskHeader}>
        <div className={styles.taskHeaderTitle}>
          自动下载的文件夹（最多5项）
        </div>
        <div
          className={`${styles.newBadge} ${
            hasReachedMaxTasks ? styles.disabled : ""
          }`}
          onClick={hasReachedMaxTasks ? undefined : handleSelectFolder}
        >
          新增
        </div>
      </div>

      {/* 任务列表 */}
      {downloadingTasks.map((task) => (
        <div
          key={task.task_id}
          className={styles.taskItem}
          onTouchStart={() => handleLongPressStart(task.task_id)}
          onTouchEnd={handleLongPressEnd}
          onTouchMove={handleLongPressEnd}
        >
          <div className={styles.taskMainInfo}>
            <div className={styles.taskIcon}>
              <PreloadImage
                src={fileIcon}
                alt=""
                style={{ width: 40, height: 40 }}
              />
            </div>
            <div className={styles.taskContent}>
              <div className={styles.taskName}>{getTaskDisplayName(task)}</div>
              <div className={styles.progressContainer}>
                <ProgressBar
                  percent={task.detail?.progress || 0}
                  text={false}
                  className={styles.taskProgress}
                  style={
                    {
                      "--fill-color": "var(--primary-color)",
                    } as React.CSSProperties
                  }
                />
              </div>
              <div className={styles.statusContainer}>
                <div className={styles.taskStatus}>
                  {getTaskStatusText(task)}
                </div>
                {task.detail && task.detail?.total_file_cnt > 0 && (
                  <div className={styles.fileCount}>
                    {getFileCountText(task)}
                  </div>
                )}
              </div>
            </div>
            <div className={styles.taskActions}>
              <span
                className={styles.actionButton}
                onClick={() => handleTogglePause(task.task_id, task.status)}
              >
                {getTaskStatusIcon(task)}
              </span>
            </div>
          </div>
          <div className={styles.taskSubInfo}>
            <div className={styles.taskPath}>{getTaskPathDisplay(task)}</div>
            <div className={styles.taskTime}>{getTaskUpdateTime(task)}</div>
          </div>
        </div>
      ))}
    </div>
  );

  // 根据是否有任务决定显示哪个UI
  const hasAnyTasks = downloadingTasks.length > 0;

  // 如果处于编辑模式，显示编辑模式UI
  if (isEditMode) {
    return renderEditMode();
  }

  // 如果正在初始加载，显示加载状态
  if (!isInitialized) {
    return (
      <div className={styles.loadingContainer}>
        <Loading />
        <span>加载中...</span>
      </div>
    );
  }

  // 否则显示正常UI
  return hasAnyTasks ? renderTaskList() : renderEmptyState();
}
