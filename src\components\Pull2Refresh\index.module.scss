/* 下拉刷新容器样式 */
.ptr_container {
  position: relative;
  overflow: hidden;
}

.pull_status_container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;

  span {
    font-family: MiSans W;
    font-weight: 400;
    font-size: 17px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    color: var(--subtitle-text-color);
  }

  .pull_icon {
    border: 2px solid var(--subtitle-text-color);
    margin-bottom: 8px;
    max-width: 30px;
    max-height: 100px;
  }
}

/* 刷新指示器 */
.ptr_indicator {
  width: 100%;
  height: 0px;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: #666;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ptr_spinner {
  animation: ptr_spin 1.2s linear infinite;
  width: 24px;
  height: 24px;
}

.ptr_spinner path {
  fill: currentColor;
}

/* 内容区域 */
.ptr_content {
  position: relative;
  z-index: 1;
  display: flex;
  width: 100%;
  height: 100%;
}

/* 阻力效果（可选） */
.ptr_resistance {
  transition-timing-function: cubic-bezier(0.17, 0.67, 0.42, 1.18);
}

/* 移动端优化 */
@media (hover: none) and (pointer: coarse) {
  .ptr_container {
    -webkit-overflow-scrolling: touch;
  }
}
