.eventLookBack_container {
  width: 100%;
  height: 100%;
  padding: 28px 28px 0 28px;
  user-select: none;
  display: flex;
  flex-direction: column;
}

.eventLookBack_title {
  font-size: 32px;
  font-weight: 400;
  color: var(--text-color);
}

.eventLookBack_event_span {
  color: rgba(140, 147, 176, 1);
  font-size: 14px;
  font-weight: 400;
}

.eventLookBack_eventFilter,
.eventLookBack_timeFilter {
  margin: 20px 0;
  width: 100%;
}

.eventLookBack_lookBack {
  margin-top: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.eventLookBack_eventFilter_container {
  width: 100%;
  padding: 10px 0px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.eventLookBack_eventFilter_item {
  width: 100px;
  padding: 10px;
  background-color: var(--event-card-background-color);
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(102, 102, 102, 1);

  img {
    height: 15px;
    margin-right: 8px;
  }
}

.selected_all {
  background-color: rgba(0, 113, 205, 1);
  color: #fff;
}
.selected_move {
  background-color: rgba(253, 197, 65, 1);
  color: #fff;
}
.selected_human {
  background-color: rgba(68, 206, 202, 1);
  color: #fff;
}
.selected_fire {
  background-color: rgba(110, 130, 253, 1);
  color: #fff;
}
.selected_pet {
  background-color: rgba(117, 223, 110, 1);
  color: #fff;
}
.selected_noise {
  background-color: rgba(125, 166, 224, 1);
  color: #fff;
}

.eventLookBack_timeFilter_container {
  height: 100px;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  margin-top: 10px;
}

.eventLookBack_timeFilter_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
}

.eventLookBack_timeFilter_item_label {
  font-weight: 500;
  font-size: 16px;
  color: var(--text-color);
  padding: 0 10px;
}

.eventLookBack_timeFilter_item_date {
  color: var(--list-value-text-color);
  font-size: 13px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 0 10px;

  img {
    height: 16px;
  }
}

.eventLookBack_lookBack_container {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-top: 20px;
}

.eventLookBack_noData {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color);
}

.eventLookBack_lookBack_card_content {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.eventLookBack_lookBack_card_left {
  width: 110px;
  height: 75px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;

  img {
    height: 70px;
  }
}

.eventLookBack_lookBack_card_center {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* align-items: center; */
  justify-content: center;
  margin: 0 8px;
}

.eventLookBack_lookBack_card_title {
  font-weight: 400;
  font-size: 18px;
  color: var(--text-color);
}

.eventLookBack_lookBack_card_subtitle {
  font-weight: 400;
  font-size: 12px;
  color: var(--list-value-text-color);
  margin-top: 8px;
}

.eventLookBack_lookBack_card_right {
  width: 80px;
  height: 35px;
  border-radius: 15px;
}

.eventLookBack_lookBack_card_btn {
  width: 80px;
  height: 34px;
  background-color: var(--primary-btn-background-color);
  color: var(--primary-color);
  border-radius: 15px;
  padding: 6px 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
