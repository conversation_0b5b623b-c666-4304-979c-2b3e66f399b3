.container {
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #fff;
  height: 100vh;
  overflow: hidden;
}

.headerSection {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  padding: 40px 60px;
  background-color: #1a1a1a;
  // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.leftSection {
  display: flex;
  align-items: center;
  gap: 20px;
}

.backButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }

  svg {
    color: #fff;
    font-size: 18px;
  }
}

.actorName {
  font-size: 36px;
  font-weight: 600;
  color: #ffffff;
  margin-left: 20px;
}

.actorAvatar {
  // border-radius: 50%;
//   margin-top: 200px;
  // object-fit: cover;
  width: 124px;
  height: 124px;
  // background-color: rgba(255, 255, 255, 0.1);
  font-size: 48px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.6);
  // background: red;
}

.worksSection {
  flex: 1;
  padding: 40px 60px;
  overflow-y: auto;
  background-color: #1a1a1a;

  // 设置滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.sectionTitle {
  font-family: 'MiSans W', sans-serif;
  font-weight: 400;
  font-size: 18px;
  line-height: 1.2;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 30px 0;
}

.worksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: 24px;
  width: 100%;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-family: 'MiSans', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.emptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.emptyText {
  font-family: 'MiSans', sans-serif;
  font-weight: 400;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
}