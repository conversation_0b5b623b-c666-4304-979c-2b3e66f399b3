import React, { useEffect, createContext, useContext, useState, useCallback } from 'react';
import themes from '../utils/theme';
import { getDeviceType } from './DeviceType';

// 创建Context
type ThemeContextType = {
  isDarkMode: boolean;
  toggleTheme: (theme: 'light' | 'dark') => void;
};

const ThemeContext = createContext<ThemeContextType>({
  isDarkMode: false,
  toggleTheme: (theme: 'light' | 'dark') => { }
});

export const useTheme = () => useContext(ThemeContext);

const ThemeDetector: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(
    window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  const deviceType = getDeviceType();

  const applyTheme = useCallback((theme: 'light' | 'dark') => {
    const root = document.documentElement || document.body;
    console.log(`当前主题:${theme}`);
    const themeVariables = themes[theme];
    for (const [key, value] of Object.entries(themeVariables)) {
      root.style.setProperty(key, value);
    }
    setIsDarkMode(theme === 'dark');
  }, []);

  const mediaQueryListener = useCallback(() => {
    // 媒体查询检测系统是否处于暗黑模式
    const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    applyTheme(isDarkMode ? 'dark' : 'light');

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      applyTheme(e.matches ? 'dark' : 'light');
    };
    mediaQuery.addEventListener('change', handleChange);
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [applyTheme])

  useEffect(() => {
    console.log(`当前设备类型:${deviceType === 1 ? 'pc' : deviceType === 2 ? 'tv端' : '移动端'}`);
    if (deviceType) {
      const currentColor = localStorage.getItem('currentColor');

      // 0 系统 1.白天 2.黑夜 根据当前主题修改颜色模式
      if (currentColor) {
        switch (currentColor) {
          case '1': applyTheme('light'); break;
          case '2': applyTheme('dark'); break;
          default: mediaQueryListener(); break;
        }
      } else {
        // 如果没有设置主题，使用默认主题
        mediaQueryListener();
      }

      // 监听本地存储变化，以便在用户更改主题时更新界面颜色模式
      window.addEventListener('storage', event => {
        console.log('监听到本地存储变化: ', event);
        if (event.key === 'currentColor') {
          console.log(`需要修改当前主题: ${event.newValue === '1' ? '白天' : event.newValue === '2' ? '黑夜' : '系统'}`);
          // 0 系统 1.白天 2.黑夜
          switch (event.newValue) {
            case '1': applyTheme('light'); break;
            case '2': applyTheme('dark'); break;
            default: mediaQueryListener(); break;
          }
          return;
        }
      });

      return;
    }

    mediaQueryListener();
  }, [applyTheme, deviceType, mediaQueryListener]);

  return (
    <ThemeContext.Provider value={{ isDarkMode, toggleTheme: applyTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeDetector;