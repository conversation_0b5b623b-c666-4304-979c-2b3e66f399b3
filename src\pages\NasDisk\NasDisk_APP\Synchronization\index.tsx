import React, { useState, useEffect, useCallback } from 'react';
import { Button, Toast, Loading, Checkbox } from 'antd-mobile';
import { HeartFill } from 'antd-mobile-icons';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { useHistory, useLocation } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { getPoolInfo, listDirectory, StoragePool } from '@/api/fatWall';
import { uploadToBaiduNetdisk, BaiduUploadPathItem } from '@/api/nasDisk';
import { useTheme } from '@/utils/themeDetector';
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import UploadBDSelector from '../components/UploadBDSelector';
import { PreloadImage } from '@/components/Image';
import file_icon from "@/Resources/icon/file-icon.png";
interface FileItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  time: string;
  itemCount?: number;
  isLiked?: boolean;
  path: string;
  isDirectory?: boolean;
  dataDir?: string;
}

interface LocationState {
  selectedUploadPath?: string;
  selectedDisplayPath?: string;
  existingTaskCount?: number;
  existingFolderPaths?: string[];
  selectedFolders?: string[];
}

const Synchronization: React.FC = () => {
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { isDarkMode } = useTheme();
  // 顶层文件夹列表
  const [topLevelFolders, setTopLevelFolders] = useState<FileItem[]>([]);

  // 选中的文件夹ID列表
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);

  // 获取已有任务数量和路径
  const existingTaskCount = location.state?.existingTaskCount || 0;
  const existingFolderPaths = location.state?.existingFolderPaths || [];
  const maxSelectableCount = 5 - existingTaskCount;

  // 检查文件夹是否已被选择
  const isFolderAlreadySelected = (folderPath: string) => {
    return existingFolderPaths.includes(folderPath);
  };

  // 当前存储池名称
  const [currentPoolName, setCurrentPoolName] = useState<string>('百度网盘');

  // 存储池列表
  const [, setStoragePools] = useState<StoragePool[]>([]);

  // webDAV配置信息
  const [, setWebDAVConfig] = useState<{
    alias_root: string;
  } | null>(null);

  // 选择的上传路径信息
  const [uploadLocation, setUploadLocation] = useState<{
    path: string;
    displayPath: string;
  }>({
    path: '/',
    displayPath: '百度网盘'
  });

  // UploadBD弹出框状态
  const [uploadBDVisible, setUploadBDVisible] = useState<boolean>(false);

  // 获取存储池信息
  const { run: fetchPoolInfo, loading: poolLoading } = useRequest(
    getPoolInfo,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          setStoragePools(response.data.internal_pool);

          // 保存webDAV配置
          if (response.data.webDAV) {
            setWebDAVConfig({
              alias_root: response.data.webDAV.alias_root
            });
          }

          // 获取第一个存储池的顶层目录作为顶层文件夹
          if (response.data.internal_pool.length > 0) {
            const firstPool = response.data.internal_pool[0];

            // 设置当前存储池名称
            setCurrentPoolName(firstPool.name);

            let pathParent = firstPool.data_dir;

            if (response.data.webDAV?.alias_root) {
              const dataDir = firstPool.data_dir.endsWith('/') ? firstPool.data_dir.slice(0, -1) : firstPool.data_dir;
              const aliasRoot = response.data.webDAV.alias_root;
              pathParent = aliasRoot + dataDir;
            }

            // 获取顶层目录
            fetchTopLevelDirectory({
              path: {
                parent: pathParent,
                recursion: false
              }
            });
          }
        }
      },
      onError: (error) => {
        console.error('获取存储池信息失败：', error);
        Toast.show({
          content: '获取存储池信息失败，请重试',
          position: 'bottom',
          duration: 2000,
        });
        setTopLevelFolders([]);
      },
    }
  );

  // 获取顶层目录列表
  const { run: fetchTopLevelDirectory, loading: directoryLoading } = useRequest(
    listDirectory,
    {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          const files: FileItem[] = response.data.files
            .filter(file => file.xattr.directory)
            .map((file, index) => ({
              id: `file_${index}`,
              name: file.name,
              type: 'folder' as const,
              time: new Date(parseInt(file.modified_time)).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              }).replace(/\//g, '/').replace(/,/g, ''),
              itemCount: Math.floor(Math.random() * 50) + 1, // 模拟项目数量
              path: `${file.parent}/${file.name}`,
              isDirectory: file.xattr.directory,
              isLiked: file.xattr.favorite
            }));
          setTopLevelFolders(files);
        }
      },
      onError: (error) => {
        console.error('获取顶层目录失败：', error);
        Toast.show({
          content: '获取顶层目录失败，请重试',
          duration: 2000,
        });
        setTopLevelFolders([]);
      },
    }
  );

  // 使用useRequest处理上传
  const { run: runUpload, loading: uploadLoading } = useRequest(
    uploadToBaiduNetdisk,
    {
      manual: true,
      onSuccess: (response, params) => {
        if (response.code === 0) {
          Toast.show({
            content: `上传任务已创建，任务ID: ${response.task_id || '未知'}`,
            duration: 3000,
          });

          // 重置选择状态
          setSelectedFolders([]);

          // 跳转到首页的synchronization tab
          history.push({
            pathname: '/baiduNetdisk_app',
            state: {
              activeTab: 'synchronization',
              isVip: true
            }
          });
        } else {
          const errorMessages: Record<number, string> = {
            2: "参数错误",
            9: "文件或目录不存在",
            111: "有其他异步任务正在执行",
            112: "页面已过期，请刷新后重试",
            117: "网络连接问题，请稍后重试",
          };

          const errorMessage = errorMessages[response.errno || response.code] || response.errmsg || "上传失败";
          Toast.show({
            content: errorMessage,
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("上传请求失败:", error);
        Toast.show({
          content: "上传失败，请检查网络连接",
          duration: 2000,
        });
      },
    }
  );

  // 初始化时获取存储池信息，并检查是否有路径信息
  useEffect(() => {
    fetchPoolInfo({});

    // 检查是否有从UploadBD传递过来的路径信息
    if (location.state?.selectedUploadPath && location.state?.selectedDisplayPath) {
      setUploadLocation({
        path: location.state.selectedUploadPath,
        displayPath: location.state.selectedDisplayPath
      });
    }

    // 恢复之前选中的文件夹状态
    if (location.state?.selectedFolders) {
      setSelectedFolders(location.state.selectedFolders);
    }
  }, [fetchPoolInfo, location.state]);

  // 处理文件夹选中状态
  const handleFolderSelect = useCallback((folderId: string, checked: boolean) => {
    if (checked) {
      if (selectedFolders.length >= maxSelectableCount) {
        Toast.show({
          content: '最多只能选择5个文件夹',
          duration: 2000,
        });
        return;
      }
      setSelectedFolders(prev => [...prev, folderId]);
    } else {
      setSelectedFolders(prev => prev.filter(id => id !== folderId));
    }
  }, [selectedFolders, maxSelectableCount]);

  // 处理返回
  const handleBack = () => {
    history.goBack();
  };

  // 处理确定按钮 - 调用上传接口
  const handleConfirm = () => {
    if (selectedFolders.length === 0) {
      Toast.show({
        content: '请至少选择一个文件夹',
        duration: 2000,
      });
      return;
    }

    // 获取选中的文件夹路径
    const selectedPaths = topLevelFolders
      .filter(folder => selectedFolders.includes(folder.id))
      .map(folder => folder.path);

    // 格式化为上传接口需要的格式
    const formattedLocalpath: BaiduUploadPathItem[] = selectedPaths.map(path => ({
      type: "directory",
      path: path
    }));

    console.log("准备上传到百度网盘:", {
      remotepath: uploadLocation.path,
      localpath: formattedLocalpath,
    });

    // 调用百度网盘上传接口
    runUpload({
      action: "upload",
      autotask: 1,
      remotepath: uploadLocation.path,
      localpath: formattedLocalpath,
    });
  };
  // 页面曝光埋点
  useEffect(() => {
    window.onetrack?.('track', 'nasDisk_autoUpload_expose');
  }, []);
  const toUpload = () => {
    setUploadBDVisible(true);
  }

  // 处理UploadBD弹出框关闭
  const handleUploadBDClose = () => {
    setUploadBDVisible(false);
  };

  // 处理UploadBD路径选择
  const handleUploadBDSelect = (path: string, displayPath: string) => {
    setUploadLocation({
      path,
      displayPath
    });
    setUploadBDVisible(false);
  };

  return (
    <div className={styles.synchronizationContainer}>
      <div className={styles.fixedHeader}>
        <NavigatorBar
          onBack={handleBack}
          backIcon={isDarkMode ? arrowLeftDark : arrowLeft}
        />
        <div className={styles.title}>选择文件夹（最多5项）</div>

        {/* 面包屑 - 显示当前存储池名称 */}
        <div className={styles.breadcrumb}>
          <span className={styles.breadcrumbItem}>{currentPoolName}</span>
        </div>
      </div>

      <div className={styles.scrollableContent}>
        {/* 加载状态 */}
        {(poolLoading || directoryLoading) && (
          <div className={styles.loadingContainer}>
            <Loading />
            <span>加载中...</span>
          </div>
        )}

        {/* 文件列表 */}
        <div className={styles.fileList}>
          {topLevelFolders.map(folder => {
            const isAlreadySelected = isFolderAlreadySelected(folder.path);
            return (
              <div
                key={folder.id}
                className={`${styles.fileItem} ${isAlreadySelected ? styles.disabledItem : ''}`}
              >
                <div className={styles.fileIcon}>
                   <PreloadImage src={file_icon} alt='close' style={{ width: '40px', height: '40px' }}  />
                </div>
                <div className={styles.fileInfo}>
                  <div className={styles.fileName}>
                    {folder.name}
                    {folder.isLiked && <HeartFill className={styles.heartIcon} />}
                  </div>
                  <div className={styles.fileDetails}>
                    {folder.time}
                    {isAlreadySelected && <span className={styles.selectedTag}></span>}
                  </div>
                </div>
                <div className={styles.checkboxContainer}>
                  <Checkbox
                    checked={isAlreadySelected || selectedFolders.includes(folder.id)}
                    disabled={isAlreadySelected}
                    onChange={(checked) => handleFolderSelect(folder.id, checked)}
                  />
                </div>
              </div>
            );
          })}

          {!poolLoading && !directoryLoading && topLevelFolders.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>
      </div>

      {/* 底部按钮 */}
      <div className={styles.footer}>
        <Button
          className={styles.leftBut}
          onClick={toUpload}
        >
          自动上传到：{uploadLocation.displayPath}


        </Button>
        <Button
        color="primary"
          className={styles.rightBut}
          onClick={handleConfirm}
          disabled={selectedFolders.length === 0 || uploadLoading}
          loading={uploadLoading}
        >
          {uploadLoading ? '上传中...' : '确定'}
        </Button>
      </div>

      {/* UploadBD弹出框 */}
      <UploadBDSelector
        visible={uploadBDVisible}
        onClose={handleUploadBDClose}
        onSelect={handleUploadBDSelect}
        title="更改自动上传位置"
      />
    </div>
  );
};

export default Synchronization;
