import React, { FC, useContext, useEffect, } from "react";
import FilmSideBar from "./SideBar";
import styles from './index.module.scss';
import RecentlyPlay from "./optPages/RecentlyPlay";
import { Route, Switch, useRouteMatch } from "react-router-dom";
import AllRecentlyPlay from "./AllRecentlyPlay";
import RecentlyAdd from "./RecentlyAdd";
import AllRecentlyAdd from "./AllRecentlyAdd";
import All from "./optPages/All";
import Played from "./Played";
import Collect from "./Collect";
import Library from "./Library";
import SearchByTV from "./Search";
import DramasOrMovie from "./DramasOrMovie";
import VideoDetails from "./VideoDetails";
import ActorDetail from "./VideoDetails/ActorDetail";
import { LibraryContext, useVideoLibraryList } from "@/hooks/useVideoLibrary";

export const FAT_TV_PREFIX_PATH = '/filmAndTelevisionWall_tv';

const DefaultLayout: FC = ({ children }) => {
  return (
    <>
      <div className={styles["nasTV_sideBar"]}>
        <FilmSideBar />
      </div>
      <div className={styles.nasTV_content}>
        {children}
      </div>
    </>
  )
}

export const useLibraryListTV = () => useContext(LibraryContext);

const NasTV: FC = () => {

  const { path } = useRouteMatch();
  const { libraries, getLib, setLibraries } = useVideoLibraryList({ effectNotLoading: true, isTv: true });

  useEffect(() => {
    console.log('定时刷新媒体库列表');
    const intervalId = setInterval(() => {
      try {
        getLib(true);
      } catch (e) {
        console.log('定时刷新媒体库列表失败:', e && JSON.stringify(e));
      }
    }, 5 * 1000); // 每30秒刷新一次媒体库列表

    return () => {
      console.log('清除定时刷新媒体库列表');
      clearInterval(intervalId);
    }
  }, [getLib])

  return (
    <LibraryContext.Provider value={{ libs: libraries, setLibs: setLibraries, refreshLibraries: () => getLib(true) as any }}>
      <div className={styles["nasTv_container"]}>
        <Switch>
          <Route exact path={`${path}/allRecentlyPlay`}><AllRecentlyPlay /></Route>
          <Route exact path={`${path}/allRecentlyAdd`}><AllRecentlyAdd /></Route>
          <Route exact path={`${path}/search`}><SearchByTV /></Route>
          <Route exact path={`${path}/recentlyAdd`}>
            <DefaultLayout>
              <RecentlyAdd />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/all`}>
            <DefaultLayout>
              <All />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/library/:id`}>
            <DefaultLayout>
              <Library />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/collect`}>
            <DefaultLayout>
              <Collect />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/played`}>
            <DefaultLayout>
              <Played />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/dramasOrMovie`}>
            <DefaultLayout>
              <DramasOrMovie />
            </DefaultLayout>
          </Route>

          <Route exact path={`${path}/videoDetails`}>
            <VideoDetails />
          </Route>

          <Route exact path={`${path}/videoDetails/actorDetail`}>
            <ActorDetail />
          </Route>

          {/* 默认显示页面 */}
          <Route exact path={path}>
            <DefaultLayout>
              <RecentlyPlay />
            </DefaultLayout>
          </Route>

        </Switch>
      </div>
    </LibraryContext.Provider>
  );
}

export default NasTV;
