import React from 'react';
import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import emptyStateIcon from '@/Resources/icon/no-file.png';
import emptyStateIconDark from '@/Resources/icon/no-file-dark.png';
import { useTheme } from "@/utils/themeDetector";

interface IEmptyState {
  title?: string;
  description?: string;
  icon?: string;
  className?: string;
  children?: React.ReactNode;
}

const EmptyState: React.FC<IEmptyState> = ({ 
  title = '没有文件', 
  description,
  icon,
  className,
  children 
}) => {
  const { isDarkMode } = useTheme();

  return (
    <div className={`${styles.empty_state_container} ${className || ''}`}>
      <div className={styles.empty_state_icon}>
        <PreloadImage src={icon || isDarkMode ? emptyStateIconDark : emptyStateIcon} style={{width:'120px',height:'80px'}} alt="空状态图标" />
        <span className={styles.empty_state_title}>没有文件</span>
      </div>
      <div className={styles.empty_state_content}>
        {description && (
          <p className={styles.empty_state_description}>
            {description}
          </p>
        )}
        {children && (
          <div className={styles.empty_state_actions}>
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmptyState;
