import { Image, Grid, List, Button, Skeleton } from "antd-mobile";
import { useHistory, useRouteMatch } from "react-router-dom";
import { useRequest } from "ahooks";
import { getVideoRecord } from "@/api/ipc";
import { exitWebClient, goToIPCPreview } from "@/api/cameraPlayer";

import styles from "./index.module.scss";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import tip from "@/Resources/icon/tip.png";
import plus from "@/Resources/icon/plus.png";
import enterRight from "@/Resources/icon/enter_right.png";
import ai from "@/Resources/icon/ai.png";
import device from "@/Resources/icon/device.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import tipDark from "@/Resources/icon/tip-dark.png";
import plusDark from "@/Resources/icon/plus-dark.png";
import enterRightDark from "@/Resources/icon/enter_right_dark.png";
import aiDark from "@/Resources/icon/ai-dark.png";
import deviceDark from "@/Resources/icon/device-dark.png";
import plays from "@/Resources/icon/plays.png";
import deviceOffline from "@/Resources/icon/device-offline.png";
import emptyCamera from "@/Resources/icon/empty-camera.png";
import manager_error_img from "@/Resources/camera_poster/camera_manager_poster_pc.png";
import manager_error_img_dark from "@/Resources/camera_poster/camera_manager_poster_pc_dark.png";
import event_error_img_dark from "@/Resources/camera_poster/camera_manager_eventLookback_poster_dark.png";
import event_error_img from "@/Resources/camera_poster/camera_manager_eventLookback_poster.png";
import { useTheme } from "@/utils/themeDetector";
import { useCallback, useEffect, useMemo } from "react";
import { CameraInfo } from "@/api/ipc";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import { PreloadImage } from "@/components/Image";
import NavigatorBar from "@/components/NavBar";
import { InfoCameraAI } from "..";

// 模拟事件回看数据 - 作为默认数据
interface IPCHomeProps {
  cameraList: CameraInfo[];
  refreshCameraList: () => void;
  isFirst?: boolean;
  isDataComplete?: boolean; // 数据是否完整
  ensureCompleteData?: () => void; // 确保获取完整数据的方法
}

const IPCHome: React.FC<IPCHomeProps> = ({ cameraList, refreshCameraList, isFirst, isDataComplete, ensureCompleteData }) => {
  const { isDarkMode } = useTheme();
  const history = useHistory();
  const { path } = useRouteMatch();

  const { isHide } = InfoCameraAI(); // 是否隐藏摄像机AI信息

  // 获取事件回看数据
  const { data: rawEventData, loading: eventDataLoading } = useRequest(() =>
    getVideoRecord({
      page: {
        size: 20,
        token: "",
      },
      options: {
        option: ["event_name"],
        event_name: ["human", "fire", "pet", "move"],
      },
    }, { showLoading: false }),
    {
      cacheKey: 'event-replay-data', // 设置缓存key
      staleTime: Infinity, // 数据永不过期，除非手动清除缓存
      refreshOnWindowFocus: false, // 禁用窗口聚焦时自动刷新
    }
  );

  // 处理事件回看数据
  const eventReplayData = useMemo(() => {
    if (rawEventData && rawEventData.code === 0 && rawEventData.data?.videos) {
      // 处理视频数据，只取前三条
      return rawEventData.data.videos
        .slice(0, 3)
        .map((video: any, index: number) => ({
          id: index + 1,
          src: video.cover_file ? `${video.cover_file}/original.jpg` : "",
          time: new Date(video.time).toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          }),
        }));
    }
    return [];
  }, [rawEventData]);

  // 摄像机点击时进入实时
  const cameraOnClick = useCallback(async (camera: CameraInfo) => {
    // 改为对象形式传递，防止刷新丢失数据问题
    try {
      await goToIPCPreview(camera.did).catch((e) => {
        console.error("进入实时失败:", e && JSON.stringify(e));
      });
    } catch (e) {
      console.error("进入实时失败:", e && JSON.stringify(e));
    }
  }, []);

  // 处理返回按钮点击
  const handleBackClick = () => {
    exitWebClient().catch((error) => {
      console.error("退出Web客户端失败:", error);
    });
  };

  // 初始化时记录摄像机管理页面曝光一次
  useEffect(() => {
    window.onetrack?.('track', 'ipc_cameraManager_expose');
  }, [])

  // 摄像机骨架屏组件
  const CameraSkeleton = () => (
    <div className={styles.skeletonContainer}>
      <Grid columns={2} style={{ marginTop: 10, gap: 2 }}>
        {Array.from({ length: 2 }).map((_, index) => (
          <Grid.Item key={index}>
            <div style={{ aspectRatio: '16/9' }}>
              <Skeleton animated style={{ width: '100%', height: 110 }} />
            </div>
          </Grid.Item>
        ))}
      </Grid>
    </div>
  );

  // 事件回看骨架屏组件
  const EventSkeleton = () => (
    <div className={styles.eventSkeletonContainer}>
      <div className={styles.eventSkeletonHeader}>
        <div style={{ width: '80px', height: '20px' }}>
          <Skeleton animated style={{ width: '100%', height: '100%' }} />
        </div>
        <div style={{ width: '60px', height: '16px' }}>
          <Skeleton animated style={{ width: '100%', height: '100%' }} />
        </div>
      </div>
      <div className={styles.eventSkeletonImages}>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} style={{ flex: 1, aspectRatio: '16/9' }}>
            <Skeleton animated style={{ width: '100%', height: 72 }} />
          </div>
        ))}
      </div>
    </div>
  );

  const navBarRight = useMemo(() => {
    return (
      <div className={styles.iconContainer}>
        <PreloadImage
          className={styles.infoIcon}
          src={isDarkMode ? tipDark : tip}
          onClick={() => history.push(`${path}/supportInformation`)}
        />
        <PreloadImage
          className={styles.addIcon}
          src={isDarkMode ? plusDark : plus}
          onClick={() => history.push(`${path}/addDevice`)}
        />
      </div>
    )
  }, [history, isDarkMode, path])

  return (
    <div className={styles.cameraManagementContainer}>
      <NavigatorBar styles={{ background: 'var(--background-color)' }} backIcon={isDarkMode ? arrowLeftDark : arrowLeft}
        onBack={handleBackClick} right={navBarRight} />

      <div className={styles.title}>摄像机管理</div>

      <div className={styles.scrollContainer}>
        {eventDataLoading ? (
          <CameraSkeleton />
        ) : (
          <Grid columns={2} style={{ marginTop: 10, gap: 2 }}>
            {cameraList.map((camera) => {
              // 取主摄镜头
              const mainLens = camera.key_frame.find((k) => k.lens_id === 0);
              let imgSrc = mainLens?.frame || "";
              if (!camera.isOnline) {
                imgSrc = deviceOffline;
              } else if (mainLens?.psm) {
                imgSrc = emptyCamera;
              }
              return (
                <Grid.Item key={camera.did}>
                  <div
                    className={styles.cameraItem}
                    onClick={() => cameraOnClick(camera)}
                  >
                    {
                      isFirst ? (
                        <div style={{ aspectRatio: '16/9' }}>
                          <Skeleton animated style={{ height: "100%" }} />
                        </div>
                      ) : (
                        <PreloadImage
                          className={styles.cameraImage}
                          src={splitURL(imgSrc)}
                          needHeader={true}
                          errorImage={isDarkMode ? manager_error_img_dark : manager_error_img}
                          loadingType='Skeleton'
                        />
                      )
                    }
                  </div>
                </Grid.Item>
              );
            })}
          </Grid>
        )}

        {
          !isHide && (
            eventDataLoading ? (
              <EventSkeleton />
            ) : (
              <div className={styles.eventReplayContainer}>
                <List.Item className={styles.eventReplayItem} arrow={false}>
                  <div className={styles.all}>
                    <div className={styles.eventReplayTitle}>事件回看</div>
                    {
                      Array.isArray(eventReplayData) && eventReplayData.length > 0 && (
                        <div className={styles.viewAll}>
                          <div
                            className={styles.view}
                            onClick={() =>
                              history.push(`${path}/cameraDetail/eventLookBack`)
                            }>查看全部</div>
                          <Image
                            className={styles.viewIcon}
                            src={isDarkMode ? enterRightDark : enterRight}
                          />
                        </div>
                      )
                    }

                  </div>
                  <div className={styles.eventReplayImages}>
                    {Array.isArray(eventReplayData) && eventReplayData.length > 0 ? (
                      eventReplayData.map((event: any) => {
                        return (
                          <div key={event.id} className={styles.eventReplayImageItem}>
                            <PreloadImage
                              className={styles.eventReplayImage}
                              src={splitURL(event.src)}
                              needHeader={true}
                              style={{ width: "100%", height: "100%" }}
                              errorImage={isDarkMode ? event_error_img_dark : event_error_img}
                              loadingType="Skeleton"
                            />
                            <div className={styles.eventReplayTime}>
                              <Image src={plays} className={styles.playIcon} />
                              {event.time}
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className={styles.eventReplayEmpty}>
                        <div className={styles.emptyText}>暂无内容</div>
                      </div>
                    )}
                  </div>
                </List.Item>
              </div>
            )
          )
        }

        <div className={styles.storageManagementContainer}>
          <Button
            className={styles.storageManagementButton}
            onClick={() => history.push({
              pathname: `${path}/videoList`,
              state: {
                cameraList,
                isDataComplete,
                ensureCompleteData,
              },
            })}
          >
            <img
              src={isDarkMode ? deviceDark : device}
              alt=""
              className={styles.icon}
            />
            <span className={styles.text}>存储管理</span>
          </Button>
        </div>

        <div className={styles.functionButtonsContainer}>
          {!isHide && (<Button
            className={styles.aiButton}
            onClick={() => history.push(`${path}/faceRecognition`)}
          >
            <img
              src={isDarkMode ? aiDark : ai}
              alt=""
              className={styles.icon}
            />
            <span className={styles.text}>AI功能</span>
          </Button>)}
          <Button
            className={styles.deviceButton}
            onClick={() =>
              history.push({
                pathname: `${path}/storageManagement`,
                state: {
                  cameraList,
                  isDataComplete,
                  ensureCompleteData,
                },
              })
            }
          >
            <img
              src={isDarkMode ? deviceDark : device}
              alt=""
              className={styles.icon}
            />
            <span className={styles.text}>设备管理</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default IPCHome;
