import styles from './index.module.scss';
import { useMemo } from 'react';
import backIcon_light from '@/Resources/icon/backIcon_light.png';
import backIcon_dark from '@/Resources/icon/backIcon_dark.png';
import { PreloadImage } from '../Image';
import { Modal as AntdModal } from 'antd';
import { useTheme } from '@/utils/themeDetector';
export interface IModal {
  isShow: boolean
  onConfirm?: () => void
  onCancel: () => void
  content: React.ReactNode
  backIcon?: string
  left?: React.ReactNode
  right?: React.ReactNode
  title?: string
  contentStyle?: React.CSSProperties;
  footer?: React.ReactNode //不希望footer显示则传入null
  width?: string
  position?: 'normal' | 'top' | 'bottom' | 'center'
  destroyOnClose?: boolean
  zIndex?: number
}

const Modal = (props: IModal) => {
  const { isShow, content, onCancel, onConfirm, backIcon, left, right, title, contentStyle, footer, width, position = 'normal', destroyOnClose = true, zIndex } = props;
  const { isDarkMode } = useTheme();

  const renderHeader = useMemo(() => {
    return (
      <>
        {
          left || right || title || backIcon ? <div className={styles.modal_top}>
            <div className={styles.modal_top_close} onClick={onCancel}>
              <PreloadImage src={backIcon ? backIcon : isDarkMode ? backIcon_dark : backIcon_light} alt='back' />
            </div>
            <div className={styles.modal_top_left}>
              {left ? left : <></>}
            </div>
            <div className={styles.modal_top_title}>
              {title ? title : <></>}
            </div>
            <div className={styles.modal_top_right}>
              {right ? right : <></>}
            </div>
          </div> : <></>
        }
      </>
    )
  }, [backIcon, isDarkMode, left, onCancel, right, title])

  const renderContent = useMemo(() => {
    return (
      <div className={styles.modal_content} style={{ width: width, height: `calc(100% - ${left || right || title || backIcon ? '56px' : '0px'} - ${footer !== null ? '74px' : '0px'} - 48px)`, ...contentStyle }}>
        {content}
      </div>
    )
  }, [backIcon, content, contentStyle, footer, left, right, title, width])

  const renderFooter = useMemo(() => {
    return (
      <div className={styles.modal_footer} style={{ display: footer !== null ? 'flex' : 'none' }}>
        {
          footer ? footer : <>
            {onConfirm ? <div className={styles.modal_footer_btns_confirm} style={{ backgroundColor: 'rgba(50, 186, 192, 1)', color: "#FFF" }} onClick={onConfirm}>确认</div> : <></>}
            {onCancel ? <div className={styles.modal_footer_btns_cancel} style={{ backgroundColor: isDarkMode ? 'rgba(75, 75, 77, 1)' : 'rgba(0, 0, 0, 0.06)' }} onClick={onCancel}>取消</div> : <></>}
          </>
        }
      </div>
    )
  }, [footer, isDarkMode, onCancel, onConfirm])

  const useStyle = useMemo(() => {
    //modal行内样式
    return {
      header: {
        padding: left || right || title || backIcon ? '8px 0px' : '0px'
      },
      content: {
        // width: "100vw"
      }
    }
  }, [backIcon, left, right, title])

  return (
    <AntdModal title={renderHeader} open={isShow} onOk={onConfirm} onCancel={onCancel} footer={renderFooter} closable={false} centered={position === 'center' ? true : false}
      maskClosable={true} destroyOnClose={destroyOnClose} width={width} className={`${styles.modal_wrapper} ${styles[position]}`} styles={useStyle} zIndex={zIndex}>
      {renderContent}
    </AntdModal>
  )
}

export default Modal;