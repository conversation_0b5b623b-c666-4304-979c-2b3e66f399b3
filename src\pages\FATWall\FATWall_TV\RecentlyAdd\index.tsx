import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import TVFocusable, { FocusableElement } from '../TVFocus';
import { PreloadImage } from '@/components/Image';
import playIcon from '@/Resources/icon/play.png';
import collect_icon_tv from '@/Resources/icon/collect_icon_tv.png';
import collected_icon_tv from '@/Resources/icon/collected_icon_tv.png';
import notWatch_icon_tv from '@/Resources/icon/notWatch_icon_tv.png';
import watched_icon_tv from '@/Resources/icon/watched_icon_tv.png';
import { useHistory } from 'react-router-dom';
import { IDescribe, MovieCardTV } from '../optPages/RecentlyPlay';
import styles from '../optPages/index.module.scss';
import ErrorComponentTV from '../Error';
import { useLibraryListTV } from '..';
import { defaultPageParam } from '../../FATWall_APP/Recently';
import { collect, filmAndTvProps, getRecentlyAdd, markWatched, getMediaFiles, MediaFileResponse } from '@/api/fatWall';
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { Toast } from '@/components/Toast/manager';
import { formatTimeAgo } from '../../FATWall_APP/Recently/RecentlyPlay';
import { playVideoTV } from '@/api/fatWallJSBridge';
import placeholder‌_poster_big from '@/Resources/icon/placeholder‌_row_big.png';
import douBan_icon from '@/Resources/icon/douban_icon.png';

interface IRecentlyAddProps {
  score: string;
}

const RecentlyAdd = () => {
  const [acKey, setAcKey] = useState<number>(0);
  const [moveAcKey, setMoveAcKey] = useState<string>('');
  const scrollContainerRef = useRef<HTMLDivElement>(null); // 滚动容器引用
  const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParam);
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam);
  const [isError, setIsError] = useState<boolean>(false);
  const [addRs, setAddRs] = useState<filmAndTvProps>({ medias: [], count: 0 });
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [currentPlayItem, setCurrentPlayItem] = useState<any>(null); // 当前要播放的项目

  const desc_ref = useRef<HTMLDivElement>(null);
  const [descOverSize, setDescOverSize] = useState<boolean>(false); // 描述是否超出容器大小

  const loaderRef = useRef<HTMLDivElement>(null);

  const [loading, setLoading] = useState<boolean>(true); // 是否正在加载中，用于显示加载动画

  const history = useHistory();

  const libs = useLibraryListTV();

  const { runAsync } = useRequest(getRecentlyAdd, { manual: true }); // 获取最近添加的电影列表数据

  // 获取媒体文件列表的API调用
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res: { code: number; data: MediaFileResponse }) => {
        if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
          console.log('TV端RecentlyAdd获取到完整剧集列表:', res.data.files);
          handleStartPlay(res.data.files, currentPlayItem);
        }
      },
      onError: (error) => {
        console.error('TV端RecentlyAdd获取媒体文件列表失败:', error);
        Toast.show('获取剧集列表失败');
      },
    }
  );

  const initData = useCallback(async (callback: (v: filmAndTvProps) => void) => {
    setLoading(true);
    const res = await runAsync({ ...pageOptRef.current, tv: 1 }, { showLoading: false }).catch(e => { console.log(`获取最近观看的电影列表数据失败: ${e}`); setLoading(false) })
    if (res && res.code === 0 && res.data) {
      if (res.data.count < pageOptRef.current.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      callback(res.data);
      setIsError(false);
      setLoading(false)
      return;
    }
    setLoading(false);
    setIsError(true);
  }, [runAsync])

  // 初始化数据加载逻辑
  useEffect(() => {
    initData((v) => setAddRs(v));
  }, [initData])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      setPageOpt((prev) => {
        return { ...prev, offset: prev.offset + prev.limit }
      })
    }
  }, [inViewport])

  // 翻页逻辑
  useUpdateEffect(() => {
    pageOptRef.current = pageOpt;
    initData((v) => setAddRs(p => ({ ...p, medias: [...p.medias, ...v.medias], count: p.count + v.count })));
  }, [pageOpt, initData])

  // 重置
  const clearAndRefresh = useCallback(() => {
    setHasMore(false);
    // setAddRs({ medias: [], count: 0 }); // 不清空数据，以免影响下一次加载

    // 重置分页参数，重新加载数据
    setPageOpt(defaultPageParam);
    if (pageOptRef.current) {
      const { limit, offset } = pageOptRef.current;
      if (limit === defaultPageParam.limit && offset === defaultPageParam.offset) {
        initData((v) => setAddRs(v));
      }
    }
    pageOptRef.current = defaultPageParam;
  }, [initData])

  // 处理开始播放
  const handleStartPlay = useCallback((mediaFiles: any[], playItem: any) => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建TV端videoList数组，
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: playItem.media_id?.toString() || '0',
      file_id: file.file_id.toString(),
      total_time: file.duration || 0,
      filter: {
        last_play_point: file.last_play_point,
        audio_index: file.audio_index,
        seen: file.seen,
        subtitle_path: file.subtitle_path,
        subtitle_type: file.subtitle_type,
        subtitle_index: file.subtitle_index,
      }
    }));

    // 对于RecentlyAdd，默认播放第一集
    let playIndex = 0;
    console.log(`TV端RecentlyAdd播放：将播放第一集，索引位置：${playIndex}`);

    // 调用TV端视频播放接口
    playVideoTV(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show('开始播放');
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });

    // 清空当前播放项目状态
    setCurrentPlayItem(null);
  }, []);

  // 播放器关闭后的数据刷新方法
  const refVideoDetail = useCallback(() => {
    // 播放器关闭后刷新最近添加列表数据
    console.log('TV端RecentlyAdd页面播放器关闭后数据刷新');
    setTimeout(() => {
      clearAndRefresh();
    }, 500);
  }, [clearAndRefresh]);

  // 注册播放器关闭回调
  useEffect(() => {
    console.log('TV端RecentlyAdd页面注册播放器关闭回调');
    
    // 注册updateVideoCenter方法给HK
    window.hs_registerHandler?.('updateVideoCenter', refVideoDetail);
  }, [refVideoDetail]);

  const movieHistory: (Omit<IDescribe, 'movieDuration'> & IRecentlyAddProps)[] = useMemo(() => {
    return addRs.medias.map((media) => {
      const time = formatTimeAgo(media.create_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : placeholder‌_poster_big : placeholder‌_poster_big; // 数组0索引为封面图，1索引为海报
      return {
        ...media,
        name: media.trans_name, title: media.trans_name, flag: [`${media.year}`, `${media.kind.join(' ')}`, `${media.origin_place}`], score: media.score ? media.score !== 0 ? media.score.toString() : '暂无评分' : '暂无评分',
        percentage: `${media.last_seen_percent}`, favourite: media.favourite, seen: media.seen, time: time, describe: media.brief, backgroundImage: poster, url: poster
      }
    })
  }, [addRs.medias])

  // 处理播放按钮点击
  const handlePlayClick = useCallback(() => {
    const currentItem = movieHistory[acKey];
    if (!currentItem) {
      Toast.show('当前没有选中的项目');
      return;
    }

    console.log('TV端RecentlyAdd点击播放:', currentItem);

    if (!currentItem.media_id) {
      Toast.show('缺少媒体ID，无法播放');
      return;
    }

    // 调用接口获取完整的剧集列表
    runGetMediaFiles({
      lib_id: 0, // 根据需求设置为0
      media_id: currentItem.media_id
    });

    // 保存当前点击的项目信息，用于后续播放
    setCurrentPlayItem(currentItem);
  }, [movieHistory, acKey, runGetMediaFiles]);

  const getCurrentItem = useCallback((item: FocusableElement, e) => {
    setMoveAcKey(item.id);
  }, [])

  useUpdateEffect(() => {
    if (!scrollContainerRef.current || movieHistory.length === 0) return; // 确保引用已经初始化
    const scCont = scrollContainerRef.current;
    if (moveAcKey === `tv-id-recentlyAdd-${movieHistory[0].name}`) {
      // scCont.scrollLeft = 0; // 将滚动条位置设置为最左侧

      requestAnimationFrame(() => {
        scCont.scrollTo({
          left: 0,
          behavior: 'smooth'
        });
      });
    }
    if (moveAcKey === `tv-id-recentlyAdd-${movieHistory[movieHistory.length - 1].name}`) {
      // scCont.scrollLeft = 0; // 将滚动条位置设置为最左侧

      requestAnimationFrame(() => {
        scCont.scrollTo({
          left: scCont.scrollWidth - scCont.clientWidth,
          behavior: 'smooth'
        });
      });
    }
    if (moveAcKey === `tv-id-recentlyAdd-more-movie`) {
      // scCont.scrollLeft = scCont.scrollWidth - scCont.clientWidth; // 将滚动条位置设置为最右侧

      requestAnimationFrame(() => {
        scCont.scrollTo({
          left: scCont.scrollWidth - scCont.clientWidth,
          behavior: 'smooth'
        });
      });
    }
  }, [moveAcKey, movieHistory, movieHistory.length])

  const playedByMovie = useCallback(async (seen: number) => {
    if (movieHistory.length > 0 && movieHistory[acKey]) {
      const res = await markWatched({ media_ids: [movieHistory[acKey].media_id], seen: seen }, { showLoading: false });


      if (res && res.code === 0) {
        Toast.show(`${seen ? '已标记' : '取消标记'}观看`);
        clearAndRefresh();
      }
    }
  }, [acKey, clearAndRefresh, movieHistory])

  const likeByMovie = useCallback(async (favourite: number) => {
    if (movieHistory.length > 0 && movieHistory[acKey]) {
      const res = await collect({ media_ids: [movieHistory[acKey].media_id], favourite: favourite }, { showLoading: false });

      if (res && res.code === 0) {
        Toast.show(`${favourite ? '已标记' : '取消标记'}喜欢`);
        clearAndRefresh();
      }
    }
  }, [acKey, clearAndRefresh, movieHistory])

  // 点击更多进入详情页
  const getMoreDetail = useCallback(() => {
    const item: any = movieHistory[acKey];
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_tv/videoDetails?${params.toString()}`);
  }, [acKey, history, movieHistory])

  useEffect(() => {
    // 判断简介是否超出高度，如果超出则显示更多按钮
    if (desc_ref && desc_ref.current) {
      const { offsetHeight } = desc_ref.current;
      const clone = desc_ref.current.cloneNode(true) as HTMLDivElement;
      clone.style.height = 'auto';
      clone.style.visibility = 'hidden';
      clone.style.position = 'absolute';
      document.body.appendChild(clone);

      if (clone.offsetHeight > offsetHeight) {
        setDescOverSize(true);
      } else {
        setDescOverSize(false);
      }

      document.body.removeChild(clone);
    }
  }, [movieHistory, acKey])



  return (
    <ErrorComponentTV loading={loading} isError={isError} hasContent={movieHistory.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
      text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无最近添加内容'}
      subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
      {
        movieHistory[acKey] ? (
          <div className={styles['nasTV_index_container']}>
            <div className={styles.nasTV_mask}></div>
            <PreloadImage src={movieHistory[acKey].backgroundImage} alt='backgroundImage' className={styles.nasTV_background_image} />
            <div className={styles['nasTV_index_desc']}>
              <div className={styles['nasTV_index_desc_title']}>
                {movieHistory[acKey].title}
              </div>

              {/* flag */}
              <div className={styles.nasTV_index_desc_flag_container}>
                <div className={styles.nasTV_index_desc_flag_score}>
                  <div className={styles.nasTV_index_desc_flag_add_item}>
                    <div className={styles.nasTV_index_desc_flag_score_item}>
                      {movieHistory[acKey].score !== '暂无评分' ? <PreloadImage src={douBan_icon} alt='豆瓣评分' /> : <></>}
                      {movieHistory[acKey].score !== '暂无评分' ? Number(movieHistory[acKey].score).toFixed(1) : movieHistory[acKey].score}
                    </div>
                  </div>
                  {movieHistory[acKey].flag.filter(it => it && it !== '0' && it !== '').length > 0 && <div className={styles.nasTV_index_desc_flag_gap}><div /></div>}
                </div>
                {
                  movieHistory[acKey].flag.filter(it => it && it !== '0' && it !== '').map((it, index) => (
                    <div key={it} className={styles.nasTV_index_desc_flag_add_container}>
                      <div className={styles.nasTV_index_desc_flag_add_item}>
                        {it}
                      </div>
                      {index !== movieHistory[acKey].flag.filter(it => it && it !== '0' && it !== '').length - 1 && <div className={styles.nasTV_index_desc_flag_gap}><div /></div>}
                    </div>
                  ))
                }
              </div>

              <div ref={desc_ref} className={styles['nasTV_index_desc_describe']}>
                {movieHistory[acKey].describe}
              </div>
              {descOverSize && <TVFocusable id={`tv-id-recentlyAdd-more`} row={0} col={1} className={styles.nasTV_index_desc_describe_more} onClick={getMoreDetail}>更多</TVFocusable>}

              {/* 播放、标记、已收藏 */}
              <div className={styles.nasTV_index_desc_btns}>
                <TVFocusable id={`tv-id-recentlyAdd-play`} row={1} col={1} className={styles.nasTV_index_btns_play_item} onClick={handlePlayClick}>
                  <PreloadImage src={playIcon} alt='play' />
                  播放
                </TVFocusable>
                <TVFocusable id={`tv-id-recentlyAdd-like`} row={1} col={2} className={styles.nasTV_index_btns_item} onClick={() => likeByMovie(movieHistory[acKey].favourite ? 0 : 1)}>
                  <PreloadImage src={movieHistory[acKey].favourite ? collected_icon_tv : collect_icon_tv} alt='like' />
                  <span>{`${movieHistory[acKey].favourite ? '已收藏' : '收藏'}`}</span>
                </TVFocusable>
                <TVFocusable id={`tv-id-recentlyAdd-played`} row={1} col={3} className={styles.nasTV_index_btns_item} onClick={() => playedByMovie(movieHistory[acKey].seen ? 0 : 1)}>
                  <PreloadImage src={movieHistory[acKey].seen ? watched_icon_tv : notWatch_icon_tv} alt='played' />
                  <span>{`${movieHistory[acKey].seen ? '已观看' : '未观看'}`}</span>
                </TVFocusable>
              </div>
            </div>

            {/* 下方影视展示栏 */}

            <div className={styles['nasTV_index_desc_recentlyWatched']}>
              {/* <span className={styles['nasTV_index_desc_recentlyWatched_text']}>最近观看</span> */}
              <div className={styles['nasTV_index_desc_recentlyWatched_list']} ref={scrollContainerRef}>
                {
                  movieHistory.map((item, index) => {
                    return (
                      <MovieCardTV key={index} type='add' getCurrentItem={getCurrentItem} focusableId={`tv-id-recentlyAddList-${item.name}-${index}`} focusableRow={2} focusableCol={index} callback={() => setAcKey(index)} cover={item.url} playTime={item.percentage} movieTitle={item.title} time={item.time} />
                    )
                  })
                }
                {
                  hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
                }
                {
                  !hasMore && <div className={styles.nasTV_index_desc_recentlyWatched_more_item_container}>
                    <TVFocusable id={`tv-id-recentlyAddMore-more-movie`} currentItemCallback={getCurrentItem} row={2} col={movieHistory.length + 1} className={styles.nasTV_index_desc_recentlyWatched_more_item_focus_container} onClick={() => history.push(`/filmAndTelevisionWall_tv/allRecentlyAdd`)}>
                      <div className={styles.nasTV_index_desc_recentlyWatched_more_item}>查看全部</div>
                    </TVFocusable>
                  </div>
                }
              </div>
            </div>

          </div>
        ) : <></>
      }
    </ErrorComponentTV>
  )
}

export default RecentlyAdd;