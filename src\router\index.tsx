import { HomePage } from '../pages/FATWall/FATWall_APP/index';

import React, { lazy } from "react";

// 图片加载
import AIFunctionality from '@/Resources/icon/AIFunctionality.png';
import AIFunctionality_dark from '@/Resources/icon/AIFunctionality_dark.png';
import eventLookBack from '@/Resources/icon/eventLookBack.png';
import eventLookBack_dark from '@/Resources/icon/eventLookBack_dark.png';
import multipleChannels from '@/Resources/icon/multipleChannels.png';
import multipleChannels_dark from '@/Resources/icon/multipleChannels_dark.png';
import storageManager from '@/Resources/icon/storageManager.png';
import storageManager_dark from '@/Resources/icon/storageManager_dark.png';
import all_icon from '@/Resources/icon/all_icon.png';
import all_icon_dark from '@/Resources/icon/all_icon_dark.png';
import collect_icon from '@/Resources/icon/collect_icon.png';
import collect_icon_dark from '@/Resources/icon/collect_icon_dark.png';
import played_icon from '@/Resources/icon/played_icon.png';
import played_icon_dark from '@/Resources/icon/played_icon_dark.png';
import file_light from '@/Resources/icon/file_light.png';
import file_dark from '@/Resources/icon/file_dark.png';
import download_light from '@/Resources/icon/download_light.png';
import download_dark from '@/Resources/icon/download_dark.png';
import upload_light from '@/Resources/icon/upload_light.png';
import upload_dark from '@/Resources/icon/upload_dark.png';
import task_light from '@/Resources/icon/task_light.png';
import task_dark from '@/Resources/icon/task_dark.png';
import vip_icon from '@/Resources/icon/vip_icon.png';

interface IChildrenRouter {
  label?: string; // 侧边栏菜单label
  key?: string; // 唯一key
  icon?: { //明暗模式下的侧边栏icon
    light: string;
    dark: string;
  }
  show?: boolean // 控制菜单项是否显示，默认为true
  showHeader?: boolean // 控制页面是否显示header，默认为true
  right_icon?: string
}
export interface IRouter {
  path: string;
  exact: boolean;
  component: React.ComponentType<any>;
  children?: (IRouter & IChildrenRouter)[]; // 第一级路由区分功能模块，第二级路由区分pc侧菜单(app设置菜单相关config不生效),第三级路由有需求控制也显示菜单
}

type AppType = 'IPC' | 'NasDisk' | 'FAT_Wall' | 'ComponentCard' | 'MacBackup' | 'HubGateway';
type DeviceType = 'mobile' | 'pc' | 'tv'

// 自动导入器
const componentLoader = (deviceType: DeviceType, name: string, appType: AppType = 'IPC', FcType?: Omit<AppType, 'ComponentCard'>) => {
  // 根据应用类型和设备类型去不同的路径下取组件

  if (appType === 'IPC') {
    switch (deviceType) {
      case "mobile": return lazy(() => import(`@/pages/IPC/IPC_APP/${name}`));
      case "pc": return lazy(() => import(`@/pages/IPC/IPC_PC/${name}`));
      default: return lazy(() => import(`@/pages/IPC/IPC_APP/${name}`));
    }
  } else if (appType === 'NasDisk') {
    switch (deviceType) {
      case "mobile": return lazy(() => import(`@/pages/NasDisk/NasDisk_APP/${name}`));
      case "pc": return lazy(() => import(`@/pages/NasDisk/NasDisk_PC/${name}`));
      default: return lazy(() => import(`@/pages/NasDisk/NasDisk_APP/${name}`));
    }
  } else if (appType === 'FAT_Wall') {
    switch (deviceType) {
      case "mobile": return lazy(() => import(`@/pages/FATWall/FATWall_APP/${name}`));
      case "pc": return lazy(() => import(`@/pages/FATWall/FATWall_PC/${name}`));
      case "tv": return lazy(() => import(`@/pages/FATWall/FATWall_TV/${name}`));
      default: return lazy(() => import(`@/pages/FATWall/FATWall_APP/${name}`));
    }
  } else if (appType === 'ComponentCard') {
    if (!FcType) {
      return lazy(() => import(`@/pages/ComponentCard`));
    }
    switch (deviceType) {
      case "mobile": return lazy(() => import(`@/pages/ComponentCard/${FcType}/APP/${name}`));
      case "pc": return lazy(() => import(`@/pages/ComponentCard/${FcType}/PC/${name}`));
      case "tv": return lazy(() => import(`@/pages/ComponentCard/${FcType}/TV/${name}`));
      default: return lazy(() => import(`@/pages/ComponentCard/${FcType}/APP/${name}`));
    }
  } else if (appType === 'MacBackup') {
    switch (deviceType) {
      case "mobile": return lazy(() => import(`@/pages/MacBackup/MacBackup_APP/${name}`));
      case "pc": return lazy(() => import(`@/pages/MacBackup/MacBackup_PC/${name}`));
      default: return lazy(() => import(`@/pages/MacBackup/MacBackup_APP/${name}`));
    }
  } else if (appType === 'HubGateway') {
    switch (deviceType) {
      case "mobile": return lazy(() => import(`@/pages/HubGateway/HubGateway_APP/${name}`));
      case "pc": return lazy(() => import(`@/pages/HubGateway/HubGateway_PC/${name}`));
      default: return lazy(() => import(`@/pages/HubGateway/HubGateway_APP/${name}`));
    }
  }

  // 默认返回
  return lazy(() => import(`@/pages/IPC/IPC_APP/${name}`));
}


const routers: IRouter[] = [
  // 默认初始化
  //{ path: '/', exact: true, component: lazy(() => import("@/pages/IPC")) },
  {
    path: '/test',
    exact: true,
    component: lazy(() => import('@/pages/Test')),
    children: [
      {
        path: '',
        exact: true,
        component: lazy(() => import('@/pages/Test')),
        icon: {
          light: multipleChannels,
          dark: multipleChannels_dark
        },
        key: 'multipleChannels',
        label: '测试1'
      }
    ]
  },
  // 摄像机管理app
  {
    path: '/cameraManagement_app',
    exact: false,
    component: componentLoader('mobile', ''),
    children: [
      {
        path: "/supportInformation",
        exact: true,
        component: componentLoader('mobile', 'SupportInformation'),
      },
      {
        path: "/addDevice",
        exact: false,
        component: componentLoader('mobile', 'AddDevice'),
        children: [
          {
            path: "/localRecord",
            exact: true,
            component: componentLoader('mobile', 'AddDevice/LocalRecord'), //子路由下的子路由组件名需要带上父文件夹
          },
        ],
      },
      {
        path: "/storageManagement",
        exact: false,
        component: componentLoader('mobile', 'StorageManagement'),
        children: [
          {
            path: "/detail/:id",
            exact: true,
            component: componentLoader('mobile', 'StorageManagement/DeviceDetail'),
          },
          {
            path: "/recordConfig",
            exact: true,
            component: componentLoader('mobile', 'StorageManagement/RecordConfig'),
          },
          {
            path: "/detectEvent",
            exact: true,
            component: componentLoader('mobile', 'StorageManagement/DetectEvent'),
          },
          {
            path: "/recordPlan",
            exact: true,
            component: componentLoader('mobile', 'StorageManagement/RecordPlan'),
          },
          {
            path: "/customize",
            exact: true,
            component: componentLoader('mobile', 'StorageManagement/Customize'),
          }
        ],
      },
      {
        path: "/faceRecognition",
        exact: false,
        component: componentLoader('mobile', 'FaceRecognition'),
        children: [
          {
            path: "/identify",
            exact: true,
            component: componentLoader('mobile', 'FaceRecognition/Identify'),
          },
          {
            path: "/faceManagement",
            exact: true,
            component: componentLoader('mobile', 'FaceRecognition/FaceManagement'),
          },
          {
            path: "/faceDetail/:id",
            exact: true,
            component: componentLoader('mobile', 'FaceRecognition/FaceDetail'),
          },
        ],
      },
      {
        path: "/cameraDetail",
        exact: false,
        component: componentLoader('mobile', 'CameraDetail'),
        children: [
          {
            path: "/lookBackDetail",
            exact: true,
            component: componentLoader('mobile', 'CameraDetail/EventLookBack'),
          },
          // {
          //   path: "/eventLookBack",
          //   exact: true,
          //   component: componentLoader('mobile', 'CameraDetail/LookBackDetail'),
          // }
        ],
      },
      {
        path: "/videoList",
        exact: true,
        component: componentLoader('mobile', 'VideoList'),
      }
    ]
  },

  // 摄像机管理pc
  {
    path: '/cameraManagement_pc',
    exact: false,
    component: componentLoader('pc', ''),
    children: [
      {
        path: '',
        exact: true,
        component: componentLoader('pc', ''),
        icon: {
          light: multipleChannels,
          dark: multipleChannels_dark
        },
        key: 'multipleChannels',
        label: '多路同频'
      },
      {
        path: '/eventLookBack',
        exact: true,
        component: componentLoader('pc', 'EventLookBack'),
        icon: {
          light: eventLookBack,
          dark: eventLookBack_dark
        },
        key: 'eventLookBack',
        label: '事件回看'
      },
      {
        path: '/faceRecognition',
        exact: true,
        component: componentLoader('pc', 'FaceRecognition'),
        children: [
          {
            path: "/faceManagement",
            exact: true,
            component: componentLoader('pc', 'FaceRecognition/FaceManagement'),
          },
        ],
        icon: {
          light: AIFunctionality,
          dark: AIFunctionality_dark
        },
        key: 'AIFunctionality',
        label: 'AI功能'
      },
      {
        path: '/videoList',
        exact: true,
        component: componentLoader('pc', ''),
        icon: {
          light: storageManager,
          dark: storageManager_dark
        },
        key: 'storageManager',
        label: '储存管理'
      },
    ]
  },

  // 影视墙app
  {
    path: '/filmAndTelevisionWall_app',
    exact: false,
    component: componentLoader('mobile', '', 'FAT_Wall'),
    children: [
      {
        path: '/recently',
        exact: true,
        component: () => HomePage({ path: 'recently' }),
      },
      {
        path: '/all',
        exact: true,
        component: () => HomePage({ path: 'all' }),
      },
      {
        path: '/search',
        exact: true,
        component: componentLoader('mobile', 'Search', 'FAT_Wall'),
      },
      {
        path: '/allLibrary',
        exact: true,
        component: componentLoader('mobile', 'All/AllLibrary', 'FAT_Wall')
      },
      {
        path: '/library',
        exact: true,
        component: componentLoader('mobile', 'All/Library', 'FAT_Wall')
      },
      {
        path: '/libraryManagement',
        exact: true,
        component: componentLoader('mobile', 'All/LibraryManagement', 'FAT_Wall'),
      },
      {
        path: "/createLibrary",
        exact: true,
        component: componentLoader('mobile', 'All/LibraryManagement/CreateLibrary', 'FAT_Wall'),
      },
      {
        path: '/all/videoDetails',
        exact: true,
        component: componentLoader('mobile', 'All/VideoDetails', 'FAT_Wall')
      },
      {
        path: '/all/videoDetails/singleEpisode',
        exact: true,
        component: componentLoader('mobile', 'All/VideoDetails/SingleEpisode', 'FAT_Wall')
      },
      {
        path: '/all/videoDetails/actorDetails',
        exact: true,
        component: componentLoader('mobile', 'All/VideoDetails/ActorDetails', 'FAT_Wall')
      },
      {
        path: '/recently/recentlyPlay',
        exact: true,
        component: componentLoader('mobile', 'Recently/RecentlyPlay', 'FAT_Wall')
      },
      {
        path: '/recently/recentlyAdd',
        exact: true,
        component: componentLoader('mobile', 'Recently/RecentlyAdd', 'FAT_Wall')
      }
    ]
  },

  // 影视墙pc
  {
    path: '/filmAndTelevisionWall_pc',
    exact: false,
    component: componentLoader('pc', '', 'FAT_Wall'),
    children: [
      {
        path: '/recently',
        key: 'recently',
        label: '最近',
        exact: false,
        component: () => {
          const { Redirect } = require('react-router-dom');
          return <Redirect to="/filmAndTelevisionWall_pc/recently/recentlyPlay" />;
        },
        icon: {
          light: multipleChannels,
          dark: multipleChannels_dark
        },
        children: [
          {
            path: '/recentlyPlay',
            exact: true,
            component: componentLoader('pc', 'Recently/RecentlyPlay', 'FAT_Wall'),
            key: 'recentlyPlay',
            label: '最近观看',
            show: true
          },
          {
            path: '/recentlyAdd',
            exact: true,
            component: componentLoader('pc', 'Recently/RecentlyAdd', 'FAT_Wall'),
            key: 'recentlyAdd',
            label: '最近添加',
            show: true
          },
        ]
      },
      {
        path: '/all',
        key: 'all',
        label: '全部',
        exact: false,
        component: componentLoader('pc', 'All', 'FAT_Wall'),
        icon: {
          light: all_icon,
          dark: all_icon_dark
        },
        children: [
          {
            path: '/videoDetails',
            key: 'videoDetailsPage',
            label: '影片详情页',
            exact: true,
            component: componentLoader('pc', 'All/VideoDetails', 'FAT_Wall'),
            icon: {
              light: played_icon,
              dark: played_icon_dark
            },
            show: false, // 设置为false就不会显示在菜单中
            showHeader: false // 不显示header
          },
          {
            path: '/dramasOrMovie',
            key: 'dramasOrMovie',
            label: '电视剧或电影',
            exact: true,
            component: componentLoader('pc', 'All/DramasOrMovie', 'FAT_Wall'),
            show: false, // 设置为false就不会显示在菜单中
            showHeader: false // 不显示header
          },
        ]

      },
      {
        path: '/collect',
        key: 'collect',
        label: '收藏',
        exact: true,
        component: componentLoader('pc', 'Collect', 'FAT_Wall'),
        icon: {
          light: collect_icon,
          dark: collect_icon_dark
        }
      },
      {
        path: '/played',
        key: 'played',
        label: '已观看',
        exact: true,
        component: componentLoader('pc', 'Played', 'FAT_Wall'),
        icon: {
          light: played_icon,
          dark: played_icon_dark
        }
      },


    ]
  },

  // 影视墙tv
  {
    path: '/filmAndTelevisionWall_tv',
    exact: false,
    component: componentLoader('tv', '', 'FAT_Wall'),
    children: [
      {
        path: '/allRecentlyPlay',
        exact: true,
        component: componentLoader('tv', 'AllRecentlyPlay', 'FAT_Wall'),
      },
      {
        path: '/recentlyAdd',
        exact: true,
        component: componentLoader('tv', 'RecentlyAdd', 'FAT_Wall'),
        children: [
          {
            path: '/allRecentlyAdd',
            exact: true,
            component: componentLoader('tv', 'RecentlyAdd/AllRecentlyAdd', 'FAT_Wall'),
          }
        ]
      },
      {
        path: '/all',
        exact: true,
        component: componentLoader('tv', 'optPages/All', 'FAT_Wall'),
      },
      {
        path: '/collect',
        exact: true,
        component: componentLoader('tv', 'Collect', 'FAT_Wall'),
      },
      {
        path: '/played',
        exact: true,
        component: componentLoader('tv', 'Played', 'FAT_Wall'),
      },
      {
        path: '/videoDetails',
        key: 'videoDetailsPageTV',
        label: 'TV影片详情页',
        exact: true,
        component: componentLoader('tv', 'VideoDetails', 'FAT_Wall'),
        show: false, // 设置为false就不会显示在菜单中
        showHeader: false // 不显示header
      },
    ]
  },

  // mac备份app
  {
    path: '/macBackup_app',
    exact: false,
    component: componentLoader('mobile', '', 'MacBackup')
  },

  // mac备份pc
  {
    path: '/macBackup_pc',
    exact: false,
    component: componentLoader('pc', '', 'MacBackup'),
    children: [
      {
        path: '',
        exact: true,
        component: componentLoader('pc', '', 'MacBackup'),
        icon: {
          light: multipleChannels,
          dark: multipleChannels_dark
        },
        key: 'macBackup',
        label: 'Mac备份助手'
      },
    ]
  },
  //中枢网关app
  {
    path: '/hubGateway_app',
    exact: false,
    component: componentLoader('mobile', '', 'HubGateway'),
    children: [
      {
        path: '/help',
        exact: true,
        component: componentLoader('mobile', 'HubGateway_APP/Help', 'HubGateway'),
        key: 'help',
        label: '中枢网关'
      },
    ]
  },
  //中枢网关pc
  {
    path: '/hubGateway_pc',
    exact: false,
    component: componentLoader('pc', '', 'HubGateway'),
    children: [
      {
        path: '/autojk',
        exact: true,
        key: 'autojk',
        component: componentLoader('pc', 'AutoJK', 'HubGateway'),
        icon: {
          light: multipleChannels,
          dark: multipleChannels_dark
        },
        label: '自动化极客版'
      },
    ]
  },

  // 各应用小组件的路由配置
  {
    path: '/componentCard',
    exact: false,
    component: componentLoader('mobile', '', 'ComponentCard'),
    children: [
      {
        path: '/nasdisk_app',
        exact: true,
        component: componentLoader('mobile', '', 'ComponentCard', 'NasDisk')
      },
      {
        path: '/nasdisk_pc',
        exact: true,
        component: componentLoader('pc', '', 'ComponentCard', 'NasDisk')
      },
    ]
  },

  //百度网盘app
  {
    path: '/baiduNetdisk_app',
    exact: false,
    component: componentLoader('mobile', '', 'NasDisk'),
    children: [
      {
        path: '/members',
        exact: false,
        component: componentLoader('mobile', 'Members', 'NasDisk'),
        children: [
          {
            path: '/counter',
            exact: true,
            component: componentLoader('mobile', 'Members/Counter', 'NasDisk'),
          },
        ]
      },
      {
        path: '/synchronization',
        exact: false,
        component: componentLoader('mobile', 'Synchronization', 'NasDisk'),
      },
      {
        path: '/TaskManager',
        exact: true,
        component: componentLoader('mobile', 'TaskManager', 'NasDisk'),
      },
      {
        path: '/selectFolder',
        exact: true,
        component: componentLoader('mobile', 'ScheduledDownload/SelectFolder', 'NasDisk'),
      },
      {
        path: '/fileUpload',
        exact: true,
        component: componentLoader('mobile', 'FileUpload', 'NasDisk'),
      },
      {
        path: '/uploadBD',
        exact: true,
        component: componentLoader('mobile', 'UploadBD', 'NasDisk'),
      },
    ]
  },

  //百度网盘pc
  {
    path: '/baiduNetdisk_pc',
    exact: false,
    component: componentLoader('pc', '', 'NasDisk'),
    children: [
      {
        path: '/bdfiles',
        exact: true,
        key: 'bdfiles',
        label: '网盘文件',
        icon: {
          light: file_light,
          dark: file_dark
        },
        // showHeader: false, // 不显示header
        component: componentLoader('pc', 'BDfiles', 'NasDisk'),
      }, {
        path: '/scheduledDownload',
        exact: true,
        key: 'scheduledDownload',
        label: '自动下载',
        icon: {
          light: download_light,
          dark: download_dark
        },

        component: componentLoader('pc', 'ScheduledDownload', 'NasDisk'),
      },
      {
        path: '/synchronization',
        exact: true,
        key: 'synchronization',
        label: '自动上传',
        icon: {
          light: upload_light,
          dark: upload_dark
        },
        right_icon: vip_icon,
        component: componentLoader('pc', 'Synchronization', 'NasDisk'),
      },
      {
        path: '/taskManager',
        exact: true,
        key: 'taskManager',
        label: '任务管理',
        icon: {
          light: task_light,
          dark: task_dark
        },
        component: componentLoader('pc', 'TaskManager', 'NasDisk'),
        children: [
          {
            path: '/downloadNas',
            exact: true,
            key: 'downloadNas',
            label: '下载至存储',
            show: true,
            component: componentLoader('pc', 'TaskManager/DownloadNas', 'NasDisk'),
          },
          {
            path: '/uploadBD',
            exact: true,
            key: 'uploadBD',
            label: '上传至网盘',
            show: true,
            component: componentLoader('pc', 'TaskManager/UploadBD', 'NasDisk'),
          }
        ]
      },
      {
        path: '/mine',
        exact: true,
        key: 'mine',
        label: '个人中心',
        component: componentLoader('pc', 'Mine', 'NasDisk'),
        show: false // 不在侧边栏显示，通过点击用户信息访问
      },
    ]
  }

]

export default routers;
