import { Form, FormInstance, InputNumber, Select } from "antd"
import clear_icon from '@/Resources/icon/close.png';
import clear_icon_dark from '@/Resources/icon/close_white.png';
import { useTheme } from "@/utils/themeDetector";
import { PreloadImage } from "@/components/Image";
import styles from './index.module.scss';
import { useEffect, useRef } from "react";
import type { InputNumberRef } from 'rc-input-number';

interface INumberInput {
  form: FormInstance<any>
}

const NumberInput = (props: INumberInput) => {
  const { form } = props;
  const { isDarkMode } = useTheme();
  const ref = useRef<InputNumberRef>(null);

  useEffect(() => {
    if (ref.current) {
      ref.current.focus({ cursor: 'all' });
    }
  }, [])

  return (
    <Form form={form} initialValues={{ size: 0, unit: 'GB' }} layout="inline" style={{ maxWidth: 'none' }} className={styles.form_container}>
      <Form.Item name={'size'} className={styles.number_input_container}>
        <InputNumber max={9999} ref={ref} autoComplete="off" type="tel" pattern="[0-9]*" inputMode="numeric" autoFocus={true}
          suffix={!isDarkMode ? <PreloadImage className={styles.number_input_img} src={clear_icon} alt="clear" />
            : <PreloadImage className={styles.number_input_img} src={clear_icon_dark} alt="clear" />} />
      </Form.Item>
      <Form.Item name={'unit'}>
        <Select>
          <Select.Option value="GB">GB</Select.Option>
          <Select.Option value="TB">TB</Select.Option>
          {/* <Select.Option value="MB">MB</Select.Option> */}
        </Select>
      </Form.Item>
    </Form>
  )
}

export default NumberInput;