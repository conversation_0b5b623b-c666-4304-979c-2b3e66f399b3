import React, { createContext, useContext, useState, useEffect, ReactNode, useRef, useCallback } from 'react';
import { getBaiduUserInfo } from '../api/nasDisk';

// 用户信息接口
interface UserInfo {
  netdisk_name: string;
  baidu_name: string;
  used_space: number;
  total_space: number;
  vip_type: number;
  nas_vip: number;
  avatar_url: string;
  uk: number;
  [key: string]: any;
  token: string
}

// Context状态接口
interface UserContextType {
  userInfo: UserInfo | null;
  setUserInfo: React.Dispatch<React.SetStateAction<UserInfo | null>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  error: string | null;
  fetchUserInfo: () => Promise<void>;
  clearUserInfo: () => void;
}

// 创建Context
const UserContext = createContext<UserContextType | undefined>(undefined);

// 格式化存储空间
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取VIP状态
const getVipStatus = (vip_type: number, nas_vip: number): string => {
  if (nas_vip === 1) return 'NAS VIP';
  if (vip_type > 0) return 'SVIP';
  return '普通用户';
};

// Provider组件
export const UserProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fetchingRef = useRef(false); // 防止重复调用的标志位
  const retryCountRef = useRef(0); // 重试次数计数器
  const maxRetryCount = 4; // 最大重试次数

  // 从localStorage加载用户信息
  useEffect(() => {
    const savedUserInfo = localStorage.getItem('userInfo');
    if (savedUserInfo) {
      try {
        setUserInfo(JSON.parse(savedUserInfo));
      } catch (e) {
        console.error('Failed to parse saved user info:', e);
        localStorage.removeItem('userInfo');
      }
    }
    // 初始化时重置请求标志
    fetchingRef.current = false;
  }, []);

  // 获取用户信息
  const fetchUserInfo = useCallback(async () => {
    // 如果已经有用户信息或正在请求中，直接返回
    if (userInfo || fetchingRef.current) {
      return;
    }

    fetchingRef.current = true;
    // 静默更新，不显示UI层面的loading
    // setIsLoading(true);
    setError(null);

    try {
      const response = await getBaiduUserInfo({
        action: 'uinfo'
      });

      // 检查响应的code字段
      if (response.code === 8) {
        // 失败，检查是否需要重试
        if (retryCountRef.current < maxRetryCount) {
          retryCountRef.current += 1;
          console.log(`获取用户信息失败 (code: 8)，第 ${retryCountRef.current} 次重试...`);

          // 重置状态并重试
          fetchingRef.current = false;
          setTimeout(() => {
            fetchUserInfo();
          }, 1000); // 延迟1秒后重试
          return;
        } else {
          // 达到最大重试次数
          console.error('获取用户信息失败，已达到最大重试次数');
          setError('获取用户信息失败，请稍后重试');
          retryCountRef.current = 0; // 重置重试计数
        }
      } else {
        // code 不等于 8，表示成功，停止调用
        console.log(`获取用户信息成功 (code: ${response.code})`);
        retryCountRef.current = 0; // 重置重试计数

        // 映射字段名，与bd.tsx中保持一致
        const userData = {
          netdisk_name: response.netdisk_name,
          baidu_name: response.baidu_name,
          used_space: response.used,
          total_space: response.total,
          vip_type: response.vip_type,
          nas_vip: response.nas_vip,
          avatar_url: response.avatar_url,
          uk: response.uk,
          end_time: response.end_time,
          token: response.token
        };
        setUserInfo(userData);

        // 保存到localStorage
        localStorage.setItem('userInfo', JSON.stringify(userData));
      }
    } catch (err) {
      // 网络错误，也进行重试
      if (retryCountRef.current < maxRetryCount) {
        retryCountRef.current += 1;
        console.log(`网络错误，第 ${retryCountRef.current} 次重试...`);

        // 重置状态并重试
        fetchingRef.current = false;
        setTimeout(() => {
          fetchUserInfo();
        }, 1000); // 延迟1秒后重试
        return;
      } else {
        // 达到最大重试次数
        setError('网络错误，请稍后重试');
        console.error('Failed to fetch user info:', err);
        retryCountRef.current = 0; // 重置重试计数
      }
    } finally {
      // 静默更新，不显示UI层面的loading
      // setIsLoading(false);
      fetchingRef.current = false;
    }
  }, [userInfo, maxRetryCount]); // 依赖于userInfo和maxRetryCount

  // 清除用户信息
  const clearUserInfo = useCallback(() => {
    setUserInfo(null);
    setError(null);
    fetchingRef.current = false; // 重置请求标志
    retryCountRef.current = 0; // 重置重试计数
    localStorage.removeItem('userInfo');
  }, []);

  const value: UserContextType = {
    userInfo,
    setUserInfo,
    isLoading,
    setIsLoading,
    error,
    fetchUserInfo,
    clearUserInfo
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

// 自定义hook
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// 导出辅助函数
export { formatBytes, getVipStatus };
