import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import styles from './index.module.scss';
import FilmFilter from "@/components/FATWall_TV/FilmFilter";
import { defaultFiltersByTV, filterTypeList } from "@/components/FATWall_APP/FATWALL_CONST";
import FilmCard, { IFilmCard } from "@/components/FATWall_PC/FilmCard";
import { useHistory } from "react-router-dom";
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { getMediaListFromLib, mediaProps } from "@/api/fatWall";
import ErrorComponentTV from "../Error";
import { useLibraryListTV } from "..";

export interface IFilter {
  name: string;
  typeList: {
    label: string;
    value: string;
  }[];
}

export const handleFilterByTV = (filter: { [key: string]: any }) => {
  let f = { ...filter };

  // 处理收藏、已观看筛选条件
  switch (filter.collect) {
    case 'all-collect': {
      delete f.seen;
      delete f.favourite;
      break;
    }
    case 'collect': {
      f.favourite = 1;
      break;
    }
    case 'viewed': {
      f.seen = 1;
      break;
    }
  }
  delete f.collect;

  // 处理年份筛选条件
  switch (filter.year) {
    case 'all-year': break;
    case '1990s': {
      f.year_start = 1990;
      f.year_end = 1999;
      break;
    }
    case '1980s': {
      f.year_start = 1980;
      f.year_end = 1989;
      break;
    }
    case '2000s': {
      f.year_start = 2000;
      f.year_end = 2009;
      break;
    }
    case '2010s': {
      f.year_start = 2010;
      f.year_end = 2019;
      break;
    }
    case 'other-year': {
      f.year_start = null;
      f.year_end = 1980;
      break;
    }
    default: {
      // const [end, start] = filter.year.split('-');
      // f.year_start = parseInt(start);
      // f.year_end = parseInt(end);
      f.year_start = parseInt(filter.year);
      f.year_end = parseInt(filter.year);
      break;
    }
  }
  delete f.year;

  // 处理影片范围筛选条件
  switch (filter.classes) {
    case 'all-classes': delete f.classes; break;
    case 'tv': f.classes = '电视剧'; break;
    case 'movie': f.classes = '电影'; break;
  }

  let newFilter: { [key: string]: any } = { ...f };
  Object.keys(f).forEach((key) => {
    const item = filterTypeList.find((it) => it.type === key);
    if (item) {
      const it = item.typeList.find((it) => it.key === f[key]);
      newFilter[key] = it ? it.title : '全部';
    }
  })

  Object.keys(newFilter).forEach((key) => {
    if (newFilter[key] === '全部' || newFilter[key] === undefined) {
      delete newFilter[key];
    }
  })

  if (newFilter['kind']) {
    newFilter['kind'] = [newFilter['kind']];
  }

  if (newFilter.sort) {
    switch (newFilter.sort) {
      case 'latest-add': newFilter.sort_type = 0; newFilter.asc = 0; break;
      case 'high-score': newFilter.sort_type = 1; newFilter.asc = 0; break;
    }
  }

  delete newFilter.sort;

  return newFilter;
}

export const defaultPageParamTV = { offset: 0, limit: 20 };

const All = () => {
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByTV);
  const history = useHistory();

  // 获取影视列表数据
  const [medias, setMedias] = useState<(mediaProps)[]>([]);
  const { runAsync } = useRequest(getMediaListFromLib, { manual: true });
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParamTV); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByTV); // 记录之前的筛选条件，用于判断筛选条件是否变化

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(true);

  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮
  const libs = useLibraryListTV();

  const [loading, setLoading] = useState<boolean>(true);

  const fetchMedias = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    setLoading(true);
    const res = await runAsync({ lib_id: 0, filter: { ...pageOptRef.current, ...filter, tv: 1 } }, { showLoading: false })
      .catch((e) => { console.log('获取媒体库影视列表失败：', e); setLoading(false); })
    if (res && res.code === 0 && res.data) {
      if (res.data.count < pageOptRef.current.limit) setHasMore(false);
      callback(res.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      setLoading(false);
      return;
    }
    setLoading(false);
    setIsError(true);
  }, [runAsync])

  // 初始化数据
  useEffect(() => {
    fetchMedias((data) => setMedias(data), handleFilterByTV(defaultFiltersByTV));
  }, [fetchMedias])

  const filmList: IFilmCard[] = useMemo(() => {
    return medias.map((item) => {
      return {
        title: item.trans_name,
        url: item.poster.length > 0 ? item.poster[0] : '',
        name: item.trans_name || item.origin_name || item.other_name,
        score: item.score ? item.score.toFixed(1).toString() : '暂无评分',
        isDrama: false,
        favourite: item.favourite === 1,
      }
    })
  }, [medias]);

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      fetchMedias((data) => setMedias(p => [...p, ...data]), handleFilterByTV(filters));
    }
  }, [inViewport, fetchMedias])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilterByTV(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(true);
    pageOptRef.current = defaultPageParamTV; // 重置页数
    fetchMedias((data) => setMedias(data), filter);
  }, [fetchMedias])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    getDataByFilters(filters);
  }, [filters, getDataByFilters])

  // 筛选条件重置和刷新按钮逻辑
  const clearAndRefresh = useCallback(() => {
    setFilters(defaultFiltersByTV); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByTV)) {
        getDataByFilters(defaultFiltersByTV);
      }
    }
  }, [getDataByFilters])

  const toMovieOrDrama = useCallback((item: mediaProps, isDrama: boolean) => {
    if (isDrama) {
      history.push(`/filmAndTelevisionWall_tv/dramasOrMovie`);
    } else {
      // 参照PC版传递URL参数

      const params = new URLSearchParams({
        classes: item.classes || '',
        media_id: item.media_id?.toString() || '0',
        lib_id: '0'
      });

      history.push(`/filmAndTelevisionWall_tv/videoDetails?${params.toString()}`);
    }
  }, [history])

  return (
    <div className={styles["nasTV_optPages_container"]}>
      <FilmFilter setFilters={setFilters} filters={filters} />
      <ErrorComponentTV customizeRow={10} loading={loading} isError={isError} hasContent={filmList.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
        text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无内容'} subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
        <div className={styles.nasTV_optPages_content}>
          {
            filmList.map((item, index) => {
              // 找到对应的媒体数据
              const mediaItem = medias[index];
              return (
                <FilmCard key={`${item.name}_${index}`} {...item} row={Math.floor(index / 5) + Object.keys(filters).length} col={index % 5} id={`tv-id-all-${item.name}-${index}`}
                  onClick={() => toMovieOrDrama(mediaItem, item.isDrama)} />
              );
            })
          }

          {
            hasMore && filmList.length >= pageOptRef.current.limit && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
          }
        </div>
      </ErrorComponentTV>
    </div>
  );
}

export default All;
