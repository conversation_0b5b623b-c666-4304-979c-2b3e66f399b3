.container {
  width: 100%;
  max-width: 100vw;
  // min-width: unset;
  height: 100%;
  position: relative;
  color: white;
  background-color: #000;
  /* 允许内部元素滚动，但容器本身不滚动 */
  overflow: auto;
  /* 确保容器占满整个屏幕 */
  margin: 0;
  padding: 0;
  left: 0;
  top: 0;
}

.videoBackground {
  width: 100%;
  height: 100%;
  position: absolute;

  top: 0;
  left: 0;
  z-index: 1;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.9));
  }
}

.backButton {
  position: fixed;
  z-index: 10;
  font-size: 24px;
  margin-bottom: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}

.scrollableContent {
  position: relative;
  z-index: 2;
  padding: 80px;
  // height: 100%;
  /* 使用全屏高度 */
  width: 100%;
  /* 使用全屏宽度 */
  // margin-top: 150px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
  /* 确保内容不会超出屏幕边界 */
  box-sizing: border-box;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
}

/* 当为剧集视图时的布局 */
.dramaScrollableContent {
  position: absolute;
  /* 使用fixed定位确保相对于视口 */
  top: 30%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100% !important;
  max-width: 100vw !important;
  min-width: unset !important;
  margin-top: 0;
  max-height: unset;
  height: auto;
  // background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8) 100px, rgba(0, 0, 0, 0.9));
  border-radius: 16px 16px 0 0;
  padding-top: 40px;
  box-sizing: border-box;
}

.title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  // padding-top: 100px;
}

.infoRow {
  display: flex;
  align-items: center;
  gap: 10px;
  // margin-bottom: 15px;
  font-size: 20px;
  color: #D6D8D9
}

.rating {
  background-color: #fff;
  font-size: 14px;
  border: 2px solid #07B551;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: MiSans;
  text-align: center;
  border-radius: 5px;
  height: 22px;
  padding: 10px 5px;
  // width: 38px;
  color: #07B551;
}

.tag {
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #fff;
  background-color: rgba(255, 255, 255, 0.1);
  font-size: 12px;
}

.versionSelector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 15px 0;
}


    :global(.ant-select-item-option-content){
    font-size: 14px !important;
  }
.versionSelect {
// ant-select-item  ant-select-item-option-active
// ant-select-item ant-select-item-option


  :global(.ant-select-selector) {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
  }

  :global(.ant-select-selection-item) {
    color: white !important;
  }

  :global(.ant-select-arrow) {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  &:hover :global(.ant-select-selector) {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }
}

.descriptionContainer {
  margin: 20px 0;
  position: relative;
  max-width: 50%;
}

.description {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  max-height: 4.8em; // 限制高度为3行
  display: -webkit-box;
  -webkit-line-clamp: 3; // 限制为3行
  -webkit-box-orient: vertical;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: 1000px;
    -webkit-line-clamp: unset;
  }
}

.fullDescription {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
}

.limitedDescription {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
}

.toggleButton {
  color: #1890FF;
  cursor: pointer;
  display: inline-block;
  margin-left: 8px;
  user-select: none;

  &:hover {
    text-decoration: underline;
  }
}

.buttons {
  display: flex;
  gap: 15px;
  margin: 20px 0;
}

.primaryButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 80px;
  width: 130px;
  // padding: 10px 20px;
  background-color: #0070f3;
  border-radius: 16px;
  font-size: 16px;
  font-weight: bold;
}

.secondaryButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 20%;
  background-color: rgba(255, 255, 255, 0.1);
}

.castSection {
  margin: 20px 0;
}

.sectionTitle {
  font-size: 14px;
  margin-bottom: 15px;
  color: #CDCECE
}



.castList {
  display: flex;
  gap: 15px;
  overflow-x: auto;
  padding-bottom: 10px;
  
}
/* 针对 WebKit 内核浏览器的样式 */
.castList::-webkit-scrollbar {
  display: block !important;
  height: 5px !important; /* 主要控制水平滚动条高度 */
}
.castList::-webkit-scrollbar-track {
  background-color: transparent !important;
  border-radius: 4px !important;
}
.castList::-webkit-scrollbar-thumb {
  background-color: #888 !important;
  border-radius: 4px !important;
  /* 如果你想调整thumb的视觉宽度，可以使用border和background-clip */
  /* border: 2px solid transparent; */
  /* background-clip: content-box; */
}

/* 针对支持标准属性的浏览器 (如 Firefox) */
@supports (scrollbar-width: thin) {
  .castList {
    scrollbar-width: thin;
    scrollbar-color: #888 transparent; /* 滑块颜色 轨道颜色 */
  }
}
.castItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
  cursor: pointer;
  transition: transform 0.2s ease, opacity 0.2s ease;

  &:hover {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

.castAvatar {
  width: 62px;
  height: 62px;
  border-radius: 50%;
  margin-bottom: 8px;
  object-fit: cover;
}

.castName {
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.castRole {
  font-size: 10px;
  opacity: 0.7;
  text-align: center;
}

.fileInfo {
  margin-top: 20px;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 15px;
  border-radius: 8px;
}

.fileInfoItem {
  font-size: 16px;
  margin-bottom: 5px;
word-break: break-all;
  opacity: 0.7;
}

/* 下载弹窗样式 */
.downloadModal {
  :global(.ant-modal-content) {
    background-color: #FFFFFF;
    color: white;
    border-radius: 30px;
    height: 240px;
    width: 400px;
    padding: 0;
  }

  :global(.ant-modal-header) {
    background-color: #1f1f1f;
    // border-bottom: 1px solid #333;

    :global(.ant-modal-title) {
      color: white;
      font-weight: bold;
    }
  }

  :global(.ant-modal-close) {
    color: white;
  }

  :global(.ant-modal-body) {
    padding: 24px;

    p {
      color: #000;
      font-size: 16px;
      // text-align: center;
    }
  }

  :global(.ant-modal-footer) {
    // border-top: 1px solid #333;
    // padding: 16px 24px;
    border-radius: 30px;
    display: flex;
    // padding-bottom: 50px;
    justify-content: center;
  }
}

.downloadModalBtn {
  background-color: #F0F0F0;
  color: #000;
  border: none;
  border-radius: 4px;
  // padding: 8px 24px;
  font-size: 16px;
  width: 80%;
  margin-bottom: 50px;
  height: 60px;
  border-radius: 12px;
  transition: background-color 0.3s;

  &:hover {
    // background-color: #40a9ff;
  }
}

/* 更多菜单样式 */
.moreMenu {
  min-width: 140px;
  // background-color: #1f1f1f;
  border-radius: 4px;
  padding: 1px 0;
}

.moreMenuItem {
  padding: 8px 16px;
  font-size: 14px;
  color: #000;
  cursor: pointer;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* 剧集列表区域样式 */
.dramaInfo {
  margin: 30px 0;
}

.modal_button {
  width: 100%;
  height: 50px;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  border-radius: 16px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: var(--text-color);
  cursor: pointer;

  &:hover {
    background-color: #fff;
    color: #4096ff;
  }
}