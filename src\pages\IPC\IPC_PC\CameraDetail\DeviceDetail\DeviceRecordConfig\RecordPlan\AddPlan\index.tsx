import { useEffect, useState } from "react";
import styles from "./index.module.scss";
import TimePicker from "@/components/TimePicker";
import { List, Checkbox, CenterPopup } from "antd-mobile";
import picker from "@/Resources/icon/picker.png";
import { Form, FormInstance } from "antd";

const AddRecordPlan = (props: { form: FormInstance<any> }) => {
  const [startVisible, setStartVisible] = useState(false);
  const [endVisible, setEndVisible] = useState(false);
  const [repeatVisible, setRepeatVisible] = useState(false);
  const [startTime, setStartTime] = useState(["00", "00"]);
  const [endTime, setEndTime] = useState(["00", "00"]);
  const [repeatMode, setRepeatMode] = useState("每天");
  const [selectedRepeatMode, setSelectedRepeatMode] = useState(repeatMode);
  const [customDaysVisible, setCustomDaysVisible] = useState(false);
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const { form } = props;

  useEffect(() => {
    if (startTime) {
      form.setFieldValue('startTime', startTime);
    }
    if (endTime) {
      form.setFieldValue('endTime', endTime);
    }
    if (repeatMode) {
      form.setFieldValue('repeatMode', repeatMode);
    }
  }, [endTime, form, repeatMode, startTime])

  // 重复模式选项
  const repeatOptions = [
    { label: "执行一次", value: "执行一次" },
    { label: "每天", value: "每天" },
    { label: "法定工作日", value: "法定工作日" },
    { label: "法定节假日", value: "法定节假日" },
    { label: "自定义", value: "自定义" },
  ];

  const weekDays = [
    { label: "周一", value: "1" },
    { label: "周二", value: "2" },
    { label: "周三", value: "3" },
    { label: "周四", value: "4" },
    { label: "周五", value: "5" },
    { label: "周六", value: "6" },
    { label: "周日", value: "7" },
  ];

  const handleRepeatOptionClick = (value: string) => {
    setSelectedRepeatMode(value);
    if (value === "自定义") {
      setCustomDaysVisible(true);
      setRepeatVisible(false);
    }
  };

  // 将选中的日期转换为显示文本
  const getSelectedDaysText = () => {
    if (selectedDays.length === 7) return "每天";
    if (selectedDays.length === 0) return "自定义";
    return selectedDays
      .map((day) => weekDays.find((d) => d.value === day)?.label)
      .join("、");
  };

  const handleStartTimeConfirm = (time: string[]) => {
    setStartTime(time);
  };

  const handleEndTimeConfirm = (time: string[]) => {
    setEndTime(time);
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <List className={styles.settingList}>
          <div className={styles.listAll}>
            <List.Item
              className={styles.settingItem}
              prefix={<span className={styles.label}>开始时间</span>}
              extra={
                <div
                  className={styles.valueContainer}
                  onClick={() => setStartVisible(true)}
                >
                  <span
                    className={styles.value}
                  >{`${startTime[0]}:${startTime[1]}`}</span>
                </div>
              }
              arrow={true}
            />
            <List.Item
              className={styles.settingItem}
              prefix={<span className={styles.label}>结束时间</span>}
              extra={
                <div
                  className={styles.valueContainer}
                  onClick={() => setEndVisible(true)}
                >
                  <span
                    className={styles.value}
                  >{`${endTime[0]}:${endTime[1]}`}</span>
                </div>
              }
              arrow={true}
            />
          </div>
        </List>
        <div className={styles.thinLine} style={{ marginTop: 16 }} />
        <List className={styles.listBox}>
          <List.Item
            className={styles.settingItem}
            prefix={<span className={styles.label}>重复</span>}
            extra={
              <div className={styles.valueContainer}>
                <span className={styles.value}>{repeatMode}</span>
              </div>
            }
            onClick={() => {
              setSelectedRepeatMode(repeatMode);
              setRepeatVisible(true);
            }}
          />
        </List>
      </div>
      {/* 开始时间选择器 */}
      <Form.Item name={'startTime'} noStyle>
        <TimePicker
          visible={startVisible}
          onClose={() => setStartVisible(false)}
          value={startTime}
          onConfirm={handleStartTimeConfirm}
          title="选择开始时间"
        />
      </Form.Item>

      {/* 结束时间选择器 */}
      <Form.Item name={'endTime'} noStyle>
        <TimePicker
          visible={endVisible}
          onClose={() => setEndVisible(false)}
          value={endTime}
          onConfirm={handleEndTimeConfirm}
          title="选择结束时间"
        />
      </Form.Item>

      {/* 重复模式选择弹窗 */}
      <Form.Item name={'repeatMode'} noStyle>
        <CenterPopup
          visible={repeatVisible}
          onMaskClick={() => setRepeatVisible(false)}
          // position="bottom"
          bodyStyle={{
            borderRadius: "16px",
            overflow: "hidden",
            borderTop: "none",
          }}
          getContainer={null}
        >
          <div className={styles.repeatPopup}>
            <div className={styles.popupHeader}>
              <div className={styles.popupTitle}>重复</div>
            </div>
            <div className={styles.repeatOptions}>
              {repeatOptions.map((option) => (
                <div
                  key={option.value}
                  className={styles.repeatOption}
                  data-selected={selectedRepeatMode === option.value}
                  onClick={() => handleRepeatOptionClick(option.value)}
                >
                  <div className={styles.optionContent}>
                    <div className={styles.iconContainer}>
                      {selectedRepeatMode === option.value && (
                        <img
                          className={styles.checkedIcon}
                          alt=""
                          src={picker}
                        />
                      )}
                    </div>
                    <span className={styles.optionText}>{option.label}</span>
                  </div>
                </div>
              ))}
            </div>
            <div className={styles.popupFooter}>
              <div
                className={styles.popupButton}
                onClick={() => setRepeatVisible(false)}
              >
                取消
              </div>
              <div
                className={`${styles.popupButton} ${styles.primary}`}
                onClick={() => {
                  setRepeatMode(selectedRepeatMode);
                  setRepeatVisible(false);
                }}
              >
                确定
              </div>
            </div>
          </div>
        </CenterPopup>
      </Form.Item>

      {/* 自定义重复日期弹窗 */}
      <CenterPopup
        visible={customDaysVisible}
        onMaskClick={() => setCustomDaysVisible(false)}
        // position="bottom"
        bodyStyle={{
          borderRadius: "16px",
          overflow: "hidden",
          borderTop: "none",
        }}
        getContainer={null}
      >
        <div className={styles.repeatPopup}>
          <div className={styles.popupHeader}>
            <div className={styles.popupTitle}>自定义</div>
          </div>
          <div className={styles.repeatOptions}>
            {weekDays.map((day) => (
              <div
                key={day.value}
                className={styles.repeatOption}
                style={{ marginTop: 0 }}
                onClick={() => {
                  const isSelected = selectedDays.includes(day.value);
                  const newSelectedDays = isSelected
                    ? selectedDays.filter((d) => d !== day.value)
                    : [...selectedDays, day.value];
                  setSelectedDays(newSelectedDays);
                }}
              >
                <div className={styles.optionContent}>
                  <span className={styles.optionText}>{day.label}</span>
                  <Checkbox checked={selectedDays.includes(day.value)} />
                </div>
              </div>
            ))}
          </div>
          <div className={styles.popupFooter}>
            <div
              className={styles.popupButton}
              onClick={() => setCustomDaysVisible(false)}
            >
              取消
            </div>
            <div
              className={`${styles.popupButton} ${styles.primary}`}
              onClick={() => {
                setRepeatMode(getSelectedDaysText());
                setCustomDaysVisible(false);
              }}
            >
              确定
            </div>
          </div>
        </div>
      </CenterPopup>
    </div>
  );
};

export default AddRecordPlan;