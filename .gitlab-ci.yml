# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
variables:
  KUBERNETES_CPU_REQUEST: 4000m
  KUBERNETES_CPU_LIMIT: 4000m
  KUBERNETES_MEMORY_REQUEST: 8Gi
  KUBERNETES_MEMORY_LIMIT: 8Gi
  CI: "false"

image: micr.cloud.mioffice.cn/devx-build-image/nodejs:16-centos7.9-base   # 使用 Node.js 16 版本

stages:          # List of stages for jobs, and their order of execution
  - build
  - test
  - deploy

# 全局规则：仅允许手动触发
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"  # 仅通过网页/API手动触发时运行
      when: always
    - when: never  # 其他情况（如 push、merge 等）不触发

build-job:       # This job runs in the build stage, which runs first.
  stage: build
  image: micr.cloud.mioffice.cn/devx-build-image/nodejs:16-centos7.9-base # 使用 Node.js 16 版本的镜像
  script:
    - export DISABLE_ESLINT_PLUGIN=true
    - echo "Installing dependencies..."
    - npm install --legacy-peer-deps  # 或者使用 yarn install
    - echo "Compiling the code..."
    - npm run build # 或者使用 yarn build
  artifacts:
    paths:
      - build/  # 确保构建输出目录被保存为构件

unit-test-job:   # This job runs in the test stage.
  stage: test    # It only starts when the job in the build stage completes successfully.
  script:
    - echo "Running unit tests... This will take about 60 seconds."
    - sleep 60
    - echo "Code coverage is 90%"

lint-test-job:   # This job also runs in the test stage.
  stage: test    # It can run at the same time as unit-test-job (in parallel).
  script:
    - echo "Linting code... This will take about 10 seconds."
    - sleep 10
    - echo "No lint issues found."

deploy-job:      # This job runs in the deploy stage.
  stage: deploy  # It only runs when *both* jobs in the test stage complete successfully.
  script:
    - echo "Deploying application..."
    - echo "Application successfully deployed."
