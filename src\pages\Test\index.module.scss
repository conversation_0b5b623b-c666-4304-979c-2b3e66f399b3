.container {
  width: 100%;
  height: 100%;
  font-size: 12px;
  overflow: hidden;
}

.timeline_container {
  width: 100%;
  height: 100px;
  position: relative;
  background-color: #f3f3f3;
}

.timeline_blockGrid {
  height: 100%;
  transition: transform 0.01s ease-out;
  will-change: transform;
  user-select: none;
}

.timeline2_blockGrid {
  height: 100%;
  user-select: none;
  display: flex;
  overflow-x: auto;
}

.timeline_indicator {
  position: absolute;
  width: 2px;
  height: 30%;
  left: 50%;
  bottom: 0;
  background-color: black;
  transform: translateX(-50%);
}

.timeline_block {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
}

.timeline2_block {
  height: 100%;
  display: flex;
  align-items: flex-start;
}

.timeline_grid {
  display: flex;
  flex-direction: column;
  position: absolute;

  span {
    transform: translateX(-50%);
  }

  .major {
    height: 60%;
    background: #94a3b8;
  }
}

.timeline2_grid {
  display: flex;
  flex-direction: column;

  span {
    transform: translateX(-50%);
    text-align: center;
  }

  .major {
    height: 60%;
    background: #94a3b8;
  }
}

.timeline_marker {
  width: 1px;
  height: 10px;
  background: #eb300b;
  flex-grow: 1;
}
