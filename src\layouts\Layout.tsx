import { PreloadImage } from "@/components/Image";
import { Divider, Layout } from "antd"
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react";
import collapsedIcon from "@/Resources/icon/sideBarIcon.png";
import collapsedIcon_dark from "@/Resources/icon/sideBarIcon_dark.png";
import { SIDE_BAR_WIDTH } from "@/components/CameraPlayer/constants";
import styles from "./Layout.module.scss";
import { useTheme } from "@/utils/themeDetector";
import { useHistory, useLocation } from "react-router-dom";
import useMenus, { IMenus } from "@/router/menus";
import routers from "@/router";

// 任务通知Context
interface TaskNotificationContextType {
  uploadNotificationCount: number;
  downloadNotificationCount: number;
  addUploadNotification: () => void;
  addDownloadNotification: () => void;
  clearUploadNotifications: () => void;
  clearDownloadNotifications: () => void;
}

const TaskNotificationContext = createContext<TaskNotificationContextType>({
  uploadNotificationCount: 0,
  downloadNotificationCount: 0,
  addUploadNotification: () => { },
  addDownloadNotification: () => { },
  clearUploadNotifications: () => { },
  clearDownloadNotifications: () => { },
});

export const useTaskNotification = () => useContext(TaskNotificationContext);

const { Sider, Content } = Layout;

export interface ICollapsePanel {
  label: string
  name: string
  icon: string
  key: string
}



const sideBarChangeContext = createContext<{
  setSidePanel: React.Dispatch<React.SetStateAction<React.ReactNode>>
}>({ setSidePanel: () => null });

export const useSideBarChange = () => useContext(sideBarChangeContext);

const CustomLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [collapsed, setCollapsed] = useState<boolean>(false);
  const menus = useMenus();
  const history = useHistory();
  const { isDarkMode } = useTheme();
  const location = useLocation<{ title?: string }>();

  const [sidePanel, setSidePanel] = useState<React.ReactNode>(<></>); // 侧边栏折叠面板

  // 任务通知状态
  const [uploadNotificationCount, setUploadNotificationCount] = useState<number>(0);
  const [downloadNotificationCount, setDownloadNotificationCount] = useState<number>(0);

  // 添加上传通知
  const addUploadNotification = useCallback(() => {
    setUploadNotificationCount(prev => prev + 1);
  }, []);

  // 添加下载通知
  const addDownloadNotification = useCallback(() => {
    setDownloadNotificationCount(prev => prev + 1);
  }, []);

  // 清除上传通知
  const clearUploadNotifications = useCallback(() => {
    setUploadNotificationCount(0);
  }, []);

  // 清除下载通知
  const clearDownloadNotifications = useCallback(() => {
    setDownloadNotificationCount(0);
  }, []);

  // 从路由配置中递归查找页面标题
  const findRouteTitle = useMemo(() => {
    return (routes: any[], path: string): string => {
      for (const route of routes) {
        // 检查当前路由
        if (route.path === path || `/${route.path}` === path) {
          return route.label || '页面详情';
        }

        // 检查子路由
        if (route.children && route.children.length > 0) {
          const childTitle = findRouteTitle(route.children, path);
          if (childTitle) return childTitle;

          // 检查组合路径
          for (const child of route.children) {
            const combinedPath = `${route.path}${child.path}`;
            if (combinedPath === path || `/${combinedPath}` === path) {
              return child.label || '页面详情';
            }
          }
        }
      }
      return '';
    };
  }, []);

  // 从路由配置中查找是否显示header
  const shouldShowHeader = useMemo(() => {
    // 递归查找当前路由配置
    const findRouteShowHeader = (routes: any[], path: string): boolean => {
      for (const route of routes) {
        // 检查当前路由
        if (route.path === path || `/${route.path}` === path) {
          // 如果明确设置了showHeader为false，则返回false
          return route.showHeader !== false;
        }

        // 检查子路由
        if (route.children && route.children.length > 0) {
          for (const child of route.children) {
            // 检查直接子路由
            if (child.path === path || `/${child.path}` === path) {
              return child.showHeader !== false;
            }

            // 检查组合路径
            const combinedPath = `${route.path}${child.path}`;
            if (combinedPath === path || `/${combinedPath}` === path) {
              return child.showHeader !== false;
            }

            // 检查更深层的子路由
            if (child.children && child.children.length > 0) {
              for (const grandChild of child.children) {
                const deepCombinedPath = `${route.path}${child.path}${grandChild.path}`;
                if (deepCombinedPath === path || `/${deepCombinedPath}` === path) {
                  return grandChild.showHeader !== false;
                }
              }
            }
          }
        }
      }

      // 默认显示header
      return true;
    };

    return findRouteShowHeader(routers, history.location.pathname);
  }, [history.location.pathname]);

  // 判断是否在VideoDetails页面（需要全屏显示）
  const isVideoDetailsPage = useMemo(() => {
    const pathname = history.location.pathname;
    return pathname.includes('/videoDetails') || pathname.includes('/video-details');
  }, [history.location.pathname]);

  // 检测是否在root容器内
  const [isInRootContainer, setIsInRootContainer] = useState(false);

  useEffect(() => {
    const checkRootContainer = () => {
      const rootContainer = document.querySelector('div#root');
      const isInContainer = !!rootContainer;
      console.log('检测div#root:', isInContainer, rootContainer);
      setIsInRootContainer(isInContainer);
    };

    // 立即检查
    checkRootContainer();

    // 延迟检查，确保DOM完全加载
    const timer = setTimeout(checkRootContainer, 100);

    return () => clearTimeout(timer);
  }, []);

  // 获取当前页面标题
  const pageTitle = useMemo(() => {
    // 首先检查location.state中是否有标题
    if (location.state && location.state.title) {
      return location.state.title;
    }

    // 尝试从菜单中查找
    const menuItem = menus.find((item) => item.path === history.location.pathname);
    if (menuItem?.label) {
      return menuItem.label;
    }

    // 尝试从路由配置中查找
    const routeTitle = findRouteTitle(routers, history.location.pathname);
    if (routeTitle) {
      return routeTitle;
    }

    // 如果还是没有找到，返回默认值
    return '页面详情';
  }, [menus, history.location.pathname, location.state, findRouteTitle]);

  const handleMenuClick = useCallback((key: string) => {
    const item: IMenus | undefined = menus.find((it) => it.key === key);
    if (item) {
      // 根据菜单key清除对应的通知
      if (key === 'uploadBD') {
        clearUploadNotifications();
      } else if (key === 'downloadNas') {
        clearDownloadNotifications();
      }
      history.push(item.path);
    }
  }, [history, menus, clearUploadNotifications, clearDownloadNotifications])

  return (
    <div className={`${styles.layout_container} ${collapsed ? styles.retract : ''} ${isVideoDetailsPage ? (isInRootContainer ? styles.fullscreenInContainer : styles.fullscreen) : ''}`}>
      <Layout hasSider={!isVideoDetailsPage}>
        {/* 只在非VideoDetails页面显示侧边栏 */}
        {!isVideoDetailsPage && (
          <Sider className={styles.layout_sider_container} width={SIDE_BAR_WIDTH} collapsible collapsed={collapsed} trigger={null}>

            <div className={styles.layout_sider_header}>
              <PreloadImage onClick={() => setCollapsed(prev => !prev)} src={isDarkMode ? collapsedIcon_dark : collapsedIcon} alt="collapsed" />
            </div>

            <div className={styles.layout_sider_content}>
              {/* 菜单栏选项 */}

              <div className={styles.layout_sider_menus}>
                {
                  menus.map((item: IMenus) => (
                    <div key={item.key} className={`${styles.layout_sider_menus_item} ${item.path === history.location.pathname ? styles.focus : ''}`} onClick={() => handleMenuClick(item.key)}>
                      {
                        item.icon && item.icon !== '' && <PreloadImage src={item.icon} alt="menus_icon" />
                      }
                      <div className={styles.menu_label_container}>
                        <div className={styles.menu_label_container_label} style={{ paddingLeft: item.icon && item.icon !== '' ? '' : '38px' }}>
                          {item.label}
                          {/* 上传至网盘菜单显示上传通知红点 */}
                          {item.key === 'uploadBD' && uploadNotificationCount > 0 && (
                            <span className={styles.notification_dot}></span>
                          )}
                          {/* 下载至存储菜单显示下载通知红点 */}
                          {item.key === 'downloadNas' && downloadNotificationCount > 0 && (
                            <span className={styles.notification_dot}></span>
                          )}
                        </div>
                        {item.right_icon && (
                          <PreloadImage src={item.right_icon} alt="right_icon" />
                        )}
                      </div>
                    </div>
                  ))
                }
              </div>

              <Divider />

              {/* 侧边栏下方组件显示 */}
              <div className={styles.layout_sider_sidePanel}>
                {sidePanel}
              </div>
            </div>

          </Sider>
        )}

        {/* 内容标题 */}

        <Content className={`${styles.layout_content_container} ${isVideoDetailsPage ? styles.fullscreenContent : ''}`}>
          {
            shouldShowHeader && !isVideoDetailsPage && (
              <div className={`${styles.header} ${collapsed ? styles.retractHeader : ''}`}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <PreloadImage onClick={() => setCollapsed(prev => !prev)} src={isDarkMode ? collapsedIcon_dark : collapsedIcon} alt="collapsed" />
                  <span>{pageTitle}</span>
                </div>
              </div>
            )}
          <div className={`${styles.layout_content} ${isVideoDetailsPage ? styles.fullscreenLayoutContent : ''}`}>
            <TaskNotificationContext.Provider value={{
              uploadNotificationCount,
              downloadNotificationCount,
              addUploadNotification,
              addDownloadNotification,
              clearUploadNotifications,
              clearDownloadNotifications
            }}>
              <sideBarChangeContext.Provider value={{ setSidePanel }}>
                {children}
              </sideBarChangeContext.Provider>
            </TaskNotificationContext.Provider>
          </div>
        </Content>
      </Layout>
    </div >
  )
}

export default CustomLayout;