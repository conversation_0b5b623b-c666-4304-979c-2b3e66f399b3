import { Image } from "antd-mobile";
import { useHistory, useRouteMatch } from "react-router-dom";
import CameraStatusPage from "@/components/CameraStatusPage";

import styles from "./index.module.scss";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import tip from "@/Resources/icon/tip.png";
import plus from "@/Resources/icon/plus.png";
import cameraIcon from "@/Resources/icon/camera-icon.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import tipDark from "@/Resources/icon/tip-dark.png";
import plusDark from "@/Resources/icon/plus-dark.png";
import cameraIconDark from "@/Resources/icon/camera-icon-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { exitWebClient } from "@/api/cameraPlayer";

const NotAdded = () => {
  const history = useHistory();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  // 处理返回按钮点击
  const handleBackClick = () => {
    exitWebClient().catch((error) => {
      console.error("退出Web客户端失败:", error);
    });
  };

  const leftHeaderContent = (
    <Image
      className={styles.backIcon}
      src={isDarkMode ? arrowLeftDark : arrowLeft}
      onClick={handleBackClick}
    />
  );

  const rightHeaderContent = (
    <div className={styles.iconContainer}>
      <Image
        className={styles.infoIcon}
        src={isDarkMode ? tipDark : tip}
        onClick={() => history.push(`${path}/supportInformation`)}
      />
      <Image
        className={styles.addIcon}
        src={isDarkMode ? plusDark : plus}
        onClick={() => history.push(`${path}/addDevice`)}
      />
    </div>
  );

  const icon = (
    <Image
      src={isDarkMode ? cameraIconDark : cameraIcon}
      className={styles.iconPlaceholder}
    />
  );

  return (
    <CameraStatusPage
      className={styles.container}
      leftHeaderContent={leftHeaderContent}
      rightHeaderContent={rightHeaderContent}
      title="摄像机管理"
      icon={icon}
      mainMessage="暂未添加摄像头"
      subMessage="注意：请确保摄像机、智能存储和手机已连接到同一个路由器"
      // buttonText="存储管理"
      showButton={false}
      onButtonClick={() => history.push(`${path}/storageManagement`)}
    />
  );
};

export default NotAdded;
