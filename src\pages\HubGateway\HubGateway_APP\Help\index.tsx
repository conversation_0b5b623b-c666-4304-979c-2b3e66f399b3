
import { useHistory } from 'react-router-dom';
import styles from './index.module.scss';
import next_dark from "@/Resources/icon/next_dark.png";
import next from "@/Resources/icon/next.png";
import NavigatorBar from '@/components/NavBar';
import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '@/components/Image';


const Help = () => {
    const history = useHistory();

    const { isDarkMode } = useTheme();






    return (
        <div className={styles.container}>
            <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} title='使用帮助' onBack={history.goBack} />

            <div className={styles.help}>
                {/* 使用帮助 */}
                <div className={styles.helpSection}>
                    <div className={styles.TitleBox}>
                        <span className={styles.title}>中枢与网关</span>
                        <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                    </div>
                </div>
            </div>
            <div className={styles.help}>
                {/* 使用帮助 */}
                <div className={styles.helpSection}>
                    <div className={styles.TitleBox}>
                        <span className={styles.title}>自动化极客版</span>
                        <PreloadImage src={isDarkMode ? next_dark : next} style={{ height: '10px', width: '10px' }} alt="next" />
                    </div>
                </div>
            </div>
        </div>  
    );
};

export default Help;