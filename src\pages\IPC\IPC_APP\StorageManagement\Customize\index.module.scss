.container {
  background-color: var(--background-color);
  padding: 10px 0;
  position: relative;
  min-height: calc(100vh - 35px);

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .title {
    font-size: 28px;
    font-weight: 400;
    color: var(--title-color);
    padding: 10px 14px;
  }
  .arrows{
    width: 40px;
    height: 40px;
  }
}

.timeSlotList {
  padding: 0 16px;
  margin-top: 36px;
  padding-bottom: 80px;
}

.timeSlotItem {
  margin-bottom: 16px;
}

.timeSlotContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-top: 24px;
}

.timeRange {
  font-size: 27px;
  font-weight: 500;
  color: var(--text-color);
  letter-spacing: 2px;
}

.daysRow {
  display: flex;
  flex-wrap: wrap;
  width: 98%;
}

.shutdownTag {
  color: var(--subtitle-text-color);
  padding: 4px 6px 4px 0;
  border-radius: 4px;
  font-size: 12px;
  border-right: 1px solid var(--thinLine-background-color);
}

.dayTag {
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--subtitle-text-color);
}
.dayTag:nth-child(7) {
  padding-left: 0;
}

.divider {
  height: 1px;
  background-color: var(--thinLine-background-color);
  margin: 12px 0;
}

.switch {
  --checked-color: var(--primary-color);
  --height: 24px;
  --width: 44px;
  position: relative;
}

.switch::before {
  content: "";
  position: absolute;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  height: 32px;
  width: 1px;
  background-color: var(--thinLine-background-color);
}

.checkbox {
  --adm-color-primary: var(--primary-color);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px;
  margin-top: 80px;
}

.emptyIcon {
  margin-bottom: 16px;
}

.emptyText {
  font-size: 14px;
  color: var(--list-value-text-color);
  // opacity: 0.4;
}

.addButtonWrapper {
  position: fixed;
  right: 24px;
  bottom: 40px;
}

.addButton {
  width: 52px;
  height: 52px;
  padding: 0;
  border: none;
  cursor: pointer;
}

.deleteIcon {
  width: 24px;
  height: 24px;
}

.deleteContainer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: var(--background-color);
}

.deleteButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.deleteText {
  font-size: 12px;
  color: var(--text-color);
  margin-top: 4px;
}
