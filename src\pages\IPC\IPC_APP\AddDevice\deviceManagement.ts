import { useState } from "react";
import { useRequest } from "ahooks";
import {
  listAllCamera,
  BasicCameraInfo,
} from "@/api/ipc";
import { Toast } from "antd-mobile";
import { getDeviceType, DeviceType } from "@/utils/DeviceType";



// 设备数据类型定义
export type CameraDevice = {
  id: string;
  name: string;
  model: string;
  ip: string;
  mac: string;
  thumbnail: string;
  selected: boolean;
};

// 状态类型定义
export type FetchState = {
  status: "loading" | "error" | "empty" | "success";
  errorType?: "network" | "device";
  devices: CameraDevice[];
};

// 设备管理Hook
export const useDeviceManagement = () => {
  const [fetchState, setFetchState] = useState<FetchState>({
    status: "loading",
    devices: [],
  });

  // 处理摄像机图标路径的函数
  const processIconPath = (icon: string): string => {
    if (!icon) return '';

    const origin = window.location.origin;
    const pathname = window.location.pathname;
    // 根据设备类型决定路径处理方式
    const deviceType = getDeviceType();
    const needPath = deviceType === DeviceType.Desktop
      ? pathname.split('/').slice(2, 5).join('/') // PC端路径处理，海康路径中带有/E:/
      : pathname.split('/').slice(1, 4).join('/'); // APP端路径处理

    return `${origin}/${needPath}/${icon}`;
  };

  const { loading, run: refetchCameras } = useRequest(listAllCamera, {
    manual: false,
    onSuccess: (result) => {
      if(result && (result.code !== 0)){
          Toast.show(result?.result);
          return;
        }
      const cameraList = result.data.camera || [];

      if (cameraList.length === 0) {
        setFetchState({
          status: "empty",
          devices: [],
        });
      } else {
        const devices = cameraList.map((camera: BasicCameraInfo) => ({
          id: camera.did,
          name: camera.name,
          model: camera.model,
          ip: camera.ip,
          mac: camera.mac,
          thumbnail: processIconPath(camera.icon || ''),
          selected: false,
        }));

        setFetchState({
          status: "success",
          devices,
        });
      }
    },
    onError: (error) => {
      console.error("获取摄像头列表失败:", error);
      setFetchState({
        status: "error",
        errorType: "network",
        devices: [],
      });
    },
  });

  // 切换单个设备选中状态
  const toggleSelect = (id: string) => {
    setFetchState((prev) => ({
      ...prev,
      devices: prev.devices.map((device) =>
        device.id === id ? { ...device, selected: !device.selected } : device
      ),
    }));
  };

  // 全选/取消全选功能
  const toggleSelectAll = () => {
    const allSelected = fetchState.devices.every((device) => device.selected);
    setFetchState((prev) => ({
      ...prev,
      devices: prev.devices.map((device) => ({
        ...device,
        selected: !allSelected,
      })),
    }));
  };

  // 重试加载
  const handleRetry = () => {
    setFetchState({ status: "loading", devices: [] });
    // 重新发起请求
    refetchCameras();
  };

  // 获取已选设备数量
  const selectedCount = fetchState.devices.filter((d) => d.selected).length;

  // 获取已选设备列表
  const selectedDevices = fetchState.devices.filter(
    (device) => device.selected
  );

  return {
    fetchState,
    setFetchState,
    toggleSelect,
    toggleSelectAll,
    handleRetry,
    selectedCount,
    selectedDevices,
    loading,
  };
};
