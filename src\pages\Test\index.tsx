import { px2rem } from '@/components/CameraPlayer/components/TimeAxis/TimeAxis';
import styles from './index.module.scss';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

const TestMyComponents = () => {
  const totalHours = 24;
  const viewGrid = 60;
  const secondsPerPixel = 1; // 控制时间轴密度
  const blockWidth = totalHours * 2 * viewGrid * secondsPerPixel;

  const [isDragging, setIsDragging] = useState<boolean>(false); // 是否正在拖拽时间轴
  const [currentDate, setCurrentDate] = useState<Date>(new Date()); // 当前时间指针对应的日期对象

  const timeBlocks: Date[] = useMemo(() => {
    console.log(currentDate);
    return [
      new Date(currentDate.getTime() - 86400000),
      new Date(currentDate),
      new Date(currentDate.getTime() + 86400000)
    ];
  }, [currentDate]);

  const [currentX, setCurrentX] = useState<number>(-blockWidth); // 当前时间指针位置（像素）
  const [prevStart, setPrevStart] = useState<number>(0); // 鼠标按下时的位置

  const rowRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(blockWidth);
  const startScrollLeftRef = useRef(blockWidth);

  // 获取滚动边界
  const getScrollBounds = useCallback(() => {
    if (!rowRef.current) return { min: 0, max: 0 };
    const { scrollWidth, clientWidth } = rowRef.current;
    return {
      min: 0,
      max: Math.max(0, scrollWidth - clientWidth),
    };
  }, []);

  // 应用滚动限制
  const applyScrollBounds = useCallback((scroll: number) => {
    const bounds = getScrollBounds();
    return Math.max(bounds.min, Math.min(bounds.max, scroll));
  }, [getScrollBounds]);

  // 开始拖拽
  const startDrag = useCallback((clientX: number) => {
    if (!rowRef.current) return;

    setIsDragging(true);
    startXRef.current = clientX;
    startScrollLeftRef.current = rowRef.current.scrollLeft;
  }, []);

  // 拖拽处理
  const drag = useCallback((clientX: number) => {
    if (!rowRef.current || !isDragging) return;

    const deltaX = clientX - startXRef.current;
    const targetScroll = startScrollLeftRef.current - deltaX;
    const scrollLeft = applyScrollBounds(targetScroll);
    if (scrollLeft < blockWidth) {
      // 说明已经滚动到上一天
      setCurrentDate(p => new Date(p.getTime() - 86400000));
      startXRef.current += blockWidth;
      rowRef.current.scrollLeft = scrollLeft + blockWidth;
      return;
    }

    if (scrollLeft > blockWidth * 2) {
      // 说明已经滚动到下一天
      setCurrentDate(p => new Date(p.getTime() + 86400000));
      startXRef.current -= blockWidth;
      rowRef.current.scrollLeft = scrollLeft - blockWidth;
      return;
    }
    rowRef.current.scrollLeft = scrollLeft;
  }, [isDragging, applyScrollBounds, blockWidth]);

  // 事件监听器
  useEffect(() => {
    const row = rowRef.current;
    if (!row) return;

    // 鼠标事件处理
    const handleMouseMove = (e: MouseEvent) => drag(e.clientX);
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    // 触摸事件处理
    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 1) drag(e.touches[0].clientX);
    };
    const handleTouchEnd = () => {
      setIsDragging(false);
    };

    //当鼠标移出该行的时候调用handleMouseUp
    const handleMouseLeave = () => {
      if (isDragging) {
        handleMouseUp();
      }
    };

    // 绑定事件
    row.addEventListener("mousemove", handleMouseMove);
    row.addEventListener("mouseleave", handleMouseLeave);
    row.addEventListener("mouseup", handleMouseUp);
    row.addEventListener("touchmove", handleTouchMove);
    row.addEventListener("touchend", handleTouchEnd);

    return () => {
      row.removeEventListener("mousemove", handleMouseMove);
      row.removeEventListener("mouseup", handleMouseUp);
      row.removeEventListener("touchmove", handleTouchMove);
      row.removeEventListener("touchend", handleTouchEnd);
      row.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, [drag, rowRef, setIsDragging, isDragging]);

  useEffect(() => {
    if (rowRef && rowRef.current) {
      // 初始化滚动位置为中间区块的位置
      rowRef.current.scrollLeft = blockWidth;
    }
  }, [blockWidth])

  const genTimeMarkers = (baseDate: Date) => {
    // 创建基准时间零点（保留日期，时间归零）
    const base = new Date(baseDate);
    base.setHours(0, 0, 0, 0);

    return Array.from({ length: totalHours * 2 }, (_, i) => {
      const hours = Math.floor(i / 2);
      const date = new Date(baseDate);
      date.setHours(hours, (i % 2) * 30);
      return {
        time: date,
        formatted: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`,
        isMajor: date.getMinutes() === 0
      };
    });
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!isDragging) return;
    e.preventDefault();

    let deltaX: number = e.clientX - prevStart;

    if (Math.abs(currentX) < blockWidth) {
      // 当前移动的x值小于单个block长度时，相当于进入了前一天，此时控制block向左移动一天，并将currentX重置为中心板块
      deltaX = currentX - blockWidth;
    }

    if (Math.abs(currentX) > blockWidth * 2) {
      deltaX = currentX + blockWidth;
    }

    requestAnimationFrame(() => {
      setCurrentX(deltaX);
    })
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.preventDefault();
    setIsDragging(true);
    setPrevStart(e.clientX - currentX);
  }

  return (
    <div className={styles.container}>
      <h1>时间轴1</h1>
      <div className={styles.timeline_container}>
        <div className={styles.timeline_blockGrid} style={{ transform: `translateX(${px2rem(currentX)})`, width: px2rem(blockWidth * timeBlocks.length) }}
          onMouseMove={handleMouseMove} onMouseDown={handleMouseDown} onMouseLeave={() => setIsDragging(false)} onMouseUp={() => setIsDragging(false)}>
          {
            timeBlocks.map((block, index) => <div key={`${block.toDateString()}_${index}`} className={styles.timeline_block} style={{ left: px2rem(index * blockWidth) }}>
              {
                genTimeMarkers(block).map((marker, index) => (
                  <div key={index} className={`${styles.timeline_grid} ${marker.isMajor ? 'major' : 'minor'}`} style={{ left: px2rem(index * viewGrid * secondsPerPixel) }}>
                    <div className={styles.timeline_marker} />
                    <span>{`${marker.formatted}`}</span>
                    <span>{`${marker.time.getDate()}`}</span>
                  </div>
                ))
              }
            </div>)
          }
        </div>
        <div className={styles.timeline_indicator}></div>
      </div>

      <h1>时间轴2</h1>
      <div className={styles.timeline_container} style={{ overflow: 'hidden' }}>
        <div ref={rowRef} className={styles.timeline2_blockGrid} onMouseDown={(e) => startDrag(e.clientX)}>
          {
            timeBlocks.map((block, index) => <div key={`${block.toDateString()}_${index}`} className={styles.timeline2_block} style={{ width: px2rem(blockWidth) }}>
              {
                genTimeMarkers(block).map((marker, index) => (
                  <div key={`${block.getDate()}_${index}_2`} className={`${styles.timeline2_grid} ${marker.isMajor ? 'major' : 'minor'}`} style={{ width: px2rem(viewGrid * secondsPerPixel) }}>
                    <div className={styles.timeline_marker} />
                    <span>{`${marker.formatted}`}</span>
                    <span>{`${marker.time.getDate()}`}</span>
                  </div>
                ))
              }
            </div>)
          }
        </div>
        <div className={styles.timeline_indicator}></div>
      </div>
    </div>
  )
}

export default TestMyComponents;