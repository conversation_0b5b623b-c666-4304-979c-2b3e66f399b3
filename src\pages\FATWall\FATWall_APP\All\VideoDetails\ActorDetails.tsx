import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { Toast } from 'antd-mobile';
import NavigatorBar from '@/components/NavBar';
import FilterFilmCard from '@/components/FATWall_APP/FilterFilmCard';
import { searchLocalMedia } from '@/api/fatWall';
import styles from './ActorDetails.module.scss';
import no_avatar from '@/Resources/icon/no_avatar.png';

interface ActorDetailsProps {
  profile_path?: string;
  name?: string;
}

interface SearchResult {
  id: string;
  title: string;
  year: string;
  country: string;
  genres: string;
  posterUrl: string;
  type: string;
  media_id: number;
  classes: string;
  score: number;
}

const ActorDetails: React.FC = () => {
  const history = useHistory();
  const location = useLocation();

  // 从URL参数或state中获取演员信息
  const urlParams = new URLSearchParams(location.search);
  const urlProfilePath = urlParams.get('profile_path');
  const urlName = urlParams.get('name');

  const stateParams = location.state as ActorDetailsProps || {};

  const profile_path = urlProfilePath || stateParams.profile_path || '';
  const name = urlName || stateParams.name || '未知演员';

  const [relatedWorks, setRelatedWorks] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 搜索相关作品
  const { run: runSearchRelatedWorks } = useRequest(
    async (actorName: string) => {
      if (!actorName.trim()) return [];

      const response = await searchLocalMedia({
        keyword: actorName.trim(),
        filter: {
          offset: 0,
          limit: 50,
          classes: "" // 搜索所有类型
        }
      });

      if (response.code === 0 && response.data) {
        const { medias } = response.data;

        // 将接口返回的数据转换为SearchResult格式
        const transformedResults: SearchResult[] = medias.map(media => ({
          id: media.media_id.toString(),
          title: media.trans_name || media.origin_name || media.other_name,
          year: media.year.toString(),
          country: media.origin_place,
          genres: media.kind.join(' / '),
          posterUrl: media.poster.length > 0 ? media.poster[0] : '',
          type: media.classes === '电视剧' ? '电视剧' : '电影',
          media_id: media.media_id,
          classes: media.classes,
          score: media.score ? media.score : 0,
        }));

        return transformedResults;
      }
      return [];
    },
    {
      manual: true,
      onSuccess: (data) => {
        setRelatedWorks(data);
        setLoading(false);
      },
      onError: () => {
        Toast.show({
          content: '获取相关作品失败',
          position: 'bottom',
          duration: 1500,
        });
        setLoading(false);
      },
    }
  );

  // 组件加载时搜索相关作品
  useEffect(() => {
    if (name && name !== '未知演员') {
      setLoading(true);
      runSearchRelatedWorks(name);
    }
  }, [name, runSearchRelatedWorks]);

  // 处理作品卡片点击
  const handleWorkClick = useCallback((work: SearchResult) => {
    // 跳转到影片详情页
    const params = new URLSearchParams({
      classes: work.classes || '',
      media_id: work.media_id.toString(),
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
  }, [history]);

  // 背景图样式
  const backgroundStyle = useMemo(() => ({
    backgroundImage: profile_path ? `url(${profile_path})` : `url(${no_avatar})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  }), [profile_path]);

  return (
    <div className={styles.container}>
      <div className={styles.scrollContainer}>
        {/* 顶部背景和演员信息 */}
        <div className={styles.headerSection} style={backgroundStyle}>
          <div className={styles.overlay}>
            <NavigatorBar
              backIconTheme="dark"
              onBack={() => history.goBack()}
            />
          </div>
          <div className={styles.actorInfo}>
            <div className={styles.actorName}>{name}</div>
          </div>
        </div>

        {/* 相关作品 */}
        <div className={styles.worksSection}>
          <h2 className={styles.sectionTitle}>相关作品</h2>
          {loading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <div className={styles.loadingText}>加载中...</div>
            </div>
          ) : relatedWorks.length > 0 ? (
            <div className={styles.worksGrid}>
              {relatedWorks.map(work => (
                <FilterFilmCard
                  type='app'
                  key={work.id}
                  title={work.title}
                  subtitle={work.year}
                  score={work.score}
                  cover={work.posterUrl}
                  isLike={false}
                  isDrama={work.type === '电视剧'}
                  onCardClick={() => handleWorkClick(work)}
                />
              ))}
            </div>
          ) : (
            <div className={styles.emptyContainer}>
              <div className={styles.emptyText}>暂无相关作品</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActorDetails;