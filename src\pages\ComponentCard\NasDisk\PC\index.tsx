import { useCallback, useMemo, useState, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { useRequest } from "ahooks";
import { getBaiduUserInfo, getTaskInfo } from "@/api/nasDisk";
import { PreloadImage } from "@/components/Image";
import expandDark from "@/Resources/icon/expand-dark.png";
import expand from "@/Resources/icon/expand.png";
import transmittingDark from "@/Resources/icon/transmitting-dark.png";
import transmitting from "@/Resources/icon/transmitting.png";
import { useTheme } from "@/utils/themeDetector";

// 同步状态类型
type SyncStatus = "transferring" | "completed" | "none" | "error";

// 组件尺寸类型
type CardSize = "small" | "large";

// 定义共通的按钮配置
const DEFAULT_BUTTON_CONFIG = {
  text: "查看同步状态",
  action: "/taskManager"
};

const LOGIN_BUTTON_CONFIG = {
  text: "去登录",
  action: "/login"
};

// 定义动画样式
const fadeInAnimation = `
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const DashboardCardNasDiskPC = () => {
  const history = useHistory();
  const { isDarkMode } = useTheme();

  // 当前同步状态
  const [syncStatus, setSyncStatus] = useState<SyncStatus>("transferring");
  
  // 登录状态
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  // 登录状态加载中
  const [userLoading, setUserLoading] = useState<boolean>(true);

  // 控制任务状态的显示/隐藏
  const [showStatusLabel, setShowStatusLabel] = useState<boolean>(false);

  // 根据容器宽度判断当前卡片尺寸
  const [cardSize, setCardSize] = useState<CardSize>("small");

  // 获取百度网盘用户信息
  const { data: userInfo, loading: userInfoLoading } = useRequest(
    () => getBaiduUserInfo({ action: "uinfo" }),
    {
      onSuccess: (result) => {
        if (result && result.token) {
          // 有token表示已登录
          setIsLoggedIn(true);
        } else {
          // 未登录或token无效
          setIsLoggedIn(false);
        }
        setUserLoading(false);
      },
      onError: () => {
        // 请求失败，设置为未登录
        setIsLoggedIn(false);
        setUserLoading(false);
      },
    }
  );

  // 获取活动任务信息
  const { loading: activeTasksLoading } = useRequest(
    () => getTaskInfo({
      selector: [
        { key: "module", value: ["bpan"] },
        { key: "type", value: ["active"] }
      ]
    }),
    {
      ready: isLoggedIn, // 只在登录后请求
      refreshDeps: [isLoggedIn],
      onSuccess: (response) => {
        if (response.code === 0 && response.data?.info) {
          const activeTasks = response.data.info;
          // 根据活动任务状态更新UI
          if (activeTasks.length > 0) {
            // 查找是否有running或waiting状态的任务
            const runningTasks = activeTasks.filter(task => 
              task.status === "running" || task.status === "waiting");
            
            if (runningTasks.length > 0) {
              setSyncStatus("transferring");
            } else {
              // 只有paused或success_waiting状态的任务
              setSyncStatus("completed");
            }
          } else {
            // 没有活动任务，检查历史任务
            historyTasksRun();
          }
        }
      },
      onError: () => {
        // 任务接口错误，设置任务状态为错误，但不影响登录状态
        setSyncStatus("error");
      }
    }
  );

  // 获取历史任务信息
  const { runAsync: historyTasksRun, loading: historyTasksLoading } = useRequest(
    () => getTaskInfo({
      selector: [
        { key: "module", value: ["bpan"] },
        { key: "type", value: ["history"] }
      ]
    }),
    {
      manual: true, // 手动触发
      onSuccess: (response) => {
        if (response.code === 0 && response.data?.info) {
          const historyTasks = response.data.info;
          // 有历史任务则显示"已完成"，没有则显示"无任务"
          if (historyTasks.length > 0) {
            setSyncStatus("completed");
          } else {
            setSyncStatus("none");
          }
        } else {
          setSyncStatus("none");
        }
      },
      onError: () => {
        // 历史任务接口错误，设置任务状态为错误，但不影响登录状态
        setSyncStatus("error");
      }
    }
  );

  // 监听容器尺寸变化，适配两种固定尺寸的webview容器
  useEffect(() => {
    // 获取容器元素
    // const container = document.getElementById('nasDiskCardContainer');
    const container = document.getElementById("root");
    if (!container) return;

    const updateSize = () => {
      const containerWidth = container.offsetWidth;
      // 阈值设为500，小于500px时为small(300*300)，大于等于500px时为large(684*300)
      // 这里的阈值可以根据实际情况调整，确保能正确区分两种尺寸
      setCardSize(containerWidth >= 600 ? "large" : "small");
    };

    // 初始化尺寸
    updateSize();

    // 创建 ResizeObserver 监听尺寸变化
    const resizeObserver = new ResizeObserver(updateSize);
    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 状态内容配置
  const statusConfig = useMemo(
    () => ({
      transferring: {
        title: "传输中...",
        subtitle: "任务",
        color: "var(--text-color)",
      },
      completed: {
        title: "已完成",
        subtitle: "任务",
        color: "var(--text-color)",
      },
      none: {
        title: "无",
        subtitle: "任务",
        color: "var(--text-color)",
      },
      error: {
        title: "获取失败",
        subtitle: "任务",
        color: "var(--error-color, red)",
      },
    }),
    []
  );

  // 获取按钮配置（根据登录状态决定）
  const getButtonConfig = useCallback(() => {
    if (!isLoggedIn) {
      return LOGIN_BUTTON_CONFIG;
    }
    return DEFAULT_BUTTON_CONFIG;
  }, [isLoggedIn]);

  // 当前配置
  const currentConfig = statusConfig[syncStatus];
  const buttonConfig = getButtonConfig();
  
  // 判断是否正在加载
  const isTaskLoading = isLoggedIn && (activeTasksLoading || historyTasksLoading);

  // 按钮点击处理
  const handleButtonClick = useCallback(() => {
    history.push(`${'/baiduNetdisk_pc'}${buttonConfig.action}`);
  }, [history, buttonConfig.action]);

  // 获取状态标签样式
  const getStatusLabelStyle = useCallback(() => {
    const baseStyle = {
      position: "absolute" as const,
      padding: "16px",
      backgroundColor: "var(--primary-btn-background-color)",
      color: "var(--primary-color)",
      borderRadius: "16px",
      animation: "fadeIn 0.3s ease",
      zIndex: 10,
      whiteSpace: "nowrap" as const,
      top: "-15px",
      right: "20px",
      width: "200px",
    };

    return baseStyle;
  }, []);

  // 切换任务状态的显示/隐藏
  const toggleStatusLabel = useCallback(() => {
    setShowStatusLabel((prev) => !prev);
  }, []);

  // 根据卡片尺寸获取内容区域样式
  const getCardContentStyle = useCallback(() => {
    const baseStyle = {
      flex: 1,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      position: "relative" as const,
    };

    if (cardSize === "small") {
      return {
        ...baseStyle,
        flexDirection: "column" as const,
        padding: "10px 20px",
      };
    } else {
      return {
        ...baseStyle,
        flexDirection: "column" as const,
        alignItems: "center",
        justifyContent: "flex-start",
        padding: "10px 20px",
      };
    }
  }, [cardSize]);

  // 根据卡片尺寸获取传输图标样式
  const getTransferIconStyle = useCallback(() => {
    const baseStyle = {
      width: "82px",
      height: "54px",
    };

    if (cardSize === "small") {
      return {
        ...baseStyle,
        margin: "20px 0 10px",
      };
    } else {
      return {
        ...baseStyle,
        margin: "20px 0 10px",
      };
    }
  }, [cardSize]);

  // 根据卡片尺寸获取状态信息样式
  const getStatusInfoStyle = useCallback(() => {
    const baseStyle = {
      display: "flex",
      flexDirection: "column" as const,
    };

    if (cardSize === "small") {
      return {
        ...baseStyle,
        alignItems: "center",
        textAlign: "center" as const,
      };
    } else {
      return {
        ...baseStyle,
        alignItems: "center",
        textAlign: "center" as const,
      };
    }
  }, [cardSize]);

  // 获取卡片容器样式
  const getCardContainerStyle = useCallback(() => {
    const baseStyle = {
      backgroundColor: "var(--componentcard-bg-color)",
      borderRadius: "16px",
      overflow: "hidden",
      position: "relative" as const,
      display: "flex",
      flexDirection: "column" as const,
      transition: "all 0.3s ease",
      width: "100%",
      height: "100%",
      boxSizing: "border-box" as const,
    };

    if (cardSize === "small") {
      return {
        ...baseStyle,
        maxWidth: "300px",
        maxHeight: "300px",
      };
    } else {
      return {
        ...baseStyle,
        maxWidth: "684px",
        maxHeight: "300px",
      };
    }
  }, [cardSize]);

  // 底部样式
  const getBottomContainerStyle = useCallback(() => {
    const baseStyle: React.CSSProperties = {
      minHeight: "80px",
      boxSizing: "border-box",
    };
    if (cardSize === "small") {
      return {
        ...baseStyle,
        padding: "16px",
      };
    } else {
      return {
        ...baseStyle,
        padding: "16px 174px"
      };
    }
  }, [cardSize]);

  return (
    <>
      <style>{fadeInAnimation}</style>
      <div id="nasDiskCardContainer" style={getCardContainerStyle()}>
        {/* 卡片头部 */}
        <div
          style={{
            padding: "14px 16px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            minHeight: "72px",
            boxSizing: "border-box",
            backgroundColor: "var(--componentcard-title-bg-color)",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <div
              style={{
                width: "34px",
                height: "34px",
                backgroundColor: "rgba(118, 175, 255, 0.34)",
                borderRadius: "8px",
                marginRight: "12px",
                position: "relative",
                overflow: "hidden",
              }}
            >
              {userInfo?.avatar_url && (
                <PreloadImage
                  src={userInfo.avatar_url}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover"
                  }}
                />
              )}
            </div>
            <div>
              <div
                style={{
                  fontSize: "16px",
                  fontWeight: 500,
                  color: "var(--text-color)",
                }}
              >
                百度网盘
              </div>
              <div
                style={{
                  fontFamily: "MiSans",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "100%",
                  letterSpacing: "0px",
                  verticalAlign: "middle",
                  color: "var(--subtitle-text-color)",
                }}
              >
                {userLoading ? "加载中..." : (isLoggedIn ? "已登录" : "未登录")}
              </div>
            </div>
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
            }}
          >
            <PreloadImage
              src={isDarkMode ? expandDark : expand}
              style={{
                width: "26px",
                height: "26px",
                border: "none",
                background: "none",
                cursor: "pointer",
                transform: showStatusLabel ? "rotate(180deg)" : "none",
              }}
              onClick={toggleStatusLabel}
            />
          </div>
        </div>

        {/* 卡片内容 */}
        <div style={getCardContentStyle()}>
          {userInfoLoading ? (
            <div
              style={{
                fontFamily: "MiSans",
                fontWeight: 500,
                fontSize: "16px",
                color: "var(--text-color)",
              }}
            >
              加载中...
            </div>
          ) : !isLoggedIn ? (
            <div
              style={{
                fontFamily: "MiSans",
                fontWeight: 500,
                fontSize: "16px",
                color: "var(--text-color)",
              }}
            >
              未登录
            </div>
          ) : isTaskLoading ? (
            <div
              style={{
                fontFamily: "MiSans",
                fontWeight: 500,
                fontSize: "16px",
                color: "var(--text-color)",
              }}
            >
              加载中...
            </div>
          ) : (
            <>
              {/* 传输图标 */}
              <PreloadImage
                style={getTransferIconStyle()}
                src={isDarkMode ? transmittingDark : transmitting}
              />

              {/* 状态信息 */}
              {showStatusLabel && (
                <div style={getStatusLabelStyle()}>
                  <div
                    style={{
                      fontSize: "14px",
                      fontWeight: 500,
                    }}
                  >
                    {"任务状态"}
                  </div>
                </div>
              )}

              <div style={getStatusInfoStyle()}>
                <div
                  style={{
                    fontFamily: "MiSans",
                    fontWeight: 500,
                    fontSize: "16px",
                    lineHeight: "100%",
                    letterSpacing: "0%",
                    textAlign: cardSize === "small" ? "center" : "left",
                    verticalAlign: "middle",
                    color: currentConfig.color,
                  }}
                >
                  {currentConfig.title}
                </div>
                {currentConfig.subtitle && (
                  <div
                    style={{
                      fontFamily: "MiSans",
                      fontWeight: 400,
                      fontSize: "14px",
                      lineHeight: "140%",
                      letterSpacing: "0px",
                      textAlign: cardSize === "small" ? "center" : "left",
                      color: "var(--title-color)",
                      marginTop: "6px",
                    }}
                  >
                    {currentConfig.subtitle}
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* 卡片底部按钮 */}
        <div
          style={getBottomContainerStyle()}
        >
          <button
            style={{
              width: "100%",
              height: "48px",
              borderRadius: "8px",
              backgroundColor: "var(--componentcard-btn-bg-color)",
              border: "none",
              fontSize: "16px",
              fontWeight: 500,
              color: "var(--title-color)",
              cursor: "pointer",
              opacity: userLoading ? 0.7 : 1,
            }}
            onClick={handleButtonClick}
            disabled={userLoading}
          >
            {buttonConfig.text}
          </button>
        </div>
      </div>
    </>
  );
};

export default DashboardCardNasDiskPC;
