import { PreloadImage } from "@/components/Image";
import styles from "./index.module.scss";
import enter_right from '@/Resources/icon/enter_right.png';
import enter_right_dark from '@/Resources/icon/enter_right_dark.png';
import { useHistory, useRouteMatch } from "react-router-dom";
import FilmCard, { FilmCardList, IFilmCard } from "../../../../components/FATWall_APP/FilmCard";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { px2rem } from "@/utils/setRootFontSize";
import { RecentlyPlayedResponse, filmAndTvProps, getRecentlyAdd, getRecentlyWatched, getMediaFiles, MediaFileResponse } from "@/api/fatWall";
import FATErrorComponents from "../../FATWall_PC/Error";
import { useLibraryListApp } from "..";
import { formatTimeAgo } from "./RecentlyPlay";
import { playVideo } from "@/api/fatWallJSBridge";
import { Toast } from "antd-mobile";
import { useRequest, useUpdateEffect } from "ahooks";
import Pull2Refresh from "@/components/Pull2Refresh";
import placeholder‌_poster_big from '@/Resources/icon/placeholder‌_row_big.png';
import placeholder‌_poster from '@/Resources/icon/placeholder‌_row.png';
import { useTheme } from "@/utils/themeDetector";

interface IRouterCard {
  name: string;
  path: string;
}

const RouterCard: React.FC<IRouterCard & { children: React.ReactNode }> = ({ name, path, children }) => {
  // 路由卡片，点击右侧箭头可以跳转页面
  const history = useHistory();
  const { isDarkMode } = useTheme();

  return (
    <div className={styles.router_card_container}>
      <div className={styles.router_card_header} onClick={() => history.push(path)}>
        <span className={styles.router_card_header_span}>{name}</span>
        <span onClick={() => history.push(path)}><PreloadImage className={styles.router_card_header_img} src={isDarkMode ? enter_right_dark : enter_right} alt="go" /></span>
      </div>
      {children}
    </div>
  )
}

export const defaultPageParam = { offset: 0, limit: 18 }; // 默认的分页参数，每次加载20条数据

const Recently = () => {
  const { path } = useRouteMatch();
  const history = useHistory();
  const [recentlyPlayFilm, setRecentlyPlayFilm] = useState<RecentlyPlayedResponse>({ files: [], count: 0 });
  const [recentlyAddFilm, setRecentlyAddFilm] = useState<filmAndTvProps>({ medias: [], count: 0 });
  const [isError, setIsError] = useState<boolean>(false); // 是否出现错误信息
  const libs = useLibraryListApp().libs; // 获取媒体库列表
  const [addPageParams, setAddPageParams] = useState<{ offset: number; limit: number }>(defaultPageParam); // 最近添加的电影列表数据分页参数
  const [playPageParams, setPlayPageParams] = useState<{ offset: number; limit: number }>(defaultPageParam); // 最近播放的电影列表数据分页参数
  const [addHasMore, setAddHasMore] = useState<boolean>(true);
  const [playHasMore, setPlayHasMore] = useState<boolean>(true);
  const prevAddParams = useRef({ ...addPageParams });
  const prevPlayParams = useRef({ ...playPageParams });
  const [currentPlayItem, setCurrentPlayItem] = useState<any>(null); // 当前要播放的项目

  const containerRef = useRef<HTMLDivElement | null>(null); // 下拉刷新的容器引用

  // 获取媒体文件列表的API调用
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res: { code: number; data: MediaFileResponse }) => {
        if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
          console.log('APP端获取到完整剧集列表:', res.data.files);
          handleStartPlay(res.data.files, currentPlayItem);
        }
      },
      onError: (error) => {
        console.error('APP端获取媒体文件列表失败:', error);
        Toast.show({
          content: '获取剧集列表失败',
          position: 'bottom',
          duration: 1500,
        });
      },
    }
  );

  // 处理开始播放
  const handleStartPlay = useCallback((mediaFiles: any[], playItem: any) => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show({
        content: '暂无可播放的文件',
        position: 'bottom',
        duration: 1500,
      });
      return;
    }

    // 构建APP端videoList数组，参考VideoDetails中的handlePlay方法
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: playItem.media_id?.toString() || '0',
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      position: file.last_play_point || 0, // 断点信息
      isCompelete: file.seen, // 是否完整播放，转换为boolean
      audioIndex: file.audio_index || 0, // 音轨信息，默认为0
      subtitlePath: file.subtitle_path || '', // 字幕路径
      subtitleType: file.subtitle_type || 0, // 字幕类型，0表示内嵌字幕
      subtitleIndex: file.subtitle_index || 0, // 字幕索引，默认为0
    }));

    // 通过当前项的file_id在列表中找到索引位置
    let playIndex = 0;
    if (playItem.file_id) {
      const targetIndex = mediaFiles.findIndex(file => file.file_id === playItem.file_id);
      if (targetIndex !== -1) {
        playIndex = targetIndex;
        console.log(`APP端Recently播放：找到file_id(${playItem.file_id})对应的索引位置：${targetIndex}`);
      } else {
        console.log(`APP端Recently播放：未找到file_id(${playItem.file_id})对应的文件，将播放第一集`);
      }
    }

    // 调用APP端视频播放接口
    playVideo(videoList, playIndex, (res) => {
      // if (res.code === 0) {
      //   Toast.show({
      //     content: '开始播放',
      //     position: 'bottom',
      //     duration: 1500,
      //   });
      // } else {
      //   Toast.show({
      //     content: `播放失败: ${res.msg}`,
      //     position: 'bottom',
      //     duration: 1500,
      //   });
      // }
    }).catch((error) => {
      Toast.show({
        content: error.message || '播放失败',
        position: 'bottom',
        duration: 1500,
      });
    });

    // 清空当前播放项目状态
    setCurrentPlayItem(null);
  }, []);

  // 处理卡片点击播放
  const handlePlayClick = useCallback((item: any) => {
    console.log('APP端点击播放卡片:', item);

    if (!item.media_id) {
      Toast.show({
        content: '缺少媒体ID，无法播放',
        position: 'bottom',
        duration: 1500,
      });
      return;
    }

    window.onetrack?.('track', 'mediacenter_recentlyPlayFilm_click', { media_id: item.media_id });

    // 调用接口获取完整的剧集列表
    runGetMediaFiles({
      lib_id: 0, // 根据需求设置为0
      media_id: item.media_id
    });

    // 保存当前点击的项目信息，用于后续播放
    setCurrentPlayItem(item);
  }, [runGetMediaFiles]);

  // 处理最近添加卡片点击跳转，参考PC端实现
  const handleAddCardClick = useCallback((item: any) => {
    console.log('APP端最近添加卡片点击:', item);

    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });

    window.onetrack?.('track', 'mediacenter_recentlyAddFilm_click', { media_id: item.media_id });

    history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
  }, [history]);

  // 最近播放和添加的电影列表数据
  const watchedRun = useCallback(async (callback: (v: RecentlyPlayedResponse) => void, playPageParams) => {
    const data = await getRecentlyWatched(playPageParams).catch(e => {
      console.log('获取最近播放失败：', e);
      setIsError(true);
    });

    if (data && data.code === 0 && data.data) {
      // 最近播放的电影列表数据加载成功，更新状态
      callback(data.data);
      setIsError(false);
      // 判断是否还有更多数据可以加载
      if (data.data.count < playPageParams.limit) setPlayHasMore(false);
    } else {
      setIsError(true);
    }
  }, []);

  const addedRun = useCallback(async (callback: (v: filmAndTvProps) => void, addPageParams) => {
    const data = await getRecentlyAdd(addPageParams).catch(e => {
      console.log('获取最近添加失败：', e);
      setIsError(true);
    });

    if (data && data.code === 0 && data.data) {
      // 最近添加的电影列表数据加载成功，更新状态
      callback(data.data);
      setIsError(false);
      // 判断是否还有更多数据可以加载
      if (data.data.count < addPageParams.limit) setAddHasMore(false);
    } else {
      setIsError(true);
    }
  }, []);

  // 最近添加查询
  useEffect(() => {
    addedRun((data) => {
      setRecentlyAddFilm(p => {
        const newData = [...p.medias, ...data.medias];
        return { count: p.count + data.count, medias: newData };
      });
    }, addPageParams);
  }, [addPageParams, addedRun])

  // 最近播放查询
  useEffect(() => {
    watchedRun((data) => {
      setRecentlyPlayFilm(p => {
        const newData = [...p.files, ...data.files];
        return { count: p.count + data.count, files: newData };
      });
    }, playPageParams);
  }, [playPageParams, watchedRun])

  const recentlyPlayFilmList: IFilmCard[] = useMemo(() => {
    return recentlyPlayFilm.files.map((media, index) => {
      const time = formatTimeAgo(media.last_seen_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : placeholder‌_poster : placeholder‌_poster; // 数组0索引为封面图，1索引为海报
      return {
        ...media, // 传递完整的media对象，包含media_id、file_id等信息
        poster: poster,
        progress: media.last_seen_percent,
        title: media?.media_classes === "电视剧" ? `${media?.media_name}第${media?.episode}集` : media?.media_name || '',
        time: `${time}`,
        type: 'play' as const
      }
    })
  }, [recentlyPlayFilm])

  const recentlyAddFilmList: IFilmCard[] = useMemo(() => {
    return recentlyAddFilm.medias.map((media, index) => {
      const time = formatTimeAgo(media.create_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : placeholder‌_poster : placeholder‌_poster;
      return {
        ...media, // 传递完整的media对象，包含media_id、classes等信息
        poster: poster,
        title: media.trans_name,
        time: `${time}`,
        type: 'add' as const,
        year: media.year?.toString() || '未知', // 转换year为string类型
        category: media.classes || '未知分类' // 添加category字段
      }
    })
  }, [recentlyAddFilm])

  const refresh = useCallback(() => {
    // 重置分页参数，重新加载数据
    setAddHasMore(true);
    setPlayHasMore(true);

    // 重置分页参数，重新加载数据
    setAddPageParams(defaultPageParam);
    setPlayPageParams(defaultPageParam);

    if (prevAddParams.current) {
      const { limit, offset } = prevAddParams.current;
      if (limit === defaultPageParam.limit && offset === defaultPageParam.offset) {
        addedRun((v) => setRecentlyAddFilm(v), defaultPageParam);
      }
    }

    if (prevPlayParams.current) {
      const { limit, offset } = prevAddParams.current;
      if (limit === defaultPageParam.limit && offset === defaultPageParam.offset) {
        watchedRun((v) => setRecentlyPlayFilm(v), defaultPageParam);
      }
    }
    prevAddParams.current = { ...addPageParams };
    prevPlayParams.current = { ...playPageParams };
  }, [addPageParams, addedRun, playPageParams, watchedRun])

  // 播放器关闭后的数据刷新方法
  const refVideoDetail = useCallback(() => {
    // 播放器关闭后刷新Recently页面数据
    console.log('Recently页面播放器关闭后数据刷新');
    setTimeout(() => {
      refresh();
    }, 500);
  }, [refresh]);

  // 注册播放器关闭回调
  useEffect(() => {
    console.log('Recently页面注册播放器关闭回调');
    
    // 注册updateVideoCenter方法给HK
    window.hs_registerHandler?.('updateVideoCenter', refVideoDetail);
  }, [refVideoDetail]);

  // 监听分页配置
  useUpdateEffect(() => {
    if (addPageParams) {
      prevAddParams.current = { ...addPageParams };
    }
  }, [addPageParams])

  useUpdateEffect(() => {
    if (playPageParams) {
      prevPlayParams.current = { ...playPageParams };
    }
  }, [playPageParams])

  // 初始化进入影视墙时的最近页面曝光
  useEffect(() => {
    window.onetrack?.('track', 'mediacenter_recently_expose');
  }, [])

  return (

    <Pull2Refresh onRefresh={refresh} isTrigger={true} containerRef={containerRef} style={{ height: '100%' }}>
      <div ref={containerRef} className={styles.content}>
        <FATErrorComponents span={isError ? '获取失败' : '暂无内容'} canTry={isError} refresh={refresh} show={
          isError || libs.length === 0 || (recentlyPlayFilmList.length === 0 && recentlyAddFilmList.length === 0)
        } subSpan={(isError || libs.length === 0) ? undefined : (recentlyPlayFilmList.length === 0 && recentlyAddFilmList.length === 0) ? '近三个月无记录' : undefined}>
          {
            recentlyPlayFilmList.length > 0 && (
              <RouterCard name="最近播放" path={`${path}/recentlyPlay`}>
                <FilmCard
                  type="play"
                  poster={recentlyPlayFilmList[0].poster === placeholder‌_poster ? placeholder‌_poster_big : recentlyPlayFilmList[0].poster}
                  progress={recentlyPlayFilmList[0].progress}
                  title={recentlyPlayFilmList[0].title}
                  time={recentlyPlayFilmList[0].time}
                  options={{
                    style: { width: '100%', height: px2rem('210px') },
                    callback: () => handlePlayClick(recentlyPlayFilmList[0])
                  }}
                />
                {
                  recentlyPlayFilmList.length > 1 && <div className={styles.filmCardList}>
                    <FilmCardList
                      type="play"
                      list={recentlyPlayFilmList.slice(1).map(item => ({
                        ...item,
                        options: {
                          style: { width: '144px', height: '82px' },
                          callback: () => handlePlayClick(item)
                        }
                      }))}
                      hasMore={playHasMore && recentlyPlayFilmList.length >= playPageParams.limit}
                      setNewPage={setPlayPageParams}
                    />
                  </div>
                }
              </RouterCard>
            )
          }
          {
            recentlyAddFilmList.length > 0 && (
              <RouterCard name="最近添加" path={`${path}/recentlyAdd`}>
                <FilmCard
                  poster={recentlyAddFilmList[0].poster === placeholder‌_poster ? placeholder‌_poster_big : recentlyAddFilmList[0].poster} // 如果poster不存在或为空则显示此图片
                  title={recentlyAddFilmList[0].title}
                  time={recentlyAddFilmList[0].time}
                  options={{
                    style: { width: '100%', height: px2rem('210px') },
                    callback: () => handleAddCardClick(recentlyAddFilmList[0])
                  }}
                />
                {
                  recentlyAddFilmList.length > 1 && <div className={styles.filmCardList}>
                    <FilmCardList
                      type="add"
                      list={recentlyAddFilmList.slice(1).map(item => ({
                        ...item,
                        options: {
                          style: { width: '144px', height: '82px' },
                          callback: () => handleAddCardClick(item)
                        }
                      }))}
                      hasMore={addHasMore && recentlyAddFilmList.length >= addPageParams.limit}
                      setNewPage={setAddPageParams}
                    />
                  </div>
                }
              </RouterCard>
            )
          }
        </FATErrorComponents>
      </div>
    </Pull2Refresh>
  )
}

export default Recently;